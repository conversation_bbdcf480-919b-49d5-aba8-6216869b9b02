apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus-deployment
spec:
  replicas: 2
  template:
    metadata:
      labels:
        armsPilotCreateAppName: "prod-zeus"
    spec:
      containers:
        - name: main
          env:
            - name: OTEL_ENVIRONMENT
              value: "prod"
      volumes:
        - name: config-volume
          configMap:
            name: prod-zeus-config