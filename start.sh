#!/bin/bash

# 检查是否传入了 JAR 包路径参数
if [ -z "$1" ]; then
  echo "Usage: ./start.sh <path_to_jar>"
  exit 1
fi

JAR_PATH=$1

# 检查文件是否存在
if [ ! -f "$JAR_PATH" ]; then
  echo "Error: File not found at path: $JAR_PATH"
  exit 1
fi

# 检查是否设置了 OTEL_ENVIRONMENT 环境变量
if [ -n "$OTEL_ENVIRONMENT" ]; then
  echo "OTEL_ENVIRONMENT is set to: $OTEL_ENVIRONMENT"
  echo "Configuring OpenTelemetry..."

  # 根据环境变量设置不同的端点
  if [ "$OTEL_ENVIRONMENT" = "prod" ]; then
    echo "Using production OpenTelemetry endpoints"

    # 杭州-外网
    TRACES_ENDPOINT="http://tracing-analysis-dc-hz.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/traces"
    METRICS_ENDPOINT="http://tracing-analysis-dc-hz.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/metrics"
  else
    echo "Using non-production OpenTelemetry endpoints"

    # 杭州-外网
    TRACES_ENDPOINT="http://tracing-analysis-dc-hz.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/traces"
    METRICS_ENDPOINT="http://tracing-analysis-dc-hz.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/metrics"
  fi

  # 设置 OpenTelemetry 相关的 JVM 参数
  OTEL_JAVA_OPTS="-javaagent:./opentelemetry-javaagent.jar \
    -Dotel.resource.attributes=service.name=$OTEL_SERVICE_NAME,service.version=$OTEL_SERVICE_VERSION,deployment.environment=$OTEL_ENVIRONMENT \
    -Dotel.exporter.otlp.protocol=http/protobuf \
    -Dotel.exporter.otlp.traces.endpoint=$TRACES_ENDPOINT \
    -Dotel.exporter.otlp.metrics.endpoint=$METRICS_ENDPOINT \
    -Dotel.logs.exporter=none"
else
  echo "OTEL_ENVIRONMENT is not set. OpenTelemetry will not be configured."
  OTEL_JAVA_OPTS=""
fi

echo "Starting application with OTEL_JAVA_OPTS: $OTEL_JAVA_OPTS"

exec java $OTEL_JAVA_OPTS \
  --add-opens java.base/java.lang=ALL-UNNAMED -Xshare:off\
  -Duser.timezone=UTC \
  -jar "$JAR_PATH" \
  --spring.config.location=file:/app/application.properties
