FROM gradle:8.10.2-jdk23 AS builder
WORKDIR /app
COPY . .

# TODO: 构建环境需要安装 ffmpeg，然后使用 ./gradlew check 跑单元测试
RUN ./gradlew spotlessCheck && ./gradlew :modules:openapi:build

FROM openjdk:23-jdk-slim AS runner
WORKDIR /app
EXPOSE 8080

# 安装 ffmpeg
RUN apt-get update && \
    apt-get install -y ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/modules/openapi/build/libs/*.jar ./

ENTRYPOINT ["java", "-jar", "./openapi-0.0.1-SNAPSHOT.jar", "--spring.config.location=file:/app/application.properties"]
