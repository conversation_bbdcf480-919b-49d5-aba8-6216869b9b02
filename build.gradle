plugins {
    id 'java'
    id "com.diffplug.spotless" version "7.0.0.BETA4" apply false
    id 'org.springframework.boot' version '3.3.4' apply false
    id 'io.spring.dependency-management' version '1.1.6' apply false
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'com.diffplug.spotless'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(23)
        }
    }

    spotless {
       java {
           targetExclude 'build/**'
           palantirJavaFormat()
       }
    }

    repositories {
        mavenCentral()
    }
    
    // Global exclusion for all subprojects
    configurations {
        all {
            exclude group: 'ch.qos.logback'
            exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
            exclude group: 'log4j', module: 'log4j'
            exclude group: 'commons-logging', module: 'commons-logging'
        }
    }

    dependencies {
        implementation 'org.springframework.boot:spring-boot-starter-web'
        implementation 'org.springframework.retry:spring-retry:2.0.10'

        compileOnly 'org.projectlombok:lombok:1.18.30'
        annotationProcessor 'org.projectlombok:lombok:1.18.30'
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    }
}

allprojects {
    group = 'co.sandai'
    version = '0.0.1-SNAPSHOT'
}