FROM gradle:8.10.2-jdk23 AS builder
WORKDIR /app
COPY . .

# TODO: 构建环境需要安装 ffmpeg，然后使用 ./gradlew check 跑单元测试
RUN ./gradlew spotlessCheck --info && ./gradlew :modules:api:build

FROM openjdk:23-jdk-slim AS runner
WORKDIR /app
EXPOSE 8080

# 安装 ffmpeg
RUN apt-get update && \
    apt-get install -y ffmpeg curl vim wget && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 下载 OpenTelemetry Java 代理
RUN wget -O opentelemetry-javaagent.jar https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar

COPY --from=builder /app/modules/api/build/libs/*.jar ./

# 设置环境变量
ENV OTEL_SERVICE_NAME=zeus \
    OTEL_SERVICE_VERSION=0.0.1 \
    HEAPDUMP_PATH=/app/heapdumps

# 创建heapdump目录和sys目录
RUN mkdir -p /app/heapdumps /app/sys

# 复制启动脚本和OOM处理脚本
COPY start.sh /app/
COPY sys/handle_oom.sh sys/upload_heapdump.sh /app/sys/

# 设置脚本执行权限
RUN chmod +x /app/start.sh /app/sys/handle_oom.sh /app/sys/upload_heapdump.sh

ENTRYPOINT ["/app/start.sh", "./api-0.0.1-SNAPSHOT.jar"]
