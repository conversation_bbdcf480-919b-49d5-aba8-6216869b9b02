# SandAI Product Rest API

接口设计规范参考：[API 接口设计文档](https://j0yswlgboxz.feishu.cn/wiki/Au5TwuWbTibpXtkSBzicgL1wnxe?from=from_copylink)

## 目录结构

```
src/main/java/co/sandai/zeus
├── ZeusApplication.java
├── api # API 层
│   ├── home
│   │   └── HomeController.java
│   ├── task
│   │   └── TaskController.java
│   └── user
│       ├── ProfileController.java
│       └── view
│           ├── UserProfile.java
│           └── UserProfileUpdateRequest.java
├── common
│    ├── ServiceExceptionHandler.java
│    ├── constant
│    │   └── ErrorCode.java
│    ├── exception
│    │   └── ZeusServiceException.java
│    └── vo
│        ├── ErrorResponse.java
│        └── ListResponse.java
├── domain # 业务领域层
│    ├── task
│    │    ├── dao
│    │    └── service
│    └── user
│        ├── config # 配置
│        │   ├── ZeusSecurityConfig.java
│        │   ├── ZeusSessionFilter.java
│        │   └── ZeusUserDetailsService.java
│        ├── dao # 数据访问
│        │   ├── Session.java
│        │   ├── SessionMapper.java
│        │   ├── User.java
│        │   └── UserMapper.java
│        └── service # 业务逻辑
│            ├── SessionService.java
│            ├── UserService.java
│            └── UserSource.java
└── infra
    └── SqlCostTimeInterceptor.java
```

## 代码格式化

- 使用 [Spotless + palantir-java-format](https://github.com/diffplug/spotless/tree/main/plugin-gradle#palantir-java-format) 对代码进行格式化。
- 执行 `./gradlew spotlessApply` 格式化所有文件（推荐安装 IDEA 插件 Spotless Gradle）。
- GitHub Actions 在构建镜像时会执行 `./gradlew check` 来校验代码格式。

## 配置文件

- 开发阶段，从 `resources/application-dev-template.properties` 复制出 `resources/application-dev.properties` 文件。
- 不要提交 `resources/application-dev.properties` 到 Git 仓库。
- 不要将敏感的配置信息设置到 `resources/application.properties` 中。

## 错误处理

1. 在 ErrorCode 中定义错误码
2. 在 Controller 或者 Service 中 `throw new ZeusServiceException(ErrorCode.SOME_ERROR_CODE)`
3. 默认 http status 是 400，也可以通过设置 `ZeusServiceException` 的 httpStatus 属性来设置 HTTP Status

## 开始开发

执行以下命令启动 API Service

```bash
./gradlew :modules:api:bootRun --args='--spring.profiles.active=dev -Duser.timezone=UTC'
```