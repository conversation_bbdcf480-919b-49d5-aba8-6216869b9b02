#!/bin/bash

# 上传堆转储文件到阿里云 OSS 的 Shell 脚本
# 使用 curl 实现，不依赖 Python

set -e

# 日志函数
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

error() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" >&2
}

# 检查参数
if [ $# -lt 1 ]; then
  error "用法: $0 <堆转储文件路径>"
  exit 1
fi

HEAP_DUMP_FILE="$1"

# 检查文件是否存在
if [ ! -f "$HEAP_DUMP_FILE" ]; then
  error "堆转储文件不存在: $HEAP_DUMP_FILE"
  exit 1
fi

# 检查环境变量
if [ -z "$OSS_ENDPOINT" ] || [ -z "$OSS_BUCKET" ] || [ -z "$OSS_ACCESS_KEY_ID" ] || [ -z "$OSS_ACCESS_KEY_SECRET" ]; then
  error "缺少必要的环境变量: OSS_ENDPOINT, OSS_BUCKET, OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET"
  exit 1
fi

# 获取文件名和主机名
FILENAME=$(basename "$HEAP_DUMP_FILE")
HOSTNAME=$(hostname)
TIMESTAMP=$(date '+%Y%m%d%H%M%S')

# 构建目标对象名
OBJECT_NAME="dump/${HOSTNAME}_${TIMESTAMP}_${FILENAME}"

# 获取文件大小
FILE_SIZE=$(stat -c%s "$HEAP_DUMP_FILE" 2>/dev/null || stat -f%z "$HEAP_DUMP_FILE")

log "开始上传堆转储文件 $HEAP_DUMP_FILE (大小: $FILE_SIZE 字节) 到 OSS"
log "目标: $OSS_BUCKET/$OBJECT_NAME"

# 计算 GMT 时间
GMT_DATE=$(date -u '+%a, %d %b %Y %H:%M:%S GMT')

# 构建 Content-Type
CONTENT_TYPE="application/octet-stream"

# 构建 StringToSign
STRING_TO_SIGN="PUT\n\n${CONTENT_TYPE}\n${GMT_DATE}\n/${OSS_BUCKET}/${OBJECT_NAME}"

# 计算签名
SIGNATURE=$(echo -en "$STRING_TO_SIGN" | openssl sha1 -hmac "$OSS_ACCESS_KEY_SECRET" -binary | base64)

# 执行上传
UPLOAD_START_TIME=$(date +%s)
log "上传开始..."

# 使用 curl 上传文件
# 处理 OSS_ENDPOINT 中可能包含的 http:// 或 https:// 前缀
ENDPOINT_CLEAN=$(echo "$OSS_ENDPOINT" | sed 's|^https://||' | sed 's|^http://||')

UPLOAD_RESULT=$(curl -s -X PUT -T "$HEAP_DUMP_FILE" \
  -H "Host: $OSS_BUCKET.$ENDPOINT_CLEAN" \
  -H "Date: $GMT_DATE" \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "Authorization: OSS $OSS_ACCESS_KEY_ID:$SIGNATURE" \
  "https://$OSS_BUCKET.$ENDPOINT_CLEAN/$OBJECT_NAME" \
  -w "%{http_code}" \
  -o /dev/null -v)

UPLOAD_END_TIME=$(date +%s)
UPLOAD_DURATION=$((UPLOAD_END_TIME - UPLOAD_START_TIME))

# 检查上传结果
if [ "$UPLOAD_RESULT" == "200" ]; then
  log "上传成功! 耗时: $UPLOAD_DURATION 秒"
  log "文件已上传至: https://$OSS_BUCKET.$OSS_ENDPOINT/$OBJECT_NAME"
  exit 0
else
  error "上传失败! HTTP 状态码: $UPLOAD_RESULT"
  exit 1
fi
