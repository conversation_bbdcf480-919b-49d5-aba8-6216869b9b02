#!/bin/bash

# 此脚本用于处理OOM事件，等待堆转储文件生成完成后再上传

# 获取进程ID
PID=$1
if [ -z "$PID" ]; then
  echo "错误：未提供进程ID" >&2
  exit 1
fi

# 堆转储目录
DUMP_DIR="${HEAPDUMP_PATH:-/app/heapdumps}"
echo "$(date '+%Y-%m-%d %H:%M:%S') - OOM事件发生，进程ID: $PID，开始处理..." >&2

# 等待堆转储文件生成
echo "$(date '+%Y-%m-%d %H:%M:%S') - 等待堆转储文件生成..." >&2

# 查找最新的堆转储文件
find_latest_dump() {
  find "$DUMP_DIR" -name "*.hprof" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-
}

# 初始等待，给JVM一些时间开始生成堆转储
sleep 5

# 查找最新的堆转储文件
LATEST_DUMP=$(find_latest_dump)
if [ -z "$LATEST_DUMP" ]; then
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 未找到堆转储文件，等待生成..." >&2
  
  # 等待最多5分钟
  for i in {1..30}; do
    sleep 10
    LATEST_DUMP=$(find_latest_dump)
    if [ -n "$LATEST_DUMP" ]; then
      break
    fi
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 继续等待堆转储文件生成... ($i/30)" >&2
  done
fi

if [ -z "$LATEST_DUMP" ]; then
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 超时：未找到堆转储文件" >&2
  exit 1
fi

echo "$(date '+%Y-%m-%d %H:%M:%S') - 找到堆转储文件: $LATEST_DUMP" >&2

# 检查文件是否仍在写入
PREV_SIZE=0
CURR_SIZE=$(stat -c%s "$LATEST_DUMP" 2>/dev/null || stat -f%z "$LATEST_DUMP")
echo "$(date '+%Y-%m-%d %H:%M:%S') - 检查文件是否完成写入，当前大小: $CURR_SIZE" >&2

# 等待文件大小不再变化，表示写入完成
while [ "$PREV_SIZE" != "$CURR_SIZE" ]; do
  PREV_SIZE=$CURR_SIZE
  sleep 10
  CURR_SIZE=$(stat -c%s "$LATEST_DUMP" 2>/dev/null || stat -f%z "$LATEST_DUMP")
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 文件仍在写入，当前大小: $CURR_SIZE" >&2
done

echo "$(date '+%Y-%m-%d %H:%M:%S') - 堆转储文件写入完成，开始上传..." >&2

# 调用Python脚本上传文件
/bin/bash /app/sys/upload_heapdump.sh "$LATEST_DUMP"
UPLOAD_STATUS=$?

if [ $UPLOAD_STATUS -eq 0 ]; then
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 堆转储文件上传成功" >&2
else
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 堆转储文件上传失败，状态码: $UPLOAD_STATUS" >&2
fi

exit $UPLOAD_STATUS
