package co.sandai.zeus.domain.payment;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.payment.dao.OrderDO;
import co.sandai.zeus.domain.payment.dao.OrderDao;
import co.sandai.zeus.domain.payment.dao.OrderMapper;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.model.PriceModel;
import co.sandai.zeus.domain.platform.organization.OrganizationService;
import co.sandai.zeus.domain.platform.organization.dao.Organization;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.stripe.StripeApiClient;
import com.stripe.exception.StripeException;
import com.stripe.model.Invoice;
import com.stripe.model.InvoiceLineItem;
import com.stripe.model.LineItem;
import com.stripe.model.Price;
import com.stripe.model.checkout.Session;
import jakarta.annotation.Resource;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PaymentService {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderDao orderDao;

    @Resource
    private StripeApiClient stripeSessionApiClient;

    @Resource
    private IDGenerator idGenerator;

    @Resource
    private PlanConfigService planConfigService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private UserService userService;

    @Autowired
    private OrganizationService organizationService;

    // query order by session id
    public OrderDO queryOrderBySessionId(String sessionId) {
        return orderMapper.selectBySessionId(sessionId);
    }

    /**
     * checkout session 支付成功后，同步订单
     * 可重入方法
     */
    public OrderDO syncOrderFromCheckoutSession(String checkoutSessionId, boolean isRenew, boolean isUpgrade)
            throws StripeException, InterruptedException {
        int retryTimes = 6;
        String invoiceId = null;
        Session session = null;
        long timeSleep = 200L;
        while (StringUtils.isBlank(invoiceId) && retryTimes-- > 0) {
            session = stripeSessionApiClient.retriveCheckoutSession(checkoutSessionId);
            if (session.getPaymentStatus().equals("unpaid")) {
                LogUtil.warnf(log, "session is unpaid, sessionId={0}", checkoutSessionId);
                return null;
            }

            invoiceId = session.getInvoice();
            if (StringUtils.isBlank(invoiceId)) {
                Thread.sleep(timeSleep);
                // 每次重试时，增加等待时间； 最后一次等待时间是 2^(retryTimes-2) * 200ms； 当retryTimes=6时，总等待时间差不多是6s
                timeSleep *= 2;
            }
        }

        if (StringUtils.isBlank(invoiceId)) {
            LogUtil.warnf(
                    log,
                    "sessionId: {0} has no invoiceId yet. This can occur because invoices for delayed notification payment methods might take longer to generate. Waiting for invoice.paid event...",
                    checkoutSessionId);
            return null;
        }

        // 支付了，处理落库. 此处假设只有一个line item. 如果以后可以同时支付多个，需要改造.
        // 这个line item需要和invoice的line item区分，invoice的line item可能包含proration的
        LineItem lineItem = session.getLineItems().getData().getFirst();
        Price price = lineItem.getPrice();

        OrderDO orderDO = new OrderDO();
        orderDO.setId(idGenerator.getNextId());
        orderDO.setOuterOrderId(invoiceId);
        // 这里不通过outerPlatform区分了，orderId本身前缀可以看出来
        orderDO.setOuterPlatform(OrderDO.OutPlatformEnum.STRIPE.name());
        OrderDO.FromEnum from = decideFromEnum(isRenew, isUpgrade, price);
        orderDO.setFrom(from.name());
        orderDO.setSessionId(session.getId());
        orderDO.setAmount(price.getUnitAmount());
        orderDO.setCurrency(price.getCurrency());

        // 填充userId
        String outCustomerId = session.getCustomer();
        User user = userService.getUserByOutCustomerId(outCustomerId);
        orderDO.setUserId(user.getId());

        log.info("order {} from {}, userId: {}", orderDO.getId(), from, user.getId());
        if (from == OrderDO.FromEnum.PURCHASE_PLATFORM_CREDIT) {
            Organization organization = organizationService.getOrCreatePersonalOrganization(user.getId());
            orderDO.setOrgId(organization.getId());
            log.info("order {} from {}, orgId: {}", orderDO.getId(), from, organization.getId());
        }

        orderDO.setStatus(OrderDO.OrderStatusEnum.PAID.name());
        boolean b = orderDao.insertWithDuplicateCheck(orderDO);
        if (b) {
            log.info("order {} created, from: {}, userId: {}", orderDO.getId(), from, user.getId());
            // 首次更新db，发送支付成功事件
            PaySuccessEvent paySuccessEvent = new PaySuccessEvent(this);
            paySuccessEvent.setPriceId(price.getId());
            paySuccessEvent.setFrom(from);
            paySuccessEvent.setUserId(orderDO.getUserId());
            paySuccessEvent.setOrgId(orderDO.getOrgId());
            paySuccessEvent.setPayTime(TimeUtil.utcTime());
            applicationEventPublisher.publishEvent(paySuccessEvent);
        }
        return orderDO;
    }

    /**
     * credit package 支付成功后，同步订单
     * 可重入方法
     *
     */
    public void syncOneTimeOrder(Invoice invoice, boolean needSendEvent) {
        if (invoice.getAmountRemaining() > 0) {
            LogUtil.errorf(log, "invoice not payed, invoiceId={0}", invoice.getId());
            return;
        }

        OrderDO orderDO = new OrderDO();
        orderDO.setId(idGenerator.getNextId());
        orderDO.setOuterOrderId(invoice.getId());
        orderDO.setOuterPlatform(OrderDO.OutPlatformEnum.STRIPE.name());

        // line item的type说明： https://docs.stripe.com/api/invoice-line-item/object#invoice_line_item_object-type
        Price price = invoice.getLines().getData().stream()
                .filter(x ->
                        // 不是subscription的，也不是proration的
                        "invoiceitem".equalsIgnoreCase(x.getType()) && x.getAmount() > 0L)
                .map(InvoiceLineItem::getPrice)
                .findFirst()
                .orElse(null);

        if (Objects.isNull(price)) {
            throw ZeusServiceException.internalError(
                    String.format("parse invoice %s error, can not found price", invoice.getId()));
        }

        OrderDO.FromEnum from = decideFromEnum(false, false, price);
        orderDO.setFrom(from.name());
        orderDO.setAmount(invoice.getAmountPaid());
        orderDO.setCurrency(invoice.getCurrency());

        // 填充userId
        String outCustomerId = invoice.getCustomer();
        User user = userService.getUserByOutCustomerId(outCustomerId);
        orderDO.setUserId(user.getId());
        orderDO.setStatus(OrderDO.OrderStatusEnum.PAID.name());

        if (from == OrderDO.FromEnum.PURCHASE_PLATFORM_CREDIT) {
            Organization org = organizationService.getOrCreatePersonalOrganization(user.getId());
            orderDO.setOrgId(org.getId());
            log.info(
                    "order from {}, orderId: {}, orgId: {}, userId: {}",
                    from,
                    orderDO.getId(),
                    org.getId(),
                    user.getId());
        }

        boolean b = orderDao.insertWithDuplicateCheck(orderDO);
        log.info(
                "create order {}, from {}, invoice id: {}, userId: {}",
                orderDO.getId(),
                from,
                invoice.getId(),
                user.getId());

        if (b && needSendEvent) {
            // 首次更新db，发送支付成功事件
            PaySuccessEvent paySuccessEvent = new PaySuccessEvent(this);
            paySuccessEvent.setPriceId(price.getId());
            paySuccessEvent.setFrom(from);
            paySuccessEvent.setUserId(orderDO.getUserId());
            paySuccessEvent.setOrgId(orderDO.getOrgId());
            paySuccessEvent.setPayTime(TimeUtil.utcTime());
            applicationEventPublisher.publishEvent(paySuccessEvent);
        }
    }

    private OrderDO.FromEnum decideFromEnum(boolean isRenew, boolean isUpgrade, Price price) {
        if (isRenew) {
            return OrderDO.FromEnum.RENEW;
        } else if (isUpgrade) {
            return OrderDO.FromEnum.UPGRADE;
        } else {
            PriceModel priceModel = planConfigService.getPriceModelByPriceId(price.getId());
            PriceModel.ProductTypeEnum productType = priceModel.getProductType();
            if (PriceModel.ProductTypeEnum.CREDIT == productType) {
                return OrderDO.FromEnum.PURCHASE_CREDIT;
            }
            if (PriceModel.ProductTypeEnum.PLATFORM_CREDIT == productType) {
                return OrderDO.FromEnum.PURCHASE_PLATFORM_CREDIT;
            }
            return OrderDO.FromEnum.SUBSCRIBE;
        }
    }

    /**
     * 落单。 仅用于订阅相关的。 一次性支付的不支持
     * @return 检查结果，是否支付成功
     */
    public boolean syncSubscriptionOrder(Invoice invoice, OrderDO.FromEnum from, boolean needSendEvent) {
        if (invoice.getAmountRemaining() != 0) {
            LogUtil.warnf(log, "invoice not payed, invoiceId={0}", invoice.getId());
            return false;
        }
        InvoiceLineItem subscription = invoice.getLines().getData().stream()
                // 订阅等级升级的情况下，两条都是invoiceitem类型的. 所以根据type过滤不准确。这里通过amount区分出更新后的那条invoice记录
                .filter(x -> x.getAmount() > 0L)
                .findAny()
                .orElse(null);
        String invoiceId = invoice.getId();
        if (subscription == null) {
            LogUtil.errorf(log, "invoiceId: {0} has no subscription line item", invoiceId);
            return false;
        }
        Long startTime = subscription.getPeriod().getStart();
        LogUtil.debugf(
                log,
                "invoiceId: {0}, startTime: {1}, invoice create time={2}",
                invoiceId,
                TimeUtil.formatSeconds(startTime),
                TimeUtil.formatSeconds(invoice.getCreated()));
        if (!TimeUtil.from(startTime).isAfter(TimeUtil.utcTime().minusDays(3))) {
            // 校验是否是最近3天内开始的订阅周期的支付
            LogUtil.errorf(log, "invoiceId: {0} is not a new payment", invoiceId);
            return false;
        }

        String priceId = subscription.getPrice().getId();

        OrderDO orderDO = new OrderDO();
        orderDO.setId(idGenerator.getNextId());
        orderDO.setOuterOrderId(invoiceId);
        orderDO.setOuterPlatform(OrderDO.OutPlatformEnum.STRIPE.name());
        orderDO.setFrom(from.name());
        orderDO.setAmount(invoice.getAmountDue());
        orderDO.setCurrency(invoice.getCurrency());

        // 填充userId
        String outCustomerId = invoice.getCustomer();
        User user = userService.getUserByOutCustomerId(outCustomerId);
        orderDO.setUserId(user.getId());
        orderDO.setStatus(OrderDO.OrderStatusEnum.PAID.name());

        boolean b = orderDao.insertWithDuplicateCheck(orderDO);
        if (b && needSendEvent) {
            // 首次更新db，发送支付成功事件
            PaySuccessEvent paySuccessEvent = new PaySuccessEvent(this);

            paySuccessEvent.setPriceId(priceId);
            paySuccessEvent.setFrom(from);
            paySuccessEvent.setUserId(orderDO.getUserId());
            paySuccessEvent.setPayTime(TimeUtil.utcTime());
            paySuccessEvent.setRequestId(invoiceId);
            applicationEventPublisher.publishEvent(paySuccessEvent);
        }

        return true;
    }
}
