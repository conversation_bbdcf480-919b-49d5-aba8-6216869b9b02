package co.sandai.zeus.domain.plan.model;

import com.alibaba.fastjson.JSON;
import java.util.Currency;
import lombok.Data;

@Data
public class PriceModel {

    private String id;
    /**
     * planCode或者creditPackageCode
     */
    private String outCode;

    private ProductTypeEnum productType = ProductTypeEnum.PLAN;
    private Currency currency;
    /**
     * 仅展示用. 单位元（如美元等）
     */
    private String price;

    /**
     * 仅展示用. 单位元（如美元等）
     */
    private String originalPrice;

    private String outPriceId;
    private PriceStatusEnum status;

    public enum PriceStatusEnum {
        ACTIVE,
        DEPRECATED,
        OFFLINE,
        ;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public enum ProductTypeEnum {
        PLAN,
        CREDIT,
        PLATFORM_CREDIT;
    }
}
