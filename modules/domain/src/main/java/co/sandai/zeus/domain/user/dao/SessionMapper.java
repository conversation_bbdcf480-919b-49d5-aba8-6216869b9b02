package co.sandai.zeus.domain.user.dao;

import org.apache.ibatis.annotations.*;

@Mapper
@Deprecated
public interface SessionMapper {

    @Select("select * from session where token=#{token}")
    Session getSessionByToken(String token);

    @Insert(
            " insert into session (user_id, token, expire_in) values (#{session.userId}, #{session.token}, #{session.expireIn})")
    void insertSession(@Param("session") Session user);
}
