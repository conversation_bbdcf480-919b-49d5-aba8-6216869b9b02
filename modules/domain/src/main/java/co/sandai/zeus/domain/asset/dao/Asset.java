package co.sandai.zeus.domain.asset.dao;

import co.sandai.zeus.infra.oss.OSSProviderSource;
import co.sandai.zeus.infra.oss.OssClient;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Setter
@Getter
public class Asset {
    public long id;
    public long userId;

    @Builder.Default
    public long orgId = 0;

    public String filename;

    public long taskId;
    public String ossPath;
    public long posterAssetId;
    public long index;
    public int width;
    public int height;
    private float duration;
    public boolean isModerationPassed;

    /**
     * 内容审核的置信度分值
     */
    public Float moderationConfidence;

    public Long voiceId;

    public String ossSource;
    public String ossBucket;
    public AssetMediaType mediaType;
    public AssetSource source;
    public AssetVideoModerationStatus videoModerationStatus;
    public String videoModerationSeed;
    public String videoModerationTaskId;
    public java.sql.Timestamp createTime;
    public java.sql.Timestamp updateTime;

    private Long taskChunkId;
    private String urn; // 说明该asset是通过推理产生的,且已经在推理所用的oss上传
    private Boolean deleted;

    // TODO: 修正下数据后可以去掉这个逻辑，直接使用 ossSource Getter
    public OSSProviderSource getOssProviderSourceEnum() {
        if (ossSource == null || ossSource.isEmpty()) {
            return OSSProviderSource.AliYun;
        }
        // 兼容保存的 endpoint 数据
        if (ossSource.contains(".")) {
            OssClient.OSSEndpointMeta meta = OssClient.parseOSSEndpoint(ossSource);
            return meta.getSource();
        }
        return OSSProviderSource.fromValue(ossSource);
    }
}
