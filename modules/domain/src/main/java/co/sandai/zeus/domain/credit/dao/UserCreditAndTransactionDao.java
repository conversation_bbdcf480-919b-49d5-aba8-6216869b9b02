package co.sandai.zeus.domain.credit.dao;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.log.trace.TraceContext;
import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.credit.enums.CreditTypeEnum;
import co.sandai.zeus.infra.lock.RedisLock;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Component
@Slf4j
public class UserCreditAndTransactionDao {

    @Resource
    private UserCreditMapper userCreditMapper;

    @Resource
    private UserCreditTransactionMapper userCreditTransactionMapper;

    private void issuePurchasedCredit(long userId, long orgId, String requestId, int amount, Timestamp expireTime) {
        // 插入
        UserCreditDO userCreditDO = new UserCreditDO();
        userCreditDO.setUserId(userId);
        userCreditDO.setOrgId(orgId);
        userCreditDO.setCreditType(CreditTypeEnum.PURCHASED.name());
        userCreditDO.setAmount((long) amount);
        userCreditDO.setExpireTime(expireTime);
        userCreditDO.setRequestId(requestId);
        userCreditMapper.insertUserCredit(userCreditDO);

        insertPurchasedCreditTransaction(
                userId,
                orgId,
                amount,
                CreditTypeEnum.PURCHASED,
                UserCreditTransactionDO.ReasonSubTypeEnum.PURCHASED_CREDIT);
    }

    /**
     * creditType = SUBSCRIPTION
     * 注意幂等性.
     * 这里要做完全的幂等的话，需要加上分布式锁了， 再结合事务保证原子性。 事务隔离级别倒是不用开���高，因为有分布式锁保证
     * 更新订阅场景请勿调用此方法。 此方法不考虑补发credit情况
     */
    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    @RedisLock(key = "issueSubscriptionCredit-${0}", timeout = 10000)
    public void issueSubscriptionCredit(Long userId, Integer amount, LocalDateTime payTime) {
        CreditTypeEnum creditTypeEnum = CreditTypeEnum.SUBSCRIPTION;
        List<UserCreditDO> userCredits =
                userCreditMapper.getUserCreditsIncludingDeleted(userId, creditTypeEnum.name(), true);
        if (CollectionUtils.isEmpty(userCredits)) {
            // 插入
            UserCreditDO userCreditDO = new UserCreditDO();
            userCreditDO.setUserId(userId);
            userCreditDO.setOrgId(0L);
            userCreditDO.setCreditType(creditTypeEnum.name());
            userCreditDO.setAmount(Long.valueOf(amount));
            userCreditDO.setExpireTime(Timestamp.valueOf(payTime.plusMonths(1)));

            userCreditMapper.insertUserCredit(userCreditDO);
        } else {
            // 更新
            List<UserCreditDO> normalUserCreditDO = userCredits.stream()
                    .filter(x -> !x.isDeleted())
                    .filter(x -> x.getTempCredit() == null || !x.getTempCredit()) // 去掉tempCredit临时账户
                    .collect(Collectors.toList());
            List<UserCreditDO> tempUserCredit = userCredits.stream()
                    .filter(x -> !x.isDeleted())
                    .filter(x -> x.getTempCredit() != null && x.getTempCredit())
                    .toList();
            if (normalUserCreditDO.size() > 1) {
                LogUtil.errorf(
                        log,
                        "userCredits of SUBSCRIPTION type count > 1, there should be only one! userId: {0}, userCredits: {1}",
                        userId,
                        JSON.toJSONString(normalUserCreditDO));
            }
            UserCreditDO firstInDb;
            if (!normalUserCreditDO.isEmpty()) {
                // 有未删除且非temp的，就取这种进行覆盖
                firstInDb = normalUserCreditDO.getFirst();
            } else if (!tempUserCredit.isEmpty()) {
                // 有未删除但temp的，取这种覆盖
                firstInDb = tempUserCredit.getFirst();
            } else {
                // 没有未删除的，剩下是deleted的
                firstInDb = userCredits.getFirst();
            }
            UserCreditDO userCreditDO = new UserCreditDO();
            userCreditDO.setId(firstInDb.getId());
            userCreditDO.setAmount(Long.valueOf(amount));
            userCreditDO.setExpireTime(Timestamp.valueOf(payTime.plusMonths(1)));
            userCreditDO.setTempCredit(false);
            userCreditMapper.updateUserCreditById(userCreditDO);
        }

        insertPurchasedCreditTransaction(
                userId, 0L, amount, creditTypeEnum, UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT);
    }

    @Transactional(rollbackFor = Throwable.class)
    @RedisLock(key = "issuePurchasedCredit-${0}", timeout = 10000)
    public void issuePurchasedUserCredit(Long userId, String requestId, int amount, LocalDateTime payTime) {
        log.info("start issue {} credits for userId {}, requestId: {}", amount, userId, requestId);
        // 先查，幂等处理
        UserCreditDO userCredit =
                userCreditMapper.getUserCreditByRequestId(userId, requestId, CreditTypeEnum.PURCHASED.name());
        if (userCredit != null) {
            LogUtil.infof(
                    log,
                    "userCredit already exists, no need to issue now, userId: {0}, requestId: {1}, creditType: {2}",
                    userId,
                    requestId,
                    CreditTypeEnum.PURCHASED.name());
            return;
        }

        // 发放
        issuePurchasedCredit(userId, 0L, requestId, amount, Timestamp.valueOf(payTime.plusMonths(1)));
        log.info("issue {} credits for user {}, expire in 1 month", amount, userId);
    }

    @Transactional(rollbackFor = Throwable.class)
    @RedisLock(key = "issuePurchasedPlatformCredit-${0}", timeout = 10000)
    public void issuePurchasedOrgCredit(Long orgId, String requestId, int amount, LocalDateTime payTime) {
        log.info("start issue {} credits for org {}, requestId: {}", amount, orgId, requestId);
        UserCreditDO userCredit =
                userCreditMapper.getOrgCreditByRequestId(orgId, requestId, CreditTypeEnum.PURCHASED.name());

        if (userCredit != null) {
            log.info(
                    "userCredit already exists, no need to issue now, orgId: {}, requestId: {}, creditType: {}",
                    orgId,
                    requestId,
                    CreditTypeEnum.PURCHASED.name());
            return;
        }

        // 发放
        issuePurchasedCredit(0L, orgId, requestId, amount, Timestamp.valueOf(payTime.plusMonths(3)));
        log.info("issued {} credits for org {}, expire in 3 months", amount, orgId);
    }

    /*
    两个入口
    1. 注册的时候
    2. 每月更新
     */
    @Transactional(isolation = Isolation.SERIALIZABLE, rollbackFor = Exception.class)
    public void issueFreeCredit(java.lang.Long userId, java.lang.Integer amount, java.time.LocalDateTime issueAt) {
        // 1. 新增credit记录
        UserCreditDO userCreditDO = new UserCreditDO();
        userCreditDO.setUserId(userId);
        userCreditDO.setOrgId(0L);
        userCreditDO.setCreditType(CreditTypeEnum.FREE.name());
        userCreditDO.setAmount(Long.valueOf(amount));
        Timestamp monthEnd = TimeUtil.getNextMonthStartTimestamp(issueAt);
        userCreditDO.setExpireTime(monthEnd);
        userCreditMapper.insertUserCredit(userCreditDO);

        insertCreditTransaction(
                userId,
                null,
                amount,
                CreditTypeEnum.FREE,
                UserCreditTransactionDO.DirectionEnum.IN,
                UserCreditTransactionDO.ReasonTypeEnum.EARNED,
                UserCreditTransactionDO.ReasonSubTypeEnum.MONTHLY_FREE_CREDIT);
    }

    /**
     * 这里采用serializable隔离级别，在超高并发情况下可能有性能问题，这里先不做考虑
     */
    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    public void deductCredits(
            Long userId,
            Long orgId,
            Integer useAmount,
            UserCreditTransactionDO.ReasonTypeEnum reasonType,
            UserCreditTransactionDO.ReasonSubTypeEnum reasonSubType) {
        // Implement logic to subtract credits from the user's account
        List<UserCreditDO> userCredits = null;
        if (Objects.nonNull(userId)) {
            userCredits = userCreditMapper.getUserCreditsIncludingDeleted(userId, null, true);
        }
        if (Objects.nonNull(orgId)) {
            userCredits = userCreditMapper.getOrgCreditsIncludingDeleted(orgId, null, true);
        }
        if (CollectionUtils.isEmpty(userCredits)) {
            throw new ZeusServiceException(ErrorCode.InsufficientCredit, "User credit not found");
        }
        // 查找所有creditType的credit，然后排序
        List<UserCreditDO> undeletedUserCreditDO = userCredits.stream()
                .filter(x -> !x.isDeleted())
                .filter(x -> x.getExpireTime().after(TimeUtil.utcTimeTimeStamp()))
                .sorted(Comparator.comparing(UserCreditDO::getExpireTime))
                .toList();
        if (undeletedUserCreditDO.isEmpty()) {
            throw new ZeusServiceException(ErrorCode.InsufficientCredit, "No active credit found");
        }

        // 检查是否能够扣减
        long totalAmount = undeletedUserCreditDO.stream()
                .mapToLong(UserCreditDO::getAmount)
                .sum();
        if (totalAmount < useAmount) {
            throw new ZeusServiceException(ErrorCode.InsufficientCredit, "Insufficient credit balance");
        }

        // 2. 更新credit
        long accumulatedAmount = 0;
        List<UserCreditDO> updatedUserCreditDOs = new ArrayList<>();
        for (UserCreditDO userCreditDO : undeletedUserCreditDO) {
            long curDeductAmount;
            if (accumulatedAmount + userCreditDO.getAmount() > useAmount) {
                curDeductAmount = useAmount - accumulatedAmount;
            } else {
                curDeductAmount = userCreditDO.getAmount();
            }
            accumulatedAmount += curDeductAmount;
            // 收集待更新credit
            UserCreditDO updatedUserCreditDO = new UserCreditDO();
            updatedUserCreditDO.setId(userCreditDO.getId());
            updatedUserCreditDO.setAmount(userCreditDO.getAmount() - curDeductAmount);

            updatedUserCreditDOs.add(updatedUserCreditDO);
            if (accumulatedAmount >= useAmount) {
                break;
            }
        }
        userCreditMapper.batchUpdateUserCreditById(updatedUserCreditDOs);
        log.info(
                "User {}, orgId {} credit useAmount changed from {} to {}",
                userId,
                orgId,
                totalAmount,
                totalAmount - accumulatedAmount);

        insertCreditTransaction(
                userId,
                orgId,
                useAmount,
                CreditTypeEnum.UNKNOWN,
                UserCreditTransactionDO.DirectionEnum.OUT,
                reasonType,
                reasonSubType);
    }

    /**
     * 此方法假设上一个subscription credit账户一定是在此时过期。也就是说仅在续费情况下调用此方法
     * 加事务
     */
    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    public void renewSubscriptionCredit(Long userId, Integer integer, LocalDateTime payTime) {
        CreditTypeEnum creditTypeEnum = CreditTypeEnum.SUBSCRIPTION;
        // 这里通过加上for update关键字，会加上行级排他锁，从而避免和定时任务冲突
        List<UserCreditDO> userCredits =
                userCreditMapper.getUserCreditsIncludingDeleted(userId, creditTypeEnum.name(), true);
        if (CollectionUtils.isEmpty(userCredits)) {
            LogUtil.errorf(log, "userCredits of SUBSCRIPTION type is empty, userId: {0}", userId);
            return;
        }
        // 更新
        List<UserCreditDO> normalUserCredits = userCredits.stream()
                .filter(x -> !x.isDeleted())
                .filter(x -> x.getTempCredit() == null || !x.getTempCredit()) // 去掉tempCredit临时账户
                .collect(Collectors.toList());
        List<UserCreditDO> tempUserCredits = userCredits.stream()
                .filter(x -> !x.isDeleted())
                .filter(x -> x.getTempCredit() != null && x.getTempCredit())
                .toList();
        if (normalUserCredits.size() > 1) {
            LogUtil.errorf(
                    log,
                    "userCredits of SUBSCRIPTION type count > 1, there should be only one! userId: {0}, userCredits: {1}",
                    userId,
                    JSON.toJSONString(normalUserCredits));
        }
        UserCreditDO firstInDb;
        long expiredCredit;
        if (!normalUserCredits.isEmpty()) {
            // 有未删除且非temp的，就取这种进行覆盖
            firstInDb = normalUserCredits.getFirst();
            expiredCredit = firstInDb.getAmount();
        } else if (!tempUserCredits.isEmpty()) {
            // 有未删除但temp的，取这种覆盖 理论上不会走到这里
            firstInDb = tempUserCredits.getFirst();
            if (TimeUtil.isExpire(firstInDb.getExpireTime())) {
                expiredCredit = 0;
            } else {
                expiredCredit = firstInDb.getAmount();
            }
        } else {
            // 没有未删除的，剩下是deleted的
            firstInDb = userCredits.getFirst();
            expiredCredit = 0;
        }
        UserCreditDO userCreditDO = new UserCreditDO();
        userCreditDO.setId(firstInDb.getId());
        userCreditDO.setAmount(Long.valueOf(integer));
        userCreditDO.setExpireTime(Timestamp.valueOf(payTime.plusMonths(1)));
        userCreditMapper.updateUserCreditById(userCreditDO);

        if (expiredCredit > 0) {
            insertCreditTransaction(
                    userId,
                    null,
                    (int) expiredCredit,
                    creditTypeEnum,
                    UserCreditTransactionDO.DirectionEnum.OUT,
                    UserCreditTransactionDO.ReasonTypeEnum.SPENT,
                    UserCreditTransactionDO.ReasonSubTypeEnum.EXPIRED_CREDITS);
        }
        insertPurchasedCreditTransaction(
                userId, 0L, integer, creditTypeEnum, UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    public void issueSubscriptionDiffCredit(Long userId, int creditNum) {
        CreditTypeEnum creditTypeEnum = CreditTypeEnum.SUBSCRIPTION;
        List<UserCreditDO> userCredits = userCreditMapper.getNotExpiredUserCredits(userId, creditTypeEnum.name());
        if (CollectionUtils.isEmpty(userCredits)) {
            LogUtil.errorf(log, "userCredits of SUBSCRIPTION type is empty, userId: {0}", userId);
            return;
        }
        // 更新
        List<UserCreditDO> normalUserCredits = userCredits.stream()
                .filter(x -> !x.isDeleted())
                .filter(x -> x.getTempCredit() == null || !x.getTempCredit()) // 去掉tempCredit临时账户
                .collect(Collectors.toList());
        if (normalUserCredits.size() > 1) {
            LogUtil.errorf(
                    log,
                    "userCredits of SUBSCRIPTION type count > 1, there should be only one! userId: {0}, userCredits: {1}",
                    userId,
                    JSON.toJSONString(normalUserCredits));
        } else if (normalUserCredits.isEmpty()) {
            LogUtil.errorf(
                    log,
                    "userCredits of SUBSCRIPTION type count <= 0, there should be at least one! userId: {0}",
                    userId);
            return;
        }
        UserCreditDO firstInDb = normalUserCredits.getFirst();
        UserCreditDO userCreditDO = new UserCreditDO();
        userCreditDO.setId(firstInDb.getId());
        userCreditDO.setAmount(creditNum + firstInDb.getAmount());
        userCreditMapper.updateUserCreditById(userCreditDO);

        insertPurchasedCreditTransaction(
                userId, 0L, creditNum, creditTypeEnum, UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    public void issueNewSubscriptionCredit(Long userId, Integer amount, LocalDateTime payTime) {
        // 把原来订阅账号置为tempCredit
        List<UserCreditDO> notExpiredUserCredits =
                userCreditMapper.getNotExpiredUserCredits(userId, CreditTypeEnum.SUBSCRIPTION.name());
        for (UserCreditDO notExpiredUserCredit : notExpiredUserCredits) {
            // 目前理论上这里只有一个循环。所以直接在循环里实现了得了
            UserCreditDO userCredit = new UserCreditDO();
            userCredit.setId(notExpiredUserCredit.getId());
            userCredit.setTempCredit(true);

            userCreditMapper.updateUserCreditById(userCredit);
        }

        // 加入一个新的订阅
        UserCreditDO newUserCredit = new UserCreditDO();
        newUserCredit.setUserId(userId);
        newUserCredit.setOrgId(0L);
        newUserCredit.setCreditType(CreditTypeEnum.SUBSCRIPTION.name());
        newUserCredit.setAmount(Long.valueOf(amount));
        newUserCredit.setExpireTime(Timestamp.valueOf(payTime.plusMonths(1)));
        userCreditMapper.insertUserCredit(newUserCredit);

        // 插入交易记录
        insertPurchasedCreditTransaction(
                userId,
                0L,
                amount,
                CreditTypeEnum.SUBSCRIPTION,
                UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT);
    }

    private void insertCreditTransaction(
            Long userId,
            Long orgId,
            int amount,
            CreditTypeEnum creditTypeEnum,
            UserCreditTransactionDO.DirectionEnum directionEnum,
            UserCreditTransactionDO.ReasonTypeEnum reasonTypeEnum,
            UserCreditTransactionDO.ReasonSubTypeEnum reasonSubTypeEnum) {
        UserCreditTransactionDO userCreditTransactionDO = new UserCreditTransactionDO();
        userCreditTransactionDO.setUserId(Objects.isNull(userId) ? 0L : userId);
        userCreditTransactionDO.setOrgId(Objects.isNull(orgId) ? 0L : orgId);
        userCreditTransactionDO.setCreditType(creditTypeEnum.name());
        userCreditTransactionDO.setDirection(directionEnum.name());
        userCreditTransactionDO.setAmount(amount);
        userCreditTransactionDO.setReasonType(reasonTypeEnum.name());
        userCreditTransactionDO.setReasonSubType(reasonSubTypeEnum.name());
        userCreditTransactionDO.setTraceId(TraceContext.getTraceId());
        userCreditTransactionMapper.insertUserCreditTransaction(userCreditTransactionDO);

        log.info(
                "credit transaction inserted, amount: {}, userId: {}, orgId: {}, direction: {}, reason: {}, subReason: {}",
                amount,
                userId,
                orgId,
                directionEnum,
                reasonTypeEnum,
                reasonSubTypeEnum);
    }

    private void insertPurchasedCreditTransaction(
            Long userId,
            Long orgId,
            Integer amount,
            CreditTypeEnum creditTypeEnum,
            UserCreditTransactionDO.ReasonSubTypeEnum reasonSubTypeEnum) {
        insertCreditTransaction(
                userId,
                orgId,
                amount,
                creditTypeEnum,
                UserCreditTransactionDO.DirectionEnum.IN,
                UserCreditTransactionDO.ReasonTypeEnum.PURCHASED,
                reasonSubTypeEnum);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    public void issueOperationCredit(Integer amount, CreditTypeEnum creditTypeEnum, Long userId, Long orgId) {
        UserCreditDO userCredit = new UserCreditDO();
        userCredit.setUserId(userId);
        userCredit.setOrgId(orgId);
        userCredit.setCreditType(creditTypeEnum.name());
        userCredit.setAmount(Long.valueOf(amount));
        userCredit.setExpireTime(Timestamp.valueOf(TimeUtil.utcTime().plusMonths(1)));
        userCredit.setRequestId("");
        userCredit.setTempCredit(false);

        userCreditMapper.insertUserCredit(userCredit);

        insertCreditTransaction(
                userId,
                orgId,
                amount,
                creditTypeEnum,
                UserCreditTransactionDO.DirectionEnum.IN,
                UserCreditTransactionDO.ReasonTypeEnum.EARNED,
                UserCreditTransactionDO.ReasonSubTypeEnum.BONUS_CREDIT);
    }
}
