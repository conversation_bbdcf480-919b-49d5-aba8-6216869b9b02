package co.sandai.zeus.domain.plan.model;

import co.sandai.zeus.domain.plan.model.enums.IntervalTypeEnum;
import co.sandai.zeus.domain.plan.model.enums.PlanStatusEnum;
import com.alibaba.fastjson.JSON;
import lombok.Data;

@Data
public class Plan {

    private String planCode;
    private String name;

    private IntervalTypeEnum intervalTypeEnum;
    private int planGrade;
    private boolean recommended;

    private MemberShip memberShip;
    // 为方便加载添加
    private String memberCode;

    private PlanStatusEnum status;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
