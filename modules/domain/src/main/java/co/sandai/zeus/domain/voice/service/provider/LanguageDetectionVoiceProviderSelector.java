package co.sandai.zeus.domain.voice.service.provider;

import co.sandai.zeus.domain.voice.service.VoiceProviderSelector;
import co.sandai.zeus.infra.voice.client.ElevenLabsClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * Selects voice provider based on ElevenLabs language detection API
 */
@Service
@Qualifier("languageDetectionSelector")
@Slf4j
public class LanguageDetectionVoiceProviderSelector implements VoiceProviderSelector {

    private final ElevenLabsClient elevenLabsClient;

    public LanguageDetectionVoiceProviderSelector(ElevenLabsClient elevenLabsClient) {
        this.elevenLabsClient = elevenLabsClient;
    }

    @Override
    public String selectProviderForText(String text) {
        // Use ElevenLabs API to detect language
        String detectedLanguage = elevenLabsClient.detectLanguage(text);
        log.info(
                "Detected language for text: {} -> {}",
                text.substring(0, Math.min(text.length(), 20)),
                detectedLanguage);

        // If Chinese, use Minimax
        if (detectedLanguage.startsWith("zh")) {
            return "minimax";
        }
        // Otherwise use ElevenLabs
        return "elevenlabs";
    }
}
