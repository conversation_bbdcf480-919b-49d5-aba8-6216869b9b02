package co.sandai.zeus.domain.task.dao.mapper;

import co.sandai.zeus.domain.task.dao.ModerationFailType;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.dao.TaskType;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TaskMapper {

    void insertTask(@Param("task") Task task);

    List<Task> getTasksByUserId(
            @Param("userId") Long userId,
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("source_list") List<TaskSourceEnum> taskSourceEnumList);

    List<Task> getTasksByUserIdAndFilters(
            @Param("userId") Long userId,
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("source_list") List<TaskSourceEnum> taskSourceEnumList,
            @Param("type_list") List<TaskType> taskTypeList);

    int countTasksByUserIdAndFilters(
            @Param("userId") Long userId,
            @Param("source_list") List<TaskSourceEnum> taskSourceEnumList,
            @Param("type_list") List<TaskType> taskTypeList);

    List<Task> getTasksByStatus(
            @Param("status") TaskStatus status,
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("source_list") List<TaskSourceEnum> taskSourceEnumList);

    List<String> getTaskPrompts(@Param("offset") int offset, @Param("limit") int limit);

    Task getTaskById(@Param("userId") Long userId);

    int getTotalTaskCountByUserId(@Param("userId") Long userId);

    int getTaskCountByUserId(@Param("userId") Long userId);

    int getTaskCountByStatus(TaskStatus status);

    int getTaskCountOfUser(List<Long> taskIds, Long userId);

    int getUnSafeCount(List<Long> taskIds);

    List<Task> getNonMergedSuccessTask();

    void updateTaskStatus(@Param("taskId") Long taskId, @Param("status") TaskStatus status);

    void updateTaskPrompt(Long taskId, String enhancedPrompt, String firstFramePrompt);

    void updateEnhancedPrompt(Long taskId, String enhancedPrompt);

    void updateTaskSourceVideoId(Long taskId, Long videoId);

    void updateTaskResultVideoId(Long taskId, Long assetId);
    /**
     * 更新任务的内容审核结果，包括失败原因和置信度
     *
     * @param taskId 任务ID
     * @param isPassed 审核是否通过
     * @param failType 审核失败类型
     * @param confidence 审核置信度值
     */
    void updateTaskModerationResult(Long taskId, boolean isPassed, ModerationFailType failType, Float confidence);

    void updateTaskInferChunkResult(Long taskId, int completedChunkCount);

    void updateTaskPosterId(Long taskId, Long posterId);

    void updateTaskResultInfo(Long taskId, Long posterId, int width, int height, String modelVersion, long seed);

    void deleteTaskById(Long taskId);
}
