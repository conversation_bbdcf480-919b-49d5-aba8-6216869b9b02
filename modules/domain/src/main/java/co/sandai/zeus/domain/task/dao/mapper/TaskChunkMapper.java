package co.sandai.zeus.domain.task.dao.mapper;

import co.sandai.zeus.domain.task.dao.TaskChunkDO;
import co.sandai.zeus.domain.task.dao.TaskChunkStatus;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface TaskChunkMapper {

    @Insert("INSERT INTO task_chunk (id, task_id, prompt, duration, enhanced_prompt, `index`, audio_asset_id) "
            + "VALUES (#{id}, #{taskId}, #{prompt}, #{duration}, #{enhancedPrompt}, #{index}, #{audioAssetId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(TaskChunkDO taskChunk);

    @Select("SELECT * FROM task_chunk WHERE id = #{id}")
    TaskChunkDO getById(@Param("id") long id);

    @Select("SELECT * FROM task_chunk WHERE task_id = #{taskId} ORDER BY `index`")
    List<TaskChunkDO> getByTaskId(@Param("taskId") long taskId);

    @Update("UPDATE task_chunk SET task_id = #{taskId}, prompt = #{prompt}, "
            + "duration = #{duration}, enhanced_prompt = #{enhancedPrompt}, "
            + "`index` = #{index} WHERE id = #{id}")
    void update(TaskChunkDO taskChunk);

    @Update("UPDATE task_chunk SET status = #{status} WHERE id = #{id}")
    void updateStatus(@Param("id") long id, @Param("status") TaskChunkStatus status);

    @Update("UPDATE task_chunk SET enhanced_prompt = #{enhancedPrompt} WHERE id = #{id}")
    void updateEnhancedPrompt(@Param("id") long id, @Param("enhanced_prompt") String enhancedPrompt);

    @MapKey("id")
    @Select("<script>"
            + "SELECT * FROM task_chunk WHERE id IN "
            + "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"
            + "#{id}"
            + "</foreach>"
            + "</script>")
    Map<Long, TaskChunkDO> findByIdsAsMap(@Param("ids") List<Long> ids);

    @Select("<script>"
            + "SELECT * FROM task_chunk WHERE id IN "
            + "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"
            + "#{id}"
            + "</foreach>"
            + "</script>")
    List<TaskChunkDO> getByIds(@Param("ids") List<Long> ids);

    @MapKey("id")
    @Select("<script>"
            + "SELECT * FROM task_chunk WHERE id IN "
            + "<foreach collection='taskChunkIds' item='taskChunkId' open='(' separator=',' close=')'>"
            + "#{taskChunkId}"
            + "</foreach>"
            + "</script>")
    Map<Long, TaskChunkDO> getByTaskChunkIdsAsMap(@Param("taskChunkIds") List<Long> taskChunkIds);
}
