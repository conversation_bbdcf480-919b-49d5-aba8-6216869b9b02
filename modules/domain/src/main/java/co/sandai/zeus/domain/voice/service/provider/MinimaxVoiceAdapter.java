package co.sandai.zeus.domain.voice.service.provider;

import co.sandai.zeus.domain.voice.dto.VoiceDTO;
import co.sandai.zeus.infra.voice.client.MinimaxClient;
import co.sandai.zeus.infra.voice.dto.MinimaxVoiceDTO;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * Adapter that implements VoiceProvider interface using MinimaxClient
 * This class translates between domain layer DTOs and infrastructure layer DTOs
 */
@Service
public class MinimaxVoiceAdapter implements VoiceProvider {

    private final MinimaxClient minimaxClient;

    public MinimaxVoiceAdapter(MinimaxClient minimaxClient) {
        this.minimaxClient = minimaxClient;
    }

    @Override
    public String getProviderName() {
        return "minimax";
    }

    @Override
    public List<VoiceDTO> getVoices() {
        List<MinimaxVoiceDTO> infraVoices = minimaxClient.getVoices();
        return infraVoices.stream().map(this::toVoiceDTO).collect(Collectors.toList());
    }

    @Override
    public VoiceDTO createVoice(String name, String description, List<MultipartFile> files) {
        MinimaxVoiceDTO infraVoice = minimaxClient.createVoice(name, description, files);
        return toVoiceDTO(infraVoice);
    }

    @Override
    public byte[] textToSpeech(String voiceId, String text) {
        return minimaxClient.textToSpeech(voiceId, text);
    }

    @Override
    public boolean deleteVoice(String voiceId) {
        // For Minimax API, voice_type must be "voice_cloning" or "voice_generation"
        return minimaxClient.deleteVoice(voiceId, "voice_cloning");
    }

    /**
     * Convert infrastructure layer DTO to domain layer DTO
     */
    private VoiceDTO toVoiceDTO(MinimaxVoiceDTO infraVoice) {
        return VoiceDTO.builder()
                .id(infraVoice.getVoiceId())
                .name(infraVoice.getName())
                .description(infraVoice.getDescription())
                .language(infraVoice.getLanguage())
                .previewUrl(infraVoice.getPreviewUrl())
                .verifiedLanguages(null) // Minimax doesn't have verified languages
                .isCloned(infraVoice.isCloned())
                .gender(infraVoice.getGender())
                .build();
    }

    @Override
    public String speechToText(byte[] audioData, String language) {
        // Placeholder implementation - Minimax currently doesn't support speech-to-text
        throw new UnsupportedOperationException("Speech-to-text is not currently supported by the Minimax provider");
    }
}
