package co.sandai.zeus.domain.voice.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO representing detailed information about a voice from an external provider.
 * This class represents the structure of the JSON that was previously stored as a string URL.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VoiceDetailsDto {
    private String voice_id;
    private String name;
    private Object samples;
    private String category;
    private FineTuning fine_tuning;
    private Labels labels;
    private String description;
    private String preview_url;
    private List<Object> available_for_tiers;
    private Object settings;
    private Sharing sharing;
    private List<String> high_quality_base_model_ids;
    private List<VerifiedLanguage> verified_languages;
    private Object safety_control;
    private VoiceVerification voice_verification;
    private String permission_on_resource;
    private boolean is_owner;
    private boolean is_legacy;
    private boolean is_mixed;
    private long created_at_unix;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FineTuning {
        private boolean is_allowed_to_fine_tune;
        private Map<String, String> state;
        private List<Object> verification_failures;
        private int verification_attempts_count;
        private boolean manual_verification_requested;
        private String language;
        private Map<String, Object> progress;
        private Map<String, String> message;
        private Object dataset_duration_seconds;
        private Object verification_attempts;
        private Object slice_ids;
        private Object manual_verification;
        private int max_verification_attempts;
        private long next_max_verification_attempts_reset_unix_ms;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Labels {
        private String accent;
        private String descriptive;
        private String age;
        private String gender;
        private String language;
        private String use_case;
        private String locale;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Sharing {
        private String status;
        private Object history_item_sample_id;
        private long date_unix;
        private List<Object> whitelisted_emails;
        private String public_owner_id;
        private String original_voice_id;
        private boolean financial_rewards_enabled;
        private boolean free_users_allowed;
        private boolean live_moderation_enabled;
        private int rate;
        private Object fiat_rate;
        private int notice_period;
        private Object disable_at_unix;
        private boolean voice_mixing_allowed;
        private boolean featured;
        private String category;
        private boolean reader_app_enabled;
        private String image_url;
        private Object ban_reason;
        private int liked_by_count;
        private int cloned_by_count;
        private String name;
        private String description;
        private Labels labels;
        private String review_status;
        private Object review_message;
        private boolean enabled_in_library;
        private Object instagram_username;
        private Object twitter_username;
        private Object youtube_username;
        private Object tiktok_username;
        private Object moderation_check;
        private Object reader_restricted_on;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VerifiedLanguage {
        private String language;
        private String model_id;
        private String accent;
        private String locale;
        private String preview_url;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VoiceVerification {
        private boolean requires_verification;
        private boolean is_verified;
        private List<Object> verification_failures;
        private int verification_attempts_count;
        private Object language;
        private Object verification_attempts;
    }
}
