package co.sandai.zeus.domain.credit.config;

import co.sandai.zeus.config.SystemConfig;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

@Component
public class CreditConfigLoader {

    @Resource
    private CreditConfigService creditConfigService;

    @Autowired
    private SystemConfig systemConfig;

    @PostConstruct
    public void loadConfig() {

        Yaml yaml = new Yaml();
        List<CreditPackage> creditPackages = loadCreditPackages(yaml);

        creditPackages = Collections.unmodifiableList(creditPackages);

        // creditConfigService
        creditConfigService.setCreditPackageList(creditPackages);
    }

    private List<CreditPackage> loadCreditPackages(Yaml yaml) {
        String configPath = String.format("plan-config/%s/creditpackageconfig.yml", systemConfig.getProductName());
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(configPath);
        if (inputStream == null) {
            throw new IllegalArgumentException("File not found: " + configPath);
        }
        Map<String, Object> yamlMap = yaml.load(inputStream);
        String jsonString = JSON.toJSONString(yamlMap.get("creditPackages"), true);
        return JSON.parseArray(jsonString, CreditPackage.class);
    }
}
