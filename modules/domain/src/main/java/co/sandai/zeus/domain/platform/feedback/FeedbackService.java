package co.sandai.zeus.domain.platform.feedback;

import co.sandai.zeus.common.helper.LarkHelper;
import co.sandai.zeus.domain.platform.feedback.dao.CustomApiPackageRequest;
import co.sandai.zeus.domain.platform.feedback.dao.CustomApiPackageRequestMapper;
import co.sandai.zeus.infra.IDGenerator;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class FeedbackService {

    @Value("${zeus.lark-robot-webhook.feedback:}")
    private String larkBotWebhook;

    @Autowired
    private CustomApiPackageRequestMapper customApiPackageRequestMapper;

    @Autowired
    private IDGenerator iDGenerator;

    public void createCustomApiPackageRequest(CustomApiPackageRequest request) {
        request.setId(iDGenerator.getNextId());
        customApiPackageRequestMapper.createCustomApiPackageRequest(request);
        if (Objects.nonNull(larkBotWebhook)) {
            LarkHelper.sendLarkBotMessage(larkBotWebhook, request.toLarkMessage());
        }
    }
}
