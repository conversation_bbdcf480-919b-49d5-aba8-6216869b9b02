package co.sandai.zeus.domain.user.dao;

import java.security.Principal;
import lombok.Data;

@Data
public class User implements Principal {
    private long id;
    private long avatarId;
    private String username;
    private String email;
    private String description;
    private UserSource source;
    private String nickname;
    private String password = "";
    private boolean isEmailVerified = false;

    private String outCustomerId;
    private String outCustomerType;

    @Override
    public String getName() {
        return this.email;
    }
}
