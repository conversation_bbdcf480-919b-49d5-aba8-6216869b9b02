package co.sandai.zeus.domain.task.dao;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.common.CropArea;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Task {
    private long id;
    private String prompt;
    private String enhancedPrompt;
    private String firstFramePrompt;
    private long sourceVideoTaskId; // 基于生成视频续写时的任务 ID
    private long sourceVideoId; // 基于上传的视频续写时的 Asset ID
    private long sourceVideoStartTimestamp;
    private long sourceVideoEndTimestamp;
    private long sourceImageId; // 图生视频时的图片 Asset ID
    private long sourceAudioId; // 音生视频时的音频 Asset ID
    private TaskType type;
    private String aspectRatio;
    private String tSchedulerFunc;
    private String tSchedulerArgs;
    private long seed;
    private float duration;
    private long totalDuration;
    private int chunkCount;
    private int completedChunkCount;
    private String model;
    private String underlyingModel;
    private CropArea cropArea;
    private TaskExtraInferArgs extraInferArgs;
    private TaskStatus status;
    private long userId;
    private long orgId;
    private int width;
    private int height;
    private int credits;
    private long resultVideoId;
    private long resultVideoPosterId;
    private boolean enablePromptEnhancement;
    private boolean enableAutoGenFirstFrame;
    private boolean isModerationPassed;

    /**
     * 内容审核不通过的具体原因类型
     */
    @Builder.Default
    private ModerationFailType moderationFailType = ModerationFailType.NONE;

    /**
     * 内容审核的置信度分值
     */
    private Float moderationConfidence;
    /**
     * 内部demo用
     * 不应该加这种字段理论上，为了快速迭代先这么写了
     */
    private Long inspirationId;
    /**
     * 场景来源
     */
    private TaskSourceEnum taskSource;

    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;

    public String getTSchedulerArgs() {
        if (Objects.isNull(tSchedulerArgs)) {
            return "";
        }
        return tSchedulerArgs;
    }

    public String getFinalPrompt() {
        if (this.getEnhancedPrompt() != null) {
            return this.getEnhancedPrompt();
        }
        return this.getPrompt();
    }

    // 有问题直接抛出invalid params异常
    public void checkParams(Asset sourceAsset) {
        if (cropArea != null) {
            if (sourceAsset == null) {
                throw ZeusServiceException.badRequest("Invalid parameters, get crop area without source asset");
            }
            cropArea.checkParams(sourceAsset.getWidth(), sourceAsset.getHeight());
        }
        // 其他校验逻辑
    }
}
