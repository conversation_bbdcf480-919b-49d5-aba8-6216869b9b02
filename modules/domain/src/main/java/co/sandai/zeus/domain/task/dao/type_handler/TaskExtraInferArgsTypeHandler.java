package co.sandai.zeus.domain.task.dao.type_handler;

import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import co.sandai.zeus.type_handler.JSONTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(TaskExtraInferArgs.class)
public class TaskExtraInferArgsTypeHandler extends JSONTypeHandler<TaskExtraInferArgs> {
    public TaskExtraInferArgsTypeHandler() {
        super(TaskExtraInferArgs.class);
    }
}
