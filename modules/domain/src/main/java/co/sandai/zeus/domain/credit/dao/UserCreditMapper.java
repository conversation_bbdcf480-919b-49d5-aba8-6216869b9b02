package co.sandai.zeus.domain.credit.dao;

import co.sandai.zeus.domain.credit.enums.CreditTypeEnum;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.*;

@Mapper
public interface UserCreditMapper {

    @Insert("INSERT INTO user_credit (user_id, org_id, credit_type, request_id, amount, expire_time) "
            + "VALUES (#{userId}, #{orgId}, #{creditType}, #{requestId}, #{amount}, #{expireTime})")
    void insertUserCredit(UserCreditDO userCredit);

    @Update("<script>UPDATE user_credit" + "<set>"
            + "<if test='userId != null'>user_id = #{userId},</if>"
            + "<if test='creditType != null'>credit_type = #{creditType},</if>"
            + "<if test='amount != null'>amount = #{amount},</if>"
            + "<if test='expireTime != null'>expire_time = #{expireTime},</if>"
            + "<if test='tempCredit != null'>temp_credit = #{tempCredit},</if>"
            + "deleted = 0"
            + "</set>"
            + "WHERE id = #{id}</script>")
    void updateUserCreditById(UserCreditDO userCredit);

    List<UserCreditDO> getUserCreditsIncludingDeleted(long userId, String creditType, boolean forUpdate);

    List<UserCreditDO> getOrgCreditsIncludingDeleted(long orgId, String creditType, boolean forUpdate);

    @Select("SELECT * FROM user_credit WHERE user_id = #{userId} AND request_id = #{requestId} "
            + "AND credit_type = #{creditType} AND deleted = 0")
    UserCreditDO getUserCreditByRequestId(long userId, String requestId, String creditType);

    @Select("SELECT * FROM user_credit WHERE user_id = #{userId} AND request_id = #{requestId} "
            + "AND credit_type = #{creditType} AND deleted = 0")
    UserCreditDO getOrgCreditByRequestId(long userId, String requestId, String creditType);

    /**
     * 查询未过期的userCredit
     *
     * @param creditType 可为空。为空时不过滤此条件
     */
    @Select("<script>SELECT * FROM user_credit WHERE user_id = #{userId} " + "AND expire_time &gt; NOW() "
            + "<if test='creditType != null'>AND credit_type = #{creditType}</if>"
            + "AND deleted = 0"
            + "</script>")
    List<UserCreditDO> getNotExpiredUserCredits(long userId, String creditType);

    List<UserCreditDO> getNotExpiredOrgCredits(long orgId, CreditTypeEnum creditType);

    @Update("<script>UPDATE user_credit SET amount = #{freeCreditAmount}, expire_time = #{expireTime} WHERE user_id IN "
            + "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>#{userId}</foreach> "
            + "AND credit_type = #{creditType}</script>")
    void batchUpdateUserCreditByUserId(
            @Param("userIds") List<Long> userIds,
            @Param("creditType") String creditType,
            @Param("freeCreditAmount") int freeCreditAmount,
            @Param("expireTime") LocalDateTime expireTime);

    @Select("<script>SELECT * FROM user_credit WHERE user_id IN "
            + "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>#{userId}</foreach> "
            + "AND credit_type = #{creditType}"
            + "AND deleted = 0"
            + "<if test='!includingTempCredit'> AND temp_credit = 0</if>"
            + "</script>")
    List<UserCreditDO> batchGetUserCredit(List<Long> userIds, String creditType, boolean includingTempCredit);

    @Select(
            """
                    <script>SELECT * FROM user_credit WHERE credit_type IN
                    <foreach item='creditType' collection='creditTypes' open='(' separator=',' close=')'>#{creditType}</foreach>
                    AND expire_time &gt; #{lowerBoundExpireTime} AND expire_time &lt;= #{upperBoundExpireTime}
                    <if test='lowerId != null'> AND id > #{lowerId} </if>
                    AND deleted = 0
                    ORDER BY id ASC LIMIT #{batchSize}</script>
            """)
    List<UserCreditDO> getExpiringUserCredit(
            Long lowerId,
            int batchSize,
            List<String> creditTypes,
            LocalDateTime lowerBoundExpireTime,
            LocalDateTime upperBoundExpireTime);

    @Update("<script>"
            + "<foreach item='item' collection='list' open='' close='' separator=';' >"
            + "UPDATE user_credit SET amount = #{item.amount} "
            + "<if test='item.expireTime != null'>, expire_time = #{item.expireTime}</if> "
            + "WHERE id = #{item.id}"
            + "</foreach>"
            + "</script>")
    void batchUpdateUserCreditById(@Param("list") List<UserCreditDO> newUserCreditDOs);
}
