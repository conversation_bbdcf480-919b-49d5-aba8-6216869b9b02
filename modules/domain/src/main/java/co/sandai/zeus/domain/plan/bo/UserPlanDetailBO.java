package co.sandai.zeus.domain.plan.bo;

import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class UserPlanDetailBO {

    private Long id;
    private String planCode;
    /**
     * userId是唯一约束，一个用户只能有一个订阅
     */
    private Long userId;
    /**
     * 计划过期时间
     */
    private Timestamp expireTime;
    /**
     * 下次发放credit时间
     */
    private Timestamp nextRenewTime;

    /**
     * 状态
     * 订阅的状态， 详见 @UserPlanDO.SubscriptionStatusEnum
     */
    private UserPlanDO.SubscriptionStatusEnum status;

    public static UserPlanDetailBO convert(
            UserPlanDO userPlanDO, String statusOfSubscription, Boolean cancelAtPeriodEnd) {
        UserPlanDetailBO bo = new UserPlanDetailBO();
        bo.setId(userPlanDO.getId());
        bo.setPlanCode(userPlanDO.getPlanCode());
        bo.setUserId(userPlanDO.getUserId());
        bo.setExpireTime(userPlanDO.getExpireTime());
        bo.setNextRenewTime(userPlanDO.getNextRenewTime());
        bo.setStatus(UserPlanDO.SubscriptionStatusEnum.fromString(statusOfSubscription, cancelAtPeriodEnd));
        return bo;
    }
}
