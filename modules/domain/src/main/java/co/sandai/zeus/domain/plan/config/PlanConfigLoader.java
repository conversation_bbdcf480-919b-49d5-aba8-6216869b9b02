package co.sandai.zeus.domain.plan.config;

import co.sandai.zeus.config.SystemConfig;
import co.sandai.zeus.domain.plan.model.Auth;
import co.sandai.zeus.domain.plan.model.MemberShip;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.PriceModel;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

/**
 * 计划配置加载器
 * <p>
 * 该类负责加载计划配置文件，并将其解析为相应的对象模型。
 * </p>
 * <p>
 * 该类在应用启动时自动加载配置文件，并将解析后的数据存储在 {@link PlanConfigService} 中。
 * </p>
 */
@Slf4j
@Component
public class PlanConfigLoader {

    @Resource
    private PlanConfigService planService;

    @Autowired
    private SystemConfig systemConfig;

    public void loadConfig() {

        Yaml yaml = new Yaml();
        List<Plan> planList = loadPlan(yaml);
        List<MemberShip> memberShips = loadMember(yaml);
        Map<String, MemberShip> memberMap =
                memberShips.stream().collect(Collectors.toMap(MemberShip::getMemberCode, memberShip -> memberShip));

        List<Auth> auths = loadAuth(yaml);
        Map<String, Auth> authMap = auths.stream().collect(Collectors.toMap(Auth::getAuthCode, auth -> auth));

        for (Plan plan : planList) {
            String memberCode = plan.getMemberCode();
            MemberShip memberShip = memberMap.get(memberCode);
            plan.setMemberShip(memberShip);

            if (memberShip == null) {
                return;
            }

            memberShip.setAuthList(new ArrayList<>(memberShip.getAuthCodes().size()));
            for (String authCode : memberShip.getAuthCodes()) {
                Auth auth = authMap.get(authCode);
                memberShip.getAuthList().add(auth);
            }
        }

        planList = Collections.unmodifiableList(planList);
        memberShips = Collections.unmodifiableList(memberShips);

        List<PriceModel> priceModels = loadPrice(yaml);
        Map<String, PriceModel> priceModelMap =
                priceModels.stream().collect(Collectors.toMap(PriceModel::getOutPriceId, priceModel -> priceModel));

        // 加载数据到planDataService
        planService.setPlanMetaData(planList, memberShips, authMap, priceModelMap);
    }

    private String getCommonPlanConfigResourcePath(String filename) {
        return String.format("plan-config/%s/%s", systemConfig.getProductName(), filename);
    }

    private String getPlanConfigResourcePathByEnv(String filename) {
        return String.format("plan-config/%s/%s/%s", systemConfig.getProductName(), systemConfig.getEnv(), filename);
    }

    private List<PriceModel> loadPrice(Yaml yaml) {
        String configPath = getPlanConfigResourcePathByEnv("priceconfig.yml");
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(configPath);
        if (inputStream == null) {
            throw new IllegalArgumentException("File not found: " + configPath);
        }
        Map<String, Object> yamlMap = yaml.load(inputStream);
        String jsonString = JSON.toJSONString(yamlMap.get("prices"), true);
        return JSON.parseArray(jsonString, PriceModel.class);
    }

    private List<Plan> loadPlan(Yaml yaml) {
        String planConfigPath = getCommonPlanConfigResourcePath("planconfig.yml");
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(planConfigPath);
        if (inputStream == null) {
            throw new IllegalArgumentException("File not found: " + planConfigPath);
        }
        Map<String, Object> yamlMap = yaml.load(inputStream);
        String jsonString = JSON.toJSONString(yamlMap.get("plans"), true);
        return JSON.parseArray(jsonString, Plan.class);
    }

    private List<MemberShip> loadMember(Yaml yaml) {
        String membershipConfig = getCommonPlanConfigResourcePath("membershipconfig.yml");
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(membershipConfig);
        if (inputStream == null) {
            throw new IllegalArgumentException("File not found: " + membershipConfig);
        }
        Map<String, Object> yamlMap = yaml.load(inputStream);
        String jsonString = JSON.toJSONString(yamlMap.get("memberships"), true);
        return JSON.parseArray(jsonString, MemberShip.class);
    }

    private List<Auth> loadAuth(Yaml yaml) {
        String authconfig = getCommonPlanConfigResourcePath("authconfig.yml");
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(authconfig);
        if (inputStream == null) {
            throw new IllegalArgumentException("File not found: " + authconfig);
        }
        Map<String, Object> yamlMap = yaml.load(inputStream);
        String jsonString = JSON.toJSONString(yamlMap.get("auths"), true);
        return JSON.parseArray(jsonString, Auth.class);
    }
}
