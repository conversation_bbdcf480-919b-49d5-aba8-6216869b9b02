package co.sandai.zeus.domain.report.dao;

import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ReportDO {
    private Long id;
    private Long userId; // 举报人ID
    private Long targetId; // 被举报内容ID
    private String targetType; // 被举报内容类型(如TASK等)
    private String tags; // 举报标签
    private String comment; // 其他描述
    private String status; // 举报状态
    private LocalDateTime createdAt;
}
