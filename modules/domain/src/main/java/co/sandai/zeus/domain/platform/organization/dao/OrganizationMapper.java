package co.sandai.zeus.domain.platform.organization.dao;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface OrganizationMapper {

    @Select("SELECT * FROM organization WHERE id = #{id}")
    Organization getOrganizationById(@Param("id") Long id);

    @Select("SELECT * FROM organization WHERE user_id = #{userId}")
    Organization getOrganizationByUserId(@Param("userId") Long userId);

    @Insert("INSERT INTO organization(id, user_id, name) VALUES (#{org.id}, #{org.userId}, #{org.name})")
    void createOrganization(@Param("org") Organization org);
}
