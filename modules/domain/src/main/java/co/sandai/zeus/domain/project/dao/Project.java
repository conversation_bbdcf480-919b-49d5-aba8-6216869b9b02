package co.sandai.zeus.domain.project.dao;

import lombok.Data;

@Data
public class Project {

    private Long id;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 画布JSON数据
     */
    private String canvasJson;

    /**
     * 是否已删除
     */
    private Boolean deleted = false;

    /**
     * 项目标题
     */
    private String title;

    /**
     * 项目封面资源ID
     */
    private Long posterAssetId;

    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;
}
