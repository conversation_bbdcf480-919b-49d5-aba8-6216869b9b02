package co.sandai.zeus.domain.payment;

import co.sandai.zeus.domain.payment.dao.OrderDO;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * 发送场景：
 * checkout回跳页面重定向；
 * checkout webhook回调；
 * invoice.paid webhook回调
 * portal更新订阅并支付 回跳页面重定向；
 * portal更新订阅并支付 webhook回调订阅更新
 */
@Getter
@Setter
public class PaySuccessEvent extends ApplicationEvent {

    private String priceId;
    private OrderDO.FromEnum from;
    private Long userId;
    private Long orgId;
    private LocalDateTime payTime;
    /**
     * 如有，可以带上，用于做幂等处理
     * 可以是session id, invoice id等。 但是同一场景应当保持一致
     */
    private String requestId;

    public PaySuccessEvent(Object source) {
        super(source);
    }
}
