package co.sandai.zeus.domain.project;

import co.sandai.zeus.domain.project.dao.Project;
import co.sandai.zeus.domain.project.dao.ProjectMapper;
import co.sandai.zeus.infra.IDGenerator;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class ProjectService {

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private IDGenerator idGenerator;

    public List<Project> getProjectsByUserId(Long userId, int offset, int limit) {
        return projectMapper.getProjectsByUserId(userId, offset, limit);
    }

    public int countProjectsByUserId(Long userId) {
        return projectMapper.countProjectsByUserId(userId);
    }

    public Project getProjectById(Long id) {
        return projectMapper.getProjectById(id);
    }

    public Long createProject(Project project) {
        project.setId(idGenerator.getNextId());
        projectMapper.insertProject(project);
        return project.getId();
    }

    public void updateProject(Project project) {
        projectMapper.updateProject(project);
    }

    public void deleteProject(Long id) {
        projectMapper.deleteProject(id);
    }

    public void updateCanvasJson(Long id, String canvasJson) {
        projectMapper.updateCanvasJson(id, canvasJson);
    }
}
