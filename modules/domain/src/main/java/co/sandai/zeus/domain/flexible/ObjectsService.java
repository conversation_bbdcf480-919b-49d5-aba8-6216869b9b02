package co.sandai.zeus.domain.flexible;

import co.sandai.zeus.domain.flexible.dao.ObjectDO;
import co.sandai.zeus.domain.flexible.dao.ObjectsMapper;
import co.sandai.zeus.infra.IDGenerator;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class ObjectsService {

    @Resource
    private ObjectsMapper objectsMapper;

    @Resource
    private IDGenerator idGenerator;

    public List<ObjectDTO> listObjects(int offset, int limit, String type) {
        return objectsMapper.findWithPaginationAndType(offset, limit, type).stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    public ObjectDTO getObject(Long id) {
        return toDTO(objectsMapper.findById(id));
    }

    public ObjectDTO createObject(ObjectDTO objectDTO) {
        ObjectDO objectDO = toDO(objectDTO);
        objectsMapper.insert(objectDO);
        return toDTO(objectDO);
    }

    public ObjectDTO updateObject(Long id, ObjectDTO objectDTO) {
        ObjectDO objectDO = toDO(objectDTO);
        objectDO.setId(id);
        objectsMapper.update(objectDO);
        return toDTO(objectDO);
    }

    public void deleteObject(Long id) {
        objectsMapper.logicalDelete(id);
    }

    private ObjectDTO toDTO(ObjectDO objectDO) {
        ObjectDTO objectDTO = new ObjectDTO();
        objectDTO.setId(objectDO.getId());
        objectDTO.setType(objectDO.getType());
        objectDTO.setData(objectDO.getData());
        objectDTO.setUserId(objectDO.getUserId());
        objectDTO.setDeleted(objectDO.getDeleted());
        objectDTO.setCreateTime(objectDO.getCreateTime());
        objectDTO.setUpdateTime(objectDO.getUpdateTime());
        return objectDTO;
    }

    private ObjectDO toDO(ObjectDTO objectDTO) {
        ObjectDO objectDO = new ObjectDO();
        if (objectDTO.getId() == null || objectDTO.getId() == 0) {
            objectDO.setId(idGenerator.getNextId());
        } else {
            objectDO.setId(objectDTO.getId());
        }
        objectDO.setType(objectDTO.getType());
        objectDO.setData(objectDTO.getData());
        objectDO.setUserId(objectDTO.getUserId());
        objectDO.setDeleted(objectDTO.getDeleted());
        objectDO.setCreateTime(objectDTO.getCreateTime());
        objectDO.setUpdateTime(objectDTO.getUpdateTime());
        return objectDO;
    }
}
