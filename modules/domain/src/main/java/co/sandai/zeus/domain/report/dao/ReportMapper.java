package co.sandai.zeus.domain.report.dao;

import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface ReportMapper {
    @Insert("INSERT INTO report (id, user_id, target_id, target_type, tags, comment, status) "
            + "VALUES (#{id}, #{userId}, #{targetId}, #{targetType}, #{tags}, #{comment}, #{status})")
    void insert(ReportDO report);

    @Insert("INSERT INTO report (id, user_id, target_id, target_type, tags, comment, status) "
            + "VALUES (#{id}, #{userId}, #{targetId}, #{targetType}, #{tags}, #{comment}, #{status}) "
            + "ON DUPLICATE KEY UPDATE "
            + "tags = #{tags}, "
            + "comment = #{comment}")
    void insertOrUpdate(ReportDO report);

    @Select("SELECT id, user_id, target_id, target_type, tags, comment, status, created_at "
            + "FROM report WHERE id = #{id}")
    ReportDO selectById(@Param("id") Long id);

    @Select("SELECT id, user_id, target_id, target_type, tags, comment, status, created_at "
            + "FROM report WHERE user_id = #{userId}")
    List<ReportDO> selectByUserId(@Param("userId") Long userId);

    @Select("SELECT id, user_id, target_id, target_type, tags, comment, status, created_at "
            + "FROM report WHERE target_id = #{targetId} AND target_type = #{targetType}")
    List<ReportDO> selectByTarget(@Param("targetId") Long targetId, @Param("targetType") String targetType);

    @Update("UPDATE report SET status = #{status} WHERE id = #{id}")
    void updateStatus(@Param("id") Long id, @Param("status") String status);
}
