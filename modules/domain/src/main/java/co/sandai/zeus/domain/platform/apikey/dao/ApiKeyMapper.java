package co.sandai.zeus.domain.platform.apikey.dao;

import java.util.List;
import org.apache.ibatis.annotations.*;

@Mapper
public interface ApiKeyMapper {
    @Select("SELECT * FROM api_key WHERE org_id = #{orgId} and deleted=0")
    List<ApiKey> getOrgApiKeys(Long orgId);

    @Select("SELECT count(*) FROM api_key WHERE org_id = #{orgId} and deleted=0")
    int countOrgApiKeys(Long orgId);

    @Select("SELECT * FROM api_key WHERE id = #{id} and deleted=0")
    ApiKey getApiKeyById(@Param("id") Long id);

    @Select("SELECT * FROM api_key WHERE `value`= #{value} and deleted=0")
    ApiKey getApiKeyByValue(String value);

    @Insert(
            "INSERT INTO api_key(id, user_id, name, value, org_id) VALUES (#{apiKey.id}, #{apiKey.userId}, #{apiKey.name}, #{apiKey.value}, #{apiKey.orgId})")
    void createApiKey(@Param("apiKey") ApiKey apiKey);

    @Update("UPDATE api_key SET deleted = 1 WHERE id = #{id}")
    void deleteApiKey(@Param("id") Long id);
}
