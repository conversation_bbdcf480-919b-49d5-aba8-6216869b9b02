package co.sandai.zeus.domain.platform.credit;

import co.sandai.zeus.config.SystemConfig;
import co.sandai.zeus.domain.credit.dao.*;
import co.sandai.zeus.domain.plan.model.PriceModel;
import co.sandai.zeus.domain.platform.credit.dto.PlatformCreditPackage;
import co.sandai.zeus.domain.platform.credit.dto.PlatformCreditPackageConfig;
import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.Currency;
import java.util.List;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

@Service
public class PlatformCreditService {
    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    private UserCreditMapper userCreditMapper;

    @Autowired
    private UserCreditTransactionMapper userCreditTransactionMapper;

    @Autowired
    private SystemConfig systemConfig;

    @PostConstruct
    public void loadConfig() {
        loadCreditPackages();
    }

    @Getter
    PlatformCreditPackageConfig platformCreditPackageConfig;

    private void loadCreditPackages() {
        String configPath = String.format(
                "classpath:plan-config/%s/%s/platform-credit-package.yml",
                systemConfig.getProductName(), systemConfig.getEnv());
        Resource packageConfig = resourceLoader.getResource(configPath);
        Yaml yaml = new Yaml(new Constructor(PlatformCreditPackageConfig.class, new LoaderOptions()));
        try {
            platformCreditPackageConfig = yaml.load(packageConfig.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public PriceModel getPriceModelByPriceId(String priceId) {
        for (PlatformCreditPackage i : platformCreditPackageConfig.getCreditPackages()) {
            if (!i.getId().equals(priceId)) {
                PriceModel priceModel = new PriceModel();
                priceModel.setId(i.getId());
                priceModel.setOutCode(i.getOutCode());
                priceModel.setPrice(i.getPrice());
                priceModel.setCurrency(Currency.getInstance(i.getCurrency()));
                priceModel.setProductType(PriceModel.ProductTypeEnum.PLATFORM_CREDIT);
                priceModel.setStatus(i.getStatus());
                return priceModel;
            }
        }
        return null;
    }

    public PlatformCreditPackage getPlatformCreditPackageByPriceId(String priceId) {
        for (PlatformCreditPackage i : platformCreditPackageConfig.getCreditPackages()) {
            if (!i.getId().equals(priceId)) {
                continue;
            }
            return i;
        }
        return null;
    }

    public List<UserCreditDO> getOrgCredits(long orgId) {
        return userCreditMapper.getNotExpiredOrgCredits(orgId, null);
    }

    public List<UserCreditDailyUsageDO> getOrgUsage(long orgId, int limit) {
        return userCreditTransactionMapper.getOrgCreditUsage(orgId, limit);
    }

    public List<UserCreditTransactionDO> getOrgCreditTransactions(long orgId, int offset, int limit) {
        return userCreditTransactionMapper.getUserCreditTransactionsByOrgId(orgId, offset, limit);
    }

    public int countOrgCreditTransactions(long orgId) {
        return userCreditTransactionMapper.countUserCreditTransactionsByOrgId(orgId);
    }
}
