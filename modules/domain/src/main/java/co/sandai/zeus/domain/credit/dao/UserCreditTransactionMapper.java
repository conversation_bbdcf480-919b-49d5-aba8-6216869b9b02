package co.sandai.zeus.domain.credit.dao;

import java.util.List;
import org.apache.ibatis.annotations.*;

@Mapper
public interface UserCreditTransactionMapper {

    @Insert(
            "INSERT INTO user_credit_transaction (user_id, org_id, credit_type, direction, amount, reason_type, reason_sub_type, trace_id) "
                    + "VALUES (#{userId}, #{orgId}, #{creditType}, #{direction}, #{amount}, #{reasonType}, #{reasonSubType}, #{traceId})")
    void insertUserCreditTransaction(UserCreditTransactionDO userCreditTransaction);

    @Select("SELECT * FROM user_credit_transaction WHERE id = #{id}")
    UserCreditTransactionDO getUserCreditTransactionById(long id);

    @Select("<script>SELECT * FROM user_credit_transaction WHERE user_id = #{userId}"
            + "<if test='lowerId != null'>AND id &gt; #{lowerId}</if> "
            + "LIMIT #{limit}</script>")
    List<UserCreditTransactionDO> getUserCreditTransactionsByUserId(long userId, Long lowerId, Integer limit);

    List<UserCreditDailyUsageDO> getOrgCreditUsage(long orgId, int limit);

    @Select("SELECT * FROM user_credit_transaction WHERE org_id=#{orgId} LIMIT #{limit} OFFSET #{offset} ")
    List<UserCreditTransactionDO> getUserCreditTransactionsByOrgId(long orgId, int offset, int limit);

    @Select("SELECT count(*) FROM user_credit_transaction WHERE org_id=#{orgId}")
    int countUserCreditTransactionsByOrgId(long orgId);

    @Insert(
            "<script>INSERT INTO user_credit_transaction (user_id, credit_type, direction, amount, reason_type, reason_sub_type, trace_id) VALUES "
                    + "<foreach collection='transactionDOList' item='transactionDO' separator=','>"
                    + "(#{transactionDO.userId}, #{transactionDO.creditType}, #{transactionDO.direction}, #{transactionDO.amount}, #{transactionDO.reasonType}, #{transactionDO.reasonSubType}, #{transactionDO.traceId})"
                    + "</foreach></script>")
    void batchInsertTransaction(List<UserCreditTransactionDO> transactionDOList);
}
