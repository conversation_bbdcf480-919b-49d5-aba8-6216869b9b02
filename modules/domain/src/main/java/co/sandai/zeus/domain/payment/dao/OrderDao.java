package co.sandai.zeus.domain.payment.dao;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import jakarta.annotation.Resource;
import java.util.Objects;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

@Component
public class OrderDao {

    @Resource
    private OrderMapper orderMapper;

    /**
     * @return true if the order is inserted successfully, false if the order already exists
     */
    public boolean insertWithDuplicateCheck(OrderDO orderDO) {
        try {
            if (Objects.isNull(orderDO.getOrgId())) {
                orderDO.setOrgId(0L);
            }
            if (Objects.isNull(orderDO.getUserId())) {
                orderDO.setUserId(0L);
            }
            orderMapper.createOrder(orderDO);
            return true;
        } catch (DuplicateKeyException e) {
            String outerOrderId = orderDO.getOuterOrderId();
            String outerPlatform = orderDO.getOuterPlatform();
            OrderDO orderDOInDb = orderMapper.queryByOuterOrderIdAndOuterPlatform(outerOrderId, outerPlatform);
            boolean compare = compare(orderDOInDb, orderDO);
            if (!compare) {
                throw new ZeusServiceException(ErrorCode.DbError, "order already exists but not equal");
            }
            return false;
        }
    }

    private boolean compare(OrderDO orderDOInDb, OrderDO orderDO) {
        if (orderDOInDb == null) {
            return false;
        }
        if (!Objects.equals(orderDOInDb.getAmount(), orderDO.getAmount())) {
            return false;
        }
        if (!orderDOInDb.getCurrency().equals(orderDO.getCurrency())) {
            return false;
        }
        if (!orderDOInDb.getFrom().equals(orderDO.getFrom())) {
            return false;
        }
        if (!orderDOInDb.getStatus().equals(orderDO.getStatus())) {
            return false;
        }
        if (!orderDOInDb.getUserId().equals(orderDO.getUserId())) {
            return false;
        }
        return true;
    }
}
