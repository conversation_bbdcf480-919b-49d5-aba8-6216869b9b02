package co.sandai.zeus.domain.credit.dao;

import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class UserCreditTransactionDO {

    private Long id;
    private Long userId;
    private Long orgId;
    private String creditType;
    private String direction;
    private Integer amount;
    private String reasonType;
    private String reasonSubType;
    private String traceId;
    private Boolean deleted;
    // todo panhong 2024/12/12 感觉还是要加一个字段表示一下发生时间。 以应对一些系统延迟的情况？
    private Timestamp createTime;
    private Timestamp modifiedTime;

    public enum DirectionEnum {
        IN,
        OUT
    }

    @AllArgsConstructor
    public enum ReasonTypeEnum {
        /**
         * 包含订阅和单独购买的
         */
        PURCHASED("Purchased"),
        /**
         * 运营奖励下发/活动获取 和 每月免费的
         */
        EARNED("Earned"),
        /**
         * 过期的和所有消费的
         */
        SPENT("Spent"),
        ;
        private String text;

        public static String getTextByName(String name) {
            for (ReasonTypeEnum reasonTypeEnum : ReasonTypeEnum.values()) {
                if (reasonTypeEnum.name().equals(name)) {
                    return reasonTypeEnum.text;
                }
            }
            return null;
        }
    }

    @AllArgsConstructor
    public enum ReasonSubTypeEnum {
        // 每月免费发放credit
        MONTHLY_FREE_CREDIT("Monthly free credit"),
        // VIP订阅包内credit发放
        SUBSCRIPTION_CREDIT("Subscription credit"),
        // 单独购买credit
        PURCHASED_CREDIT("Purchased credit"),
        // 运营奖励下发/活动获取credit
        BONUS_CREDIT("Bonus credit"),

        // 过期的credit，其中包括过期的free credits，subscription credits以及purchased credits
        EXPIRED_CREDITS("Expired credits"),
        // video生成功能的消耗
        VIDEO_GENERATION("Video generation"),
        // video扩写/续写功能的消耗
        VIDEO_EXTENSION("Video extension"),
        // 实时生成功能的消耗
        REAL_TIME_VIDEO_GENERATION("Real time video generation"),
        // 图片生成功能的消耗
        IMAGE_GENERATION("Image generation"),
        // 音频到视频生成功能的消耗
        HUMAN_GENERATION("Human generation"),

        // 音频生成功能的消耗
        AUDIO_GENERATION("Audio generation"),
        ;
        private String text;

        public static String getTextByName(String name) {
            for (ReasonSubTypeEnum typeEnum : ReasonSubTypeEnum.values()) {
                if (typeEnum.name().equals(name)) {
                    return typeEnum.text;
                }
            }
            return null;
        }
    }
}
