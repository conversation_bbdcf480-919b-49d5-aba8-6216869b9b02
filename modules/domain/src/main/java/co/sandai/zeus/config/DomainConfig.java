package co.sandai.zeus.config;

import co.sandai.zeus.domain.task.dao.type_handler.TaskCropAreaTypeHandler;
import co.sandai.zeus.domain.task.dao.type_handler.TaskExtraInferArgsTypeHandler;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = "co.sandai.zeus.domain", annotationClass = Mapper.class)
public class DomainConfig {
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            typeHandlerRegistry.register(TaskCropAreaTypeHandler.class);
            typeHandlerRegistry.register(TaskExtraInferArgsTypeHandler.class);
        };
    }
}
