package co.sandai.zeus.domain.share.dao;

import co.sandai.zeus.domain.task.dao.Task;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface ShareMapper {
    @Insert("INSERT INTO share (id, task_id, user_id)" + "VALUE (#{share.id}, #{share.taskId}, #{share.userId})")
    void insertTaskShare(@Param("share") Share share);

    @Insert("INSERT INTO share (id, content, content_hash)"
            + "VALUE (#{share.id}, #{share.content}, #{share.contentHash})")
    void insertGenerationShare(@Param("share") Share share);

    @Select("DELETE from share WHERE task_id = #{taskId}")
    void deleteShareByTaskId(long taskId);

    @Select("SELECT * FROM share WHERE task_id=#{taskId}")
    Share getShareByTaskId(long taskId);

    @Select("SELECT * FROM share WHERE id=#{id}")
    Share getShareById(long id);

    @Select("SELECT * FROM share WHERE content_hash=#{contentHash} limit 1")
    Share getShareByContentHash(String contentHash);

    @Select("SELECT task.* FROM share LEFT JOIN task ON task.id = share.task_id "
            + "WHERE share.user_id=#{userId} ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<Task> getUserSharedTasks(long userId, int limit, int offset);

    @Select("SELECT task.* FROM share LEFT JOIN task ON task.id = share.task_id "
            + "WHERE task.deleted=0 ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<Task> getAllSharedTasks(int limit, int offset);

    @Select("SELECT task.* FROM share LEFT JOIN task ON task.id = share.task_id "
            + "WHERE share.is_staff_pick=1 AND task.deleted=0 "
            + "ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<Task> getStaffPickedSharedTasks(int limit, int offset);

    @Select("SELECT COUNT(*) FROM share LEFT JOIN task ON task.id = share.task_id "
            + "WHERE share.is_staff_pick=1 AND task.deleted=0 ")
    int getCountOfStaffPickedSharedTasks();
}
