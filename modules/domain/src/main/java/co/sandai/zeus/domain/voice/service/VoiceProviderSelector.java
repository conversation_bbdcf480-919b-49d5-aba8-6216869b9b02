package co.sandai.zeus.domain.voice.service;

/**
 * Interface for selecting the appropriate voice provider based on text content
 */
public interface VoiceProviderSelector {

    /**
     * Select the appropriate voice provider name based on text content
     * @param text Text to analyze for provider selection
     * @return Provider name to use for this text
     */
    String selectProviderForText(String text);
}
