package co.sandai.zeus.domain.favorite.service;

import co.sandai.zeus.domain.favorite.dao.Favorite;
import co.sandai.zeus.domain.favorite.dao.TaskFavoriteMapper;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.infra.IDGenerator;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TaskFavoriteService {
    @Autowired
    private TaskFavoriteMapper taskFavoriteMapper;

    @Autowired
    private IDGenerator idGenerator;

    public long addFavoriteTask(long userId, long taskId) {
        if (isFavoriteTask(userId, taskId)) {
            return 0;
        }

        long id = idGenerator.getNextId();
        Favorite fav = Favorite.builder().id(id).userId(userId).taskId(taskId).build();
        taskFavoriteMapper.insertFavorite(fav);
        return id;
    }

    public boolean isFavoriteTask(long userId, long taskId) {
        return taskFavoriteMapper.userHasFavoriteTask(userId, taskId) > 0;
    }

    public List<Task> getFavoriteTasks(long userId, int offset, int limit) {
        return taskFavoriteMapper.getUserFavoriteTasks(userId, limit, offset);
    }

    public void deleteFavoriteTask(long userId, long taskId) {
        if (!isFavoriteTask(userId, taskId)) {
            return;
        }
        taskFavoriteMapper.deleteUserFavoriteTask(userId, taskId);
    }
}
