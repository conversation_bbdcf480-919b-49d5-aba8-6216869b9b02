package co.sandai.zeus.domain.voice.service.provider;

import co.sandai.zeus.domain.voice.dto.VoiceDTO;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * Interface for voice providers (e.g., ElevenLabs, Minimax)
 *
 * This interface defines API for voice provider services and is NOT a MyBatis mapper.
 */
@Component
public interface VoiceProvider {
    /**
     * Get the name of this voice provider
     * @return Provider name
     */
    String getProviderName();

    /**
     * Get available voices from the provider
     * @return List of voice DTOs
     */
    List<VoiceDTO> getVoices();

    /**
     * Create a new voice
     * @param name Voice name
     * @param description Voice description
     * @param files Audio files for voice creation
     * @return Created voice DTO
     */
    VoiceDTO createVoice(String name, String description, List<MultipartFile> files);

    /**
     * Convert text to speech
     * @param voiceId ID of the voice to use
     * @param text Text to convert to speech
     * @return Audio data as bytes
     */
    byte[] textToSpeech(String voiceId, String text);

    /**
     * Delete a voice
     * @param voiceId ID of the voice to delete
     * @return true if deletion was successful, false otherwise
     */
    boolean deleteVoice(String voiceId);

    /**
     * Convert speech to text
     * @param audioData Audio data as bytes to convert to text
     * @param language Optional language code (e.g., "en", "zh"). If null, auto-detect language
     * @return Transcribed text from the audio
     */
    String speechToText(byte[] audioData, String language);
}
