package co.sandai.zeus.domain.platform.feedback.dao;

import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CustomApiPackageRequest {
    private Long id;
    private String company;
    private String business;
    private String phone;
    private String detail;
    private Long userId;
    private Long orgId;
    private LocalDateTime createTime;

    public String toLarkMessage() {
        return String.format("ID: %s\n", id)
                + String.format("UserID: %s\n", userId)
                + String.format("OrganizationID: %s\n", orgId)
                + String.format("Company: %s\n", company)
                + String.format("Business: %s\n", business)
                + String.format("Phone: %s\n", phone)
                + String.format("Detail: %s\n", detail);
    }
}
