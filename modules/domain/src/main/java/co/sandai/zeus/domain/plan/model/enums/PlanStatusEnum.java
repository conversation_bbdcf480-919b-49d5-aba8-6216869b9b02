package co.sandai.zeus.domain.plan.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PlanStatusEnum {
    ACTIVE("ACTIVE"),
    DEPRECATED("DEPRECATED"),
    /**
     * 完全下线，线上没有用户属于这种plan的时候才能下线
     */
    OFFLINE("OFFLINE"),
    ;

    private String code;

    public PlanStatusEnum findbyCode(String code) {
        if (code == null) {
            return null;
        }

        for (PlanStatusEnum planStatusEnum : PlanStatusEnum.values()) {
            if (planStatusEnum.getCode().equals(code)) {
                return planStatusEnum;
            }
        }
        return null;
    }
}
