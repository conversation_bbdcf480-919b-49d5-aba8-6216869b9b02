package co.sandai.zeus.domain.task.dao;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TaskChunkDO {

    private long id;
    private Long taskId;
    private String prompt;
    private Float duration;
    private String enhancedPrompt;
    private Integer index; // 在这个任务中的index, 从0开始
    private TaskChunkStatus status;
    private Long audioAssetId; // TODO 这种扩展性很差，是否要改成Asset的list

    public float getDurationByTaskType(TaskType taskType) {
        if (taskType == TaskType.A2V) {
            return (float) Math.ceil(this.duration);
        }
        return this.duration;
    }
}
