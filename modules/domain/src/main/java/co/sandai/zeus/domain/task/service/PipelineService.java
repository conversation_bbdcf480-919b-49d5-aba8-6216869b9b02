package co.sandai.zeus.domain.task.service;

import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.common.WhiteListService;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.dao.UserCreditTransactionDO;
import co.sandai.zeus.domain.task.dao.PipelineTask;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskChunkDO;
import co.sandai.zeus.domain.task.dao.TaskChunkStatus;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.dao.mapper.PipelineTaskMapper;
import co.sandai.zeus.infra.IDGenerator;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class PipelineService {

    @Resource
    private PipelineTaskMapper pipelineTaskMapper;

    @Resource
    private TaskService taskService;

    @Resource
    private AssetService assetService;

    @Resource
    private IDGenerator idGenerator;

    @Resource
    private UserCreditService userCreditService;

    @Autowired
    private GenerationService generationService;

    @Autowired
    private WhiteListService whiteListService;

    /**
     * Create a pipeline task with sequence ordering
     *
     * @param pipelineId ID of the pipeline
     * @param taskId ID of the task
     * @param taskIndex index of the task in the pipeline sequence
     * @return the created PipelineTask
     */
    @Transactional(rollbackFor = Throwable.class)
    public PipelineTask createPipelineTask(Long pipelineId, Long taskId, Integer taskIndex) {
        PipelineTask pipelineTask = PipelineTask.builder()
                .id(idGenerator.getNextId())
                .pipelineId(pipelineId)
                .taskId(taskId)
                .taskIndex(taskIndex)
                .build();

        pipelineTaskMapper.insert(pipelineTask);
        return pipelineTask;
    }

    /**
     * Get a pipeline task by its task ID
     *
     * @param taskId ID of the task
     * @return the pipeline task or null if not found
     */
    public PipelineTask getPipelineTaskByTaskId(Long taskId) {
        return pipelineTaskMapper.getByTaskId(taskId);
    }

    /**
     * Get all pipeline tasks for a specific pipeline ID
     *
     * @param pipelineId ID of the pipeline
     * @return list of pipeline tasks ordered by task_index
     */
    public List<PipelineTask> getPipelineTasksByPipelineId(Long pipelineId) {
        return pipelineTaskMapper.getByPipelineId(pipelineId);
    }

    /**
     * Get all task IDs for a specific pipeline ID
     *
     * @param pipelineId ID of the pipeline
     * @return list of task IDs ordered by task_index
     */
    public List<Long> getTaskIdsByPipelineId(Long pipelineId) {
        return pipelineTaskMapper.getTaskIdsByPipelineId(pipelineId);
    }

    /**
     * 第一次生成task， 需要扣除用户的credit，并且要审核所有文本才能提交到athena推理
     * @param task            The first task in the pipeline
     * @param sourceAssets    Source assets for the task
     * @param taskChunks      Task chunks for the task
     * @param enhancementType Type of enhancement to apply
     * @param creditAmount
     * @param userId
     * @throws IOException if there's an error submitting the task
     *
     * @Transactional: 如果有异常抛出，credit扣减事务回滚
     */
    @Transactional(rollbackFor = Throwable.class)
    public void processFirstTime(
            Task task,
            List<Asset> sourceAssets,
            List<TaskChunkDO> taskChunks,
            String enhancementType,
            String queue,
            int creditAmount,
            Long userId,
            List<String> allPrompts)
            throws IOException {
        // 此处不考虑后续提交任务失败的情况
        userCreditService.deductCredit(
                userId,
                null,
                creditAmount,
                UserCreditTransactionDO.ReasonTypeEnum.SPENT,
                UserCreditTransactionDO.ReasonSubTypeEnum.VIDEO_GENERATION);

        // Submit task to inference
        taskService.submitTaskAsAthenaJob(task, sourceAssets, taskChunks, enhancementType, queue, allPrompts);

        // Update task status to Running
        taskService.updateTaskStatus(task.getId(), TaskStatus.Running);
    }

    /**
     * Process the next task in a pipeline when a task is completed
     *
     * @param task The completed task object
     * @param currentPipelineTask The current pipeline task object
     * @throws IOException if there's an error submitting the next task
     */
    @Transactional(rollbackFor = Throwable.class)
    public void processNextTaskInPipeline(Task task, PipelineTask currentPipelineTask) throws IOException {
        // Get next task ID based on pipeline ID and current task index
        Integer nextTaskIndex = currentPipelineTask.getTaskIndex() + 1;
        Long nextTaskId = pipelineTaskMapper.getTaskIdByPipelineIdAndTaskIndex(
                currentPipelineTask.getPipelineId(), nextTaskIndex);
        if (nextTaskId == null) {
            log.info("Pipeline {} completed, no more tasks to process", currentPipelineTask.getPipelineId());
            return;
        }

        // Use the provided task object directly
        // Check if current task was successful
        if (task.getStatus() != TaskStatus.Success) {
            log.error(
                    "Task {} failed, updating status of remaining tasks in pipeline {}",
                    task.getId(),
                    currentPipelineTask.getPipelineId());

            // Get all remaining tasks in the pipeline and mark them as failed
            List<Long> taskIds = getTaskIdsByPipelineId(currentPipelineTask.getPipelineId());
            for (Long id : taskIds) {
                Task remainingTask = taskService.getTaskById(id);
                // Only update tasks that haven't started yet
                if (remainingTask.getStatus() == TaskStatus.Pending) {
                    taskService.updateTaskStatus(id, TaskStatus.Fail);
                }
            }
            return;
        }

        // Get the result video of the current task
        Asset sourceAsset = assetService.getAssetById(task.getResultVideoId());
        if (sourceAsset == null) {
            log.error("Source asset not found for task {}", task.getId());
            return;
        }

        // Get the next task
        Task nextTask = taskService.getTaskById(nextTaskId);
        nextTask.setSourceVideoTaskId(task.getId());
        taskService.updateTaskSourceVideoId(nextTaskId, sourceAsset.getId());

        // Get or create the task chunks for the next task
        List<TaskChunkDO> nextTaskChunks;

        if (nextTask.getDuration() != 0) {
            // Create a single task chunk directly from the task
            TaskChunkDO chunk = new TaskChunkDO();
            //            chunk.setId(idGenerator.getNextId());
            chunk.setTaskId(nextTask.getId());
            chunk.setPrompt(nextTask.getPrompt());
            chunk.setIndex(0);
            chunk.setDuration(nextTask.getDuration());
            chunk.setStatus(TaskChunkStatus.Pending);
            nextTaskChunks = List.of(chunk);
        } else {
            // Multiple chunks case - need to fetch from the database
            nextTaskChunks = taskService.getTaskChunkByTaskId(nextTask.getId());
        }

        // Set the status of the next task to Running
        taskService.updateTaskStatus(nextTask.getId(), TaskStatus.Running);

        // 现在还没有用到source里的prompt
        // pipeline第一次提交task的时候已经审核过prompt，这里不再需要审核，传空集合
        taskService.submitTaskAsAthenaJob(
                nextTask,
                List.of(sourceAsset),
                nextTaskChunks,
                nextTask.getExtraInferArgs().getEnhancementType(),
                generationService.decideQueue(task.getUserId(), whiteListService.isSuperUser(task.getUserId())),
                Collections.emptyList());

        log.info("Started next task {} in pipeline {}", nextTaskId, currentPipelineTask.getPipelineId());
    }
}
