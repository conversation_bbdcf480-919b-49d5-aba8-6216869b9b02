package co.sandai.zeus.domain.voice.dao.mapper;

import co.sandai.zeus.domain.voice.dao.Voice;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface VoiceMapper {

    /**
     * Insert a new voice record
     * @param voice Voice entity to insert
     */
    void insertVoice(@Param("voice") Voice voice);

    /**
     * Get voices by user ID
     * @param userId User ID
     * @return List of voices
     */
    List<Voice> getVoicesByUserId(@Param("userId") long userId);

    /**
     * Get a voice by its ID
     * @param id Voice ID
     * @return Voice entity
     */
    Voice getVoiceById(@Param("id") Long id);

    /**
     * Get voice by provider voice ID
     * @param voiceId Provider's voice ID
     * @return Voice entity
     */
    Voice getVoiceByVoiceId(@Param("voiceId") String voiceId);

    /**
     * Get voice by provider voice ID regardless of deleted status
     * @param voiceId Provider's voice ID
     * @return Voice entity
     */
    Voice getVoiceByVoiceIdIncludingDeleted(@Param("voiceId") String voiceId);

    /**
     * Count voices by user ID
     * @param userId User ID
     * @return Count of voices
     */
    int getVoiceCountByUserId(@Param("userId") Long userId);

    /**
     * Get voices by provider name
     * @param provider Provider name
     * @param includeDeleted Whether to include deleted voices
     * @return List of voices
     */
    List<Voice> getVoicesByProvider(
            @Param("provider") String provider, @Param("includeDeleted") boolean includeDeleted);

    /**
     * Update voice details
     * @param id Voice ID
     * @param name New name
     * @param description New description
     */
    void updateVoice(@Param("id") Long id, @Param("name") String name, @Param("description") String description);

    /**
     * Update voice deleted status
     * @param id Voice ID
     * @param deleted Deleted status
     */
    void updateVoiceDeleted(@Param("id") Long id, @Param("deleted") boolean deleted);

    /**
     * Delete voice by ID
     * @param id Voice ID
     */
    void deleteVoiceById(@Param("id") Long id);

    /**
     * Update voice preview URL
     * @param id Voice ID
     * @param previewUrl New preview URL
     */
    void updateVoicePreviewUrl(@Param("id") Long id, @Param("previewUrl") String previewUrl);

    /**
     * Update voice verified languages
     * @param id Voice ID
     * @param verifiedLanguages New verified languages JSON
     */
    void updateVoiceVerifiedLanguages(@Param("id") Long id, @Param("verifiedLanguages") String verifiedLanguages);

    /**
     * Update voice gender
     * @param id Voice ID
     * @param gender New gender
     */
    void updateVoiceGender(@Param("id") Long id, @Param("gender") String gender);

    /**
     * Update all voice fields except ID and voice_id
     * @param voice Updated voice entity
     */
    void updateVoiceAllFields(@Param("voice") Voice voice);
}
