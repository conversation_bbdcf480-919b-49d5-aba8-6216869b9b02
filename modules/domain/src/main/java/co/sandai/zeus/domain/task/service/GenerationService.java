package co.sandai.zeus.domain.task.service;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.utils.StreamUtil;
import co.sandai.zeus.config.SystemConfig;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.auth.service.AuthService;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.dao.UserCreditTransactionDO;
import co.sandai.zeus.domain.plan.UserPlanAuthService;
import co.sandai.zeus.domain.task.HighQualityStrategyUtils;
import co.sandai.zeus.domain.task.dao.ModerationFailType;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskChunkDO;
import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import co.sandai.zeus.domain.task.dao.TaskType;
import co.sandai.zeus.domain.task.dao.mapper.TaskChunkMapper;
import co.sandai.zeus.infra.infer.InferClient;
import co.sandai.zeus.infra.prompt.PromptService;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class GenerationService {

    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private UserPlanAuthService userPlanAuthService;

    /**
     * 内容审核失败异常
     */
    public static class ModerationFailedException extends RuntimeException {
        private Task task;
        private String jobId;

        public ModerationFailedException(String message, Task task, String jobId) {
            super(message);
            this.task = task;
            this.jobId = jobId;
        }

        public Task getTask() {
            return task;
        }

        public String getJobId() {
            return jobId;
        }
    }

    @Resource
    private PromptService promptService;

    @Resource
    private TaskService taskService;

    @Autowired
    private InferClient inferClient;

    @Resource
    private TaskChunkMapper taskChunkMapper;

    @Resource
    private AuthService authService;

    @Autowired
    private UserCreditService userCreditService;

    @Autowired
    private ApplicationContext applicationContext;

    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public Task createTaskAndChunk(Task task, List<TaskChunkDO> outputChunks) {
        task = taskService.createTask(task);

        // TODO 这块看看后续要不要统一一下，把steps都放在task里实现整体看更简单一点
        if (outputChunks.size() > 1) {
            for (TaskChunkDO chunk : outputChunks) {
                chunk.setTaskId(task.getId());
            }
            // 保存taskChunk
            for (TaskChunkDO outputChunk : outputChunks) {
                // 先简单写，遍历插入
                taskChunkMapper.insert(outputChunk);
            }
        }
        return task;
    }

    /**
     * 提交生成任务，并处理可能的审核失败
     */
    public void submitGeneration(
            Long userId,
            Long orgId,
            String enhancementType,
            Task task,
            List<Asset> sourceAssets,
            List<TaskChunkDO> outputChunks,
            String queue,
            boolean deductCredits)
            throws IOException {

        Asset asset = sourceAssets.getFirst();
        if (asset.getMediaType().isVideo()) {
            task.setSourceVideoId(asset.getId());
            if (task.getType() == TaskType.ExtendDuration && asset.getSource() == AssetSource.Upload && orgId == null) {
                task.getExtraInferArgs().setEnableInputVideoToTs(true);
            }
        } else if (asset.getMediaType().isImage()) {
            task.setSourceImageId(asset.getId());
        }

        float maxDuration = 0;
        for (TaskChunkDO chunk : outputChunks) {
            maxDuration = Math.max(maxDuration, chunk.getDuration());
        }
        checkTaskDuration(userId, maxDuration);

        if (outputChunks.size() == 1) {
            task.setPrompt(outputChunks.getFirst().getPrompt());
            task.setDuration(outputChunks.getFirst().getDuration());
            Long audioAssetId = outputChunks.getFirst().getAudioAssetId();
            if (audioAssetId != null) {
                task.setSourceAudioId(audioAssetId);
            }
        } else {
            task.setPrompt("");
        }
        GenerationService proxy = applicationContext.getBean(GenerationService.class);
        Task finalTask = proxy.createTaskAndChunk(task, outputChunks);
        try {
            // 执行事务: 创建任务、提交推理、内容审核
            // 使用ApplicationContext获取代理对象以确保事务生效
            proxy.submitAndModerateTask(
                    userId, orgId, enhancementType, finalTask, sourceAssets, outputChunks, queue, deductCredits);
        } catch (ModerationFailedException e) {
            // 审核失败处理
            log.warn("Task moderation failed for task {}", e.getTask().getId());

            // 更新审核结果
            taskService.updateTaskModerationResult(finalTask.getId(), false, ModerationFailType.PROMPT, null);

            // 取消推理任务
            try {
                inferClient.cancelJob(e.getJobId());
                log.info("Successfully canceled inference job {}", e.getJobId());
            } catch (Exception ex) {
                log.error("Failed to cancel inference job {}: {}", e.getJobId(), ex.getMessage());
            }

            // 抛出异常给调用者
            throw new ZeusServiceException(
                    ErrorCode.InvalidParameters,
                    "Your prompt contains inappropriate content. Please modify it and try again.");
        } catch (Exception e) {
            // 非风控原因导致的失败，如扣款失败等，需要删除任务记录
            log.error("Task {} failed before moderation, deleting task record", finalTask.getId(), e);
            try {
                taskService.deleteTaskById(finalTask.getId());
            } catch (Exception deleteEx) {
                log.error("Failed to delete task {} after exception: {}", finalTask.getId(), deleteEx.getMessage());
            }
            // 继续向上抛出原始异常
            throw e;
        }
    }

    /**
     * 创建并提交任务（事务方法）
     * 在事务内创建任务，提交到Athena作业，进行审核，最后只有审核通过才扣减积分
     */
    @Transactional(rollbackFor = Throwable.class)
    public void submitAndModerateTask(
            Long userId,
            Long orgId,
            String enhancementType,
            Task task,
            List<Asset> sourceAssets,
            List<TaskChunkDO> outputChunks,
            String queue,
            boolean deductCredits)
            throws IOException {

        if (deductCredits) {
            int creditAmount = creditStrategy(task, outputChunks);
            UserCreditTransactionDO.ReasonSubTypeEnum reasonSubType =
                    UserCreditTransactionDO.ReasonSubTypeEnum.VIDEO_GENERATION;
            if (task.getTaskSource() == TaskSourceEnum.HUMAN) {
                reasonSubType = UserCreditTransactionDO.ReasonSubTypeEnum.HUMAN_GENERATION;
            }
            userCreditService.deductCredit(
                    userId, orgId, creditAmount, UserCreditTransactionDO.ReasonTypeEnum.SPENT, reasonSubType);
        }

        // 提交任务到推理服务
        String jobId = taskService.submitTaskAsAthenaJob(task, sourceAssets, outputChunks, enhancementType, queue);

        // 获取提示词并进行内容审核
        String promptToModerate = task.getPrompt();
        boolean passed = taskService.moderateTaskPrompt(task.getId(), userId, orgId, jobId, promptToModerate);

        if (!passed) {
            log.warn("Task {} prompt moderation failed", task.getId());

            // 抛出异常触发事务回滚，传递审核相关信息
            throw new ModerationFailedException("Prompt content moderation failed", task, jobId);
        }

        // 内容审核通过，标记任务状态
        task.setModerationPassed(true);
    }

    public String decideQueue(long userId, boolean superUser) {
        String queue;
        boolean highPriorityGenerations = authService.checkAuth(userId, "high_priority_generations");
        if (superUser) {
            // 如果用户在白名单中，使用高优先级队列
            queue = "high";
        } else if (highPriorityGenerations) {
            // 如果用户有高优先级的权限，直接使用normal优先级队列
            queue = "normal";
        } else if (systemConfig.getNewUserFirstTaskQueue() != null
                && taskService.getTotalTaskCountByUserId(userId) == 0) {
            queue = systemConfig.getNewUserFirstTaskQueue();
            log.info("user first task, user id: {}, use queue: {}", userId, queue);
        } else {
            queue = "low";
        }
        return queue;
    }

    /**
     * 根据任务类型和输出块计算积分消耗和消费类型
     *
     * @param task 任务对象
     * @param outputChunks 输出块列表
     * @return 积分策略结果，包含积分数量和消费类型
     */
    public static int creditStrategy(Task task, List<TaskChunkDO> outputChunks) {
        TaskExtraInferArgs extraInferArgs = task.getExtraInferArgs();
        if (task.getType() == TaskType.A2V) {
            float totalDuration =
                    StreamUtil.of(outputChunks).map(TaskChunkDO::getDuration).reduce(0f, Float::sum);
            String resolution = extraInferArgs.getResolution();
            String model = task.getModel();
            // 540p: $1/mins
            // 720p: $2/mins
            float price = resolution.equals("540p") ? (float) 100 / 60 : (float) 200 / 60;
            float totalPrice = totalDuration * price;
            if (Objects.nonNull(model) && (model.equals("magi-gaga-14b-8") || model.equals("test-avatar-8"))) {
                // 14 B 模型， https://j0yswlgboxz.feishu.cn/docx/Zq0odesmtoNgZLxMEMRcm3r6nDd
                totalPrice = (float) (totalPrice * 2.5);
            }

            int result = Math.max(1, (int) Math.floor(totalPrice));
            log.info(
                    "Human Task {}, model: {}, resolution: {}, totalDuration: {}, credit strategy result: {}",
                    task.getId(),
                    model,
                    resolution,
                    totalDuration,
                    result);

            return result;
        }

        // 其他任务使用原来的计费逻辑
        int creditAmount = StreamUtil.of(outputChunks)
                        .map(TaskChunkDO::getDuration)
                        .reduce(0f, Float::sum)
                        .intValue()
                * 5;
        creditAmount *= HighQualityStrategyUtils.creditTimesAccordingToQuality(extraInferArgs);
        return creditAmount;
    }

    private void checkTaskDuration(Long userId, float duration) {
        if (!systemConfig.isGaGa()) {
            return;
        }
        if (duration > 180) {
            throw ZeusServiceException.badRequest("Duration exceeds the limit: 180s");
        }
        if (duration > 60 && !userPlanAuthService.hasAuth(userId, "up_to_180s_video_length")) {
            throw ZeusServiceException.upgradeRequired();
        }
        if (duration > 30 && !userPlanAuthService.hasAuth(userId, "up_to_60s_video_length")) {
            throw ZeusServiceException.upgradeRequired();
        }
    }
}
