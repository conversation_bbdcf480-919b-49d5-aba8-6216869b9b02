package co.sandai.zeus.domain.credit;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.log.trace.TraceContext;
import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.credit.config.CreditPackage;
import co.sandai.zeus.domain.credit.dao.*;
import co.sandai.zeus.domain.credit.enums.CreditTypeEnum;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.model.MemberShip;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.platform.credit.dto.PlatformCreditPackage;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Component
@Slf4j
public class UserCreditService {

    @Resource
    private UserCreditMapper userCreditMapper;

    @Resource
    private UserCreditAndTransactionDao userCreditAndTransactionDao;

    @Autowired
    private UserCreditTransactionMapper userCreditTransactionMapper;

    @Autowired
    private PlanConfigService planConfigService;

    /**
     * 更新订阅场景请勿调用此方法。 此方法不考虑补发credit情况
     * 注意幂等性
     * @param userId 用户 ID
     * @param plan 用于获取方案要发放的credit数量
     * @param payTime 支付时间
     */
    public void issueSubscriptionCredit(Long userId, Plan plan, LocalDateTime payTime) {
        Optional<Integer> optionalCreditNum =
                Optional.ofNullable(plan).map(Plan::getMemberShip).map(MemberShip::getSubscriptionCreditNum);
        if (optionalCreditNum.isEmpty()) {
            LogUtil.errorf(log, "creditNum is null, plan: {0}", plan);
            return;
        }
        userCreditAndTransactionDao.issueSubscriptionCredit(userId, optionalCreditNum.get(), payTime);
    }

    /**
     * 增加一个新的subscription credit, 原来的都置为tempCredit不再支持renew
     */
    public void issueNewSubscriptionCredit(Long userId, Plan plan, LocalDateTime payTime) {
        Optional<Integer> optionalCreditNum =
                Optional.ofNullable(plan).map(Plan::getMemberShip).map(MemberShip::getSubscriptionCreditNum);
        if (optionalCreditNum.isEmpty()) {
            LogUtil.errorf(log, "creditNum is null, plan: {0}", plan);
            return;
        }
        userCreditAndTransactionDao.issueNewSubscriptionCredit(userId, optionalCreditNum.get(), payTime);
    }

    public void issueSubscriptionDiffCredit(Long userId, Plan currentPlan, Plan oldPlan) {
        if (oldPlan.getPlanCode().equals(currentPlan.getPlanCode())) {
            LogUtil.infof(log, "oldPlan and currentPlan are the same, maybe from a retry request, userId: {0}", userId);
            return;
        }
        Optional<Integer> optionalCreditNum =
                Optional.of(currentPlan).map(Plan::getMemberShip).map(MemberShip::getSubscriptionCreditNum);
        if (optionalCreditNum.isEmpty()) {
            LogUtil.errorf(log, "creditNum is null, currentPlan: {0}", currentPlan);
            return;
        }
        Optional<Integer> optionalOldCreditNum =
                Optional.of(oldPlan).map(Plan::getMemberShip).map(MemberShip::getSubscriptionCreditNum);
        if (optionalOldCreditNum.isEmpty()) {
            LogUtil.errorf(log, "creditNum is null, oldPlan: {0}", oldPlan);
            return;
        }
        userCreditAndTransactionDao.issueSubscriptionDiffCredit(
                userId, optionalCreditNum.get() - optionalOldCreditNum.get());
    }

    /**
     * @param userId
     */
    public void issueFreeCredit(Long userId) {
        LocalDateTime payTime = TimeUtil.utcTime();
        userCreditAndTransactionDao.issueFreeCredit(userId, planConfigService.getFreeCreditAmount(), payTime);
    }

    public long getUserCreditSum(long userId) {
        List<UserCreditDO> userCreditDOs = queryUserCredit(userId, null);
        return userCreditDOs.stream().mapToLong(UserCreditDO::getAmount).sum();
    }

    public List<UserCreditDO> queryUserCredit(long userId, CreditTypeEnum creditType) {
        return userCreditMapper.getNotExpiredUserCredits(userId, creditType == null ? null : creditType.name());
    }

    public List<UserCreditTransactionDO> queryUserCreditTransaction(long userId, long lowerId, int limit) {
        return userCreditTransactionMapper.getUserCreditTransactionsByUserId(userId, lowerId, limit);
    }

    public void issuePurchasedUserCredit(
            Long userId, String requestId, CreditPackage creditPackage, LocalDateTime payTime) {
        userCreditAndTransactionDao.issuePurchasedUserCredit(userId, requestId, creditPackage.getAmount(), payTime);
    }

    public void issuePurchasedOrgCredit(
            Long orgId, String requestId, PlatformCreditPackage creditPackage, LocalDateTime payTime) {
        userCreditAndTransactionDao.issuePurchasedOrgCredit(orgId, requestId, creditPackage.getAmount(), payTime);
    }

    public void renewSubscriptionCredit(Long userId, Plan plan, LocalDateTime payTime) {
        Optional<Integer> optionalCreditNum =
                Optional.ofNullable(plan).map(Plan::getMemberShip).map(MemberShip::getSubscriptionCreditNum);
        if (optionalCreditNum.isEmpty()) {
            LogUtil.errorf(log, "creditNum is null, plan: {0}", plan);
            return;
        }
        userCreditAndTransactionDao.renewSubscriptionCredit(userId, optionalCreditNum.get(), payTime);
    }

    /**
     * 首次发放用户（新注册用户）不适用此方法
     */
    @Transactional(rollbackFor = Throwable.class)
    public void batchRenewCredit(List<Long> userIds, CreditTypeEnum creditTypeEnum, int creditAmount) {
        List<UserCreditDO> userCreditDOs = userCreditMapper.batchGetUserCredit(userIds, creditTypeEnum.name(), false);

        if (CollectionUtils.isEmpty(userCreditDOs)) {
            LogUtil.warnf(log, "No user credit found for userIds: {0}, creditType: {1}", userIds, creditTypeEnum);
            return;
        }

        // 更新user_credit表
        userCreditMapper.batchUpdateUserCreditByUserId(
                userIds,
                creditTypeEnum.name(),
                creditAmount,
                // 任务定时时间从00:00以后开始，不设置在23:50+的时间，否则这里误差会有1天。
                TimeUtil.utcTime()
                        .plusMonths(1)
                        .withHour(0)
                        .withMinute(0)
                        .withSecond(0)
                        .withNano(0));
        // 插入过期记录
        List<UserCreditTransactionDO> transactionDOList = new ArrayList<>();
        for (UserCreditDO userCreditDO : userCreditDOs) {
            if (userCreditDO.getAmount() <= 0) {
                continue;
            }

            UserCreditTransactionDO transaction = new UserCreditTransactionDO();
            transaction.setUserId(userCreditDO.getUserId());
            transaction.setCreditType(creditTypeEnum.name());
            transaction.setDirection(UserCreditTransactionDO.DirectionEnum.OUT.name());
            transaction.setAmount(Math.toIntExact(userCreditDO.getAmount()));
            transaction.setReasonType(UserCreditTransactionDO.ReasonTypeEnum.SPENT.name());
            transaction.setReasonSubType(UserCreditTransactionDO.ReasonSubTypeEnum.EXPIRED_CREDITS.name());
            transaction.setTraceId(TraceContext.getTraceId());
            transaction.setDeleted(false);

            transactionDOList.add(transaction);
        }
        userCreditTransactionMapper.batchInsertTransaction(transactionDOList);

        // 插入发放记录
        List<UserCreditTransactionDO> issueCreditTransactionDos = new ArrayList<>();
        for (UserCreditDO userCreditDO : userCreditDOs) {
            UserCreditTransactionDO transaction = new UserCreditTransactionDO();
            transaction.setUserId(userCreditDO.getUserId());
            transaction.setCreditType(creditTypeEnum.name());
            transaction.setDirection(UserCreditTransactionDO.DirectionEnum.IN.name());
            transaction.setAmount(planConfigService.getFreeCreditAmount());
            transaction.setReasonType(UserCreditTransactionDO.ReasonTypeEnum.EARNED.name());
            transaction.setReasonSubType(UserCreditTransactionDO.ReasonSubTypeEnum.MONTHLY_FREE_CREDIT.name());
            transaction.setTraceId(TraceContext.getTraceId());
            transaction.setDeleted(false);

            issueCreditTransactionDos.add(transaction);
        }
        userCreditTransactionMapper.batchInsertTransaction(issueCreditTransactionDos);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    public void batchRenewCredit(List<UserPlanDO> userPlans) {
        List<Long> userIds = userPlans.stream().map(UserPlanDO::getUserId).collect(Collectors.toList());

        // 这里不需要加expire_time条件，因为这里给的userId一定是需要去renew的
        CreditTypeEnum creditTypeEnum = CreditTypeEnum.SUBSCRIPTION;
        List<UserCreditDO> userCreditDOInDbList =
                userCreditMapper.batchGetUserCredit(userIds, CreditTypeEnum.SUBSCRIPTION.name(), false);
        Map<Long, UserPlanDO> userPlanMap =
                userPlans.stream().collect(Collectors.toMap(UserPlanDO::getUserId, userPlan -> userPlan));

        Map<String, Plan> planMap = planConfigService.getUsablePlanList().stream()
                .collect(Collectors.toMap(Plan::getPlanCode, plan -> plan));

        // 更新user_credit表
        List<UserCreditDO> newUserCreditDOs = new ArrayList<>();
        for (UserCreditDO userCreditDO : userCreditDOInDbList) {
            UserPlanDO userPlanDO = userPlanMap.get(userCreditDO.getUserId());
            Plan plan = planMap.get(userPlanDO.getPlanCode());

            UserCreditDO newUserCreditDO = new UserCreditDO();
            newUserCreditDO.setId(userCreditDO.getId());

            newUserCreditDO.setAmount((long) plan.getMemberShip().getSubscriptionCreditNum());
            newUserCreditDO.setExpireTime(TimeUtil.addMonths(userCreditDO.getExpireTime(), 1));
            newUserCreditDO.setCreditType(creditTypeEnum.name());

            newUserCreditDOs.add(newUserCreditDO);
        }
        userCreditMapper.batchUpdateUserCreditById(newUserCreditDOs);

        // 插入过期记录
        List<UserCreditTransactionDO> transactionDOList = new ArrayList<>();
        for (UserCreditDO userCreditDO : userCreditDOInDbList) {
            if (userCreditDO.getAmount() <= 0) {
                continue;
            }
            UserCreditTransactionDO transaction = new UserCreditTransactionDO();
            transaction.setUserId(userCreditDO.getUserId());
            transaction.setCreditType(creditTypeEnum.name());
            transaction.setDirection(UserCreditTransactionDO.DirectionEnum.OUT.name());
            transaction.setAmount(Math.toIntExact(userCreditDO.getAmount()));
            transaction.setReasonType(UserCreditTransactionDO.ReasonTypeEnum.SPENT.name());
            transaction.setReasonSubType(UserCreditTransactionDO.ReasonSubTypeEnum.EXPIRED_CREDITS.name());
            transaction.setTraceId(TraceContext.getTraceId());

            transactionDOList.add(transaction);
        }
        if (!CollectionUtils.isEmpty(transactionDOList)) {
            userCreditTransactionMapper.batchInsertTransaction(transactionDOList);
        }

        // 插入发放记录
        List<UserCreditTransactionDO> issueCreditTransactionDos = new ArrayList<>();
        for (UserCreditDO userCreditDO : userCreditDOInDbList) {
            UserCreditTransactionDO transaction = new UserCreditTransactionDO();
            transaction.setUserId(userCreditDO.getUserId());
            transaction.setCreditType(creditTypeEnum.name());
            transaction.setDirection(UserCreditTransactionDO.DirectionEnum.IN.name());

            UserPlanDO userPlanDO = userPlanMap.get(userCreditDO.getUserId());
            Plan plan = planMap.get(userPlanDO.getPlanCode());
            transaction.setAmount(plan.getMemberShip().getSubscriptionCreditNum());
            transaction.setReasonType(UserCreditTransactionDO.ReasonTypeEnum.PURCHASED.name());
            transaction.setReasonSubType(UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT.name());
            transaction.setTraceId(TraceContext.getTraceId());

            issueCreditTransactionDos.add(transaction);
        }
        userCreditTransactionMapper.batchInsertTransaction(issueCreditTransactionDos);
    }

    public List<UserCreditDO> queryExpiringUserCredit(
            Long lowerId, int batchSize, List<CreditTypeEnum> creditTypeList) {
        LocalDateTime upperBoundExpireTime = TimeUtil.utcTime();
        LocalDateTime lowerBoundExpireTime = upperBoundExpireTime.minusDays(1);
        return userCreditMapper.getExpiringUserCredit(
                lowerId,
                batchSize,
                creditTypeList.stream().map(CreditTypeEnum::name).collect(Collectors.toList()),
                lowerBoundExpireTime,
                upperBoundExpireTime);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    public void batchExpireCredit(List<UserCreditDO> userCredits) {
        List<UserCreditDO> userCreditDos = new ArrayList<>();
        for (UserCreditDO userCredit : userCredits) {
            if (userCredit.getAmount() <= 0) {
                continue;
            }
            UserCreditDO newUserCreditDO = new UserCreditDO();
            newUserCreditDO.setId(userCredit.getId());
            newUserCreditDO.setAmount(0L);
            newUserCreditDO.setExpireTime(userCredit.getExpireTime());

            userCreditDos.add(newUserCreditDO);
        }
        if (CollectionUtils.isEmpty(userCreditDos)) {
            return;
        }
        userCreditMapper.batchUpdateUserCreditById(userCreditDos);

        // 插入过期记录
        List<UserCreditTransactionDO> transactionDOList = new ArrayList<>();
        for (UserCreditDO userCredit : userCredits) {
            if (userCredit.getAmount() <= 0) {
                continue;
            }
            UserCreditTransactionDO transaction = new UserCreditTransactionDO();
            transaction.setUserId(userCredit.getUserId());
            transaction.setCreditType(userCredit.getCreditType());
            transaction.setDirection(UserCreditTransactionDO.DirectionEnum.OUT.name());
            transaction.setAmount(Math.toIntExact(userCredit.getAmount()));
            transaction.setReasonType(UserCreditTransactionDO.ReasonTypeEnum.SPENT.name());
            transaction.setReasonSubType(UserCreditTransactionDO.ReasonSubTypeEnum.EXPIRED_CREDITS.name());
            transaction.setTraceId(TraceContext.getTraceId());

            transactionDOList.add(transaction);
        }
        userCreditTransactionMapper.batchInsertTransaction(transactionDOList);
    }

    public void deductCredit(
            Long userId,
            Long orgId,
            Integer amount,
            UserCreditTransactionDO.ReasonTypeEnum reason,
            UserCreditTransactionDO.ReasonSubTypeEnum subReason) {
        if (amount <= 0) {
            LogUtil.errorf(log, "Invalid credit amount to decrease: {0}", amount);
            return;
        }

        if (Objects.nonNull(userId) && Objects.nonNull(orgId)) {
            log.error("userId and orgId can only one is not null, userId: {}, orgId: {}", userId, orgId);
            throw ZeusServiceException.internalError("userId and orgId can only one is not null");
        }

        if (Objects.nonNull(userId)) {
            log.info("will deduct {} credits of user: {}", amount, userId);
        }

        if (Objects.nonNull(orgId)) {
            log.info("will deduct {} credits of org: {}", amount, orgId);
        }

        // Check if user has sufficient credits
        userCreditAndTransactionDao.deductCredits(userId, orgId, amount, reason, subReason);
    }

    public void issueOperateCredit(List<Long> userIds, Integer amount, CreditTypeEnum creditTypeEnum) {
        for (Long userId : userIds) {
            LogUtil.infof(
                    log,
                    "issueOperateCredit, userId: {0}, amount: {1}, creditType: {2}",
                    userId,
                    amount,
                    creditTypeEnum);
            userCreditAndTransactionDao.issueOperationCredit(amount, creditTypeEnum, userId, null);
        }
    }
}
