package co.sandai.zeus.domain.asset.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface AssetMapper {

    void insertAsset(@Param("asset") Asset asset);

    @Select("SELECT * from asset where id=#{id} and deleted=0")
    Asset getAssetById(@Param("id") long id);

    @Select("SELECT * from asset where id=#{id}")
    Asset getAssetByIdWithDeleted(@Param("id") long id);

    @Select("SELECT count(*) from asset where task_id=#{taskId} and deleted=0")
    int getCountByTaskId(@Param("taskId") long taskId);

    @Select("UPDATE asset SET poster_asset_id=#{posterAssetId} where id=#{assetId} and deleted=0")
    void updateAssetPosterById(long assetId, long posterAssetId);

    @Select(
            "UPDATE asset SET poster_asset_id=#{posterAssetId}, width=#{width}, height=#{height} where id=#{assetId} and deleted=0")
    void updateAssetPosterAndSizeById(long assetId, long posterAssetId, int width, int height);
    /**
     * 更新资源审核结果及置信度
     *
     * @param assetId    资源ID
     * @param isPassed   是否通过审核
     * @param confidence 审核置信度值
     */
    @Update(
            "UPDATE asset SET is_moderation_passed=#{isPassed}, moderation_confidence=#{confidence} where id=#{assetId} and deleted=0")
    void updateAssetModerateResult(long assetId, boolean isPassed, Float confidence);

    /**
     * 更新视频资源审核结果、状态及置信度
     *
     * @param assetId    资源ID
     * @param isPassed   是否通过审核
     * @param status     视频审核状态
     * @param confidence 审核置信度值
     */
    @Update(
            "UPDATE asset SET is_moderation_passed=#{isPassed}, video_moderation_status=#{status}, moderation_confidence=#{confidence} where id=#{assetId} and deleted=0")
    void updateVideoModerateResult(long assetId, boolean isPassed, AssetVideoModerationStatus status, Float confidence);

    @Update(
            "UPDATE asset SET video_moderation_status=#{status}, video_moderation_seed=#{seed}, video_moderation_task_id=#{moderationTaskId} where id=#{assetId} and deleted=0")
    void updateVideoAssetModerateInfo(
            long assetId, AssetVideoModerationStatus status, String moderationTaskId, String seed);

    @Select("DELETE FROM asset where id=#{assetId}")
    void deleteAsset(long assetId);

    @Select("select * from asset where media_type='MP4' and poster_asset_id=0 and deleted=0")
    List<Asset> getNonPosterMp4Assets();

    @Select(
            "SELECT * from asset where task_id=#{taskId} and media_type=#{mediaType} and source=#{source} and deleted = 0 order by task_chunk_id,`index`")
    List<Asset> getChunkAssetsByTaskId(long taskId, AssetMediaType mediaType, AssetSource source);

    @SelectProvider(type = AssetSQLProvider.class, method = "filterAssets")
    List<Asset> filterAssets(long userId, AssetMediaType[] types, AssetSource[] sources, int offset, int limit);

    @SelectProvider(type = AssetSQLProvider.class, method = "countAssets")
    int countAssets(long userId, AssetMediaType[] types, AssetSource[] sources);

    @Select("SELECT * FROM asset WHERE task_chunk_id = #{taskChunkId} and deleted = 0 order by `index`")
    List<Asset> findByTaskChunkId(@Param("taskChunkId") long taskChunkId);

    @Update("UPDATE asset SET deleted = 1 WHERE id = #{id}")
    void logitDelete(Long id);

    @Update("UPDATE asset SET urn = #{urn} WHERE id = #{id}")
    void updateAssetUrn(@Param("id") long id, @Param("urn") String urn);

    @Select("SELECT * from asset where urn=#{urn} and deleted=0")
    Asset getByUrn(@Param("urn") String urn);

    @Select(
            "<script>SELECT * from asset where task_id=#{taskId} and <if test='taskChunkId != null'>task_chunk_id=#{taskChunkId}</if><if test='taskChunkId == null'>task_chunk_id IS NULL</if> and `index`=#{index} and media_type=#{mediaType} and source=#{source} and deleted=0 LIMIT 1</script>")
    Asset getTsByChunk(
            @Param("taskId") long taskId,
            @Param("taskChunkId") Long taskChunkId,
            @Param("index") long index,
            @Param("mediaType") String mediaType,
            @Param("source") AssetSource source);

    @Select("<script>"
            + "SELECT * FROM asset WHERE id IN "
            + "<foreach item='id' collection='ids' open='(' separator=',' close=')'>"
            + "#{id}"
            + "</foreach>"
            + " AND deleted=0 AND is_moderation_passed=1"
            + "</script>")
    List<Asset> getAssetsByIds(@Param("ids") List<Long> ids);

    /**
     * Overwrite existing asset fields when matching task info (taskId, taskChunkId,
     * index, mediaType).
     */
    @Update(
            "<script>UPDATE asset SET oss_bucket = #{asset.ossBucket}, oss_source = #{asset.ossSource}, oss_path = #{asset.ossPath}, urn = #{asset.urn}, width = #{asset.width}, height = #{asset.height}, duration = #{asset.duration}, source = #{asset.source} WHERE task_id = #{asset.taskId} and <if test='asset.taskChunkId != null'>task_chunk_id = #{asset.taskChunkId}</if><if test='asset.taskChunkId == null'>task_chunk_id IS NULL</if> and `index` = #{asset.index} and media_type = #{asset.mediaType} and deleted = 0</script>")
    void overwriteAssetByTaskInfo(@Param("asset") Asset asset);
}
