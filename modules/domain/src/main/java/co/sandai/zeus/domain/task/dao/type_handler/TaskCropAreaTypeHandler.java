package co.sandai.zeus.domain.task.dao.type_handler;

import co.sandai.zeus.domain.common.CropArea;
import co.sandai.zeus.type_handler.JSONTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(CropArea.class)
public class TaskCropAreaTypeHandler extends JSONTypeHandler<CropArea> {
    public TaskCropAreaTypeHandler() {
        super(CropArea.class);
    }
}
