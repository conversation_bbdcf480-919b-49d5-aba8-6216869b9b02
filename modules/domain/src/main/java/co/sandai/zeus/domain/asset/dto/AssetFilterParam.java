package co.sandai.zeus.domain.asset.dto;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.domain.asset.dao.AssetMediaType;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import java.util.Arrays;
import java.util.Objects;
import lombok.Builder;
import lombok.Setter;

@Builder
@Setter
public class AssetFilterParam {
    public enum AssetFilterParamSource {
        Upload,
        Generate,
        Featured,
        HumanFeatured,
        TextToSpeech;

        public AssetSource toAssetSource() {
            switch (this) {
                case Upload:
                    return AssetSource.Upload;
                case Generate:
                    return AssetSource.Generate;
                case Featured:
                    return AssetSource.Featured;
                case HumanFeatured:
                    return AssetSource.HumanFeatured;
                case TextToSpeech:
                    return AssetSource.TextToSpeech;
                default:
                    throw new ZeusServiceException(ErrorCode.InvalidParameters);
            }
        }
    }

    public enum AssetFilterParamType {
        Video,
        Image,
        Audio;

        public AssetMediaType[] toAssetMediaTypes() {
            if (this == Video) {
                return new AssetMediaType[] {AssetMediaType.MP4};
            }
            if (this == Image) {
                return new AssetMediaType[] {AssetMediaType.PNG, AssetMediaType.JPEG, AssetMediaType.WEBP};
            }
            if (this == Audio) {
                return new AssetMediaType[] {AssetMediaType.WAV, AssetMediaType.MP3};
            }
            throw new ZeusServiceException(ErrorCode.InvalidParameters);
        }
    }

    private AssetFilterParamSource[] sources;
    private AssetFilterParamType[] types;

    public AssetSource[] getSources() {
        if (Objects.isNull(sources) || sources.length == 0) {
            return new AssetSource[] {};
        }
        AssetSource[] assetSources = Arrays.stream(sources)
                .map(AssetFilterParamSource::toAssetSource)
                .toArray(AssetSource[]::new);

        // 验证Featured和HumanFeatured不能同时出现
        boolean hasFeatured = false;
        boolean hasHumanFeatured = false;
        for (AssetSource source : assetSources) {
            if (source == AssetSource.Featured) {
                hasFeatured = true;
            } else if (source == AssetSource.HumanFeatured) {
                hasHumanFeatured = true;
            }
        }
        if (hasFeatured && hasHumanFeatured) {
            throw new ZeusServiceException(
                    ErrorCode.InvalidParameters, "Featured and HumanFeatured cannot be used together");
        }

        return assetSources;
    }

    public AssetMediaType[] getTypes() {
        if (Objects.isNull(types) || types.length == 0) {
            return new AssetMediaType[] {};
        }
        return Arrays.stream(types)
                .map(AssetFilterParamType::toAssetMediaTypes)
                .flatMap(Arrays::stream)
                .toArray(AssetMediaType[]::new);
    }
}
