package co.sandai.zeus.domain.flexible.dao;

import java.util.List;
import org.apache.ibatis.annotations.*;

@Mapper
public interface ObjectsMapper {

    @Select("SELECT * FROM objects WHERE deleted = false")
    List<ObjectDO> findAll();

    @Select("SELECT * FROM objects WHERE id = #{id} AND deleted = false")
    ObjectDO findById(Long id);

    @Insert(
            "INSERT INTO objects (id, type, data, user_id, deleted, create_time, update_time) VALUES (#{id}, #{type}, #{data}, #{userId}, #{deleted}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    Long insert(ObjectDO objectDO);

    @Update(
            "UPDATE objects SET type = #{type}, data = #{data}, user_id = #{userId}, deleted = #{deleted}, create_time = #{createTime}, update_time = #{updateTime} WHERE id = #{id}")
    void update(ObjectDO objectDO);

    @Update("UPDATE objects SET deleted = true WHERE id = #{id}")
    void logicalDelete(Long id);

    @Select("<script>" + "SELECT * FROM objects "
            + "<where>"
            + "  <if test='type != null'>AND type = #{type}</if>"
            + "</where>"
            + "LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<ObjectDO> findWithPaginationAndType(
            @Param("offset") int offset, @Param("limit") int limit, @Param("type") String type);
}
