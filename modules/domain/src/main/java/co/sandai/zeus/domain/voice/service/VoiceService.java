package co.sandai.zeus.domain.voice.service;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.helper.AudioHelper;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.auth.service.AuthService;
import co.sandai.zeus.domain.common.WhiteListService;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.dao.UserCreditTransactionDO;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import co.sandai.zeus.domain.voice.dao.Voice;
import co.sandai.zeus.domain.voice.dao.mapper.VoiceMapper;
import co.sandai.zeus.domain.voice.dto.VerifiedLanguageDTO;
import co.sandai.zeus.domain.voice.dto.VoiceDTO;
import co.sandai.zeus.domain.voice.service.provider.VoiceProvider;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.infer.ArtifactClient;
import co.sandai.zeus.infra.voice.client.GoogleTranslateClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * Unified service for voice-related operations
 * Acts as coordinator between controllers and provider implementations
 */
@Service
@Slf4j
public class VoiceService {
    @Autowired
    private ArtifactClient artifactClient;

    @Autowired
    private VoiceProviderRegistry providerRegistry;

    @Autowired
    private AssetService assetService;

    @Resource
    private AuthService authService;

    @Autowired
    private UserCreditService userCreditService;

    @Autowired
    private GoogleTranslateClient googleTranslateClient;

    @Autowired
    private VoiceMapper voiceMapper;

    @Autowired
    private IDGenerator idGenerator;

    @Autowired
    private WhiteListService whiteListService;

    @Autowired
    private UserService userService;

    /**
     * Download content from a URL and return it as a byte array
     *
     * @param url The URL to download from
     * @return The downloaded content as a byte array
     * @throws IOException If an I/O error occurs
     */
    protected byte[] downloadFromUrl(String url) throws IOException {
        URL urlObj = URI.create(url).toURL();
        HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
        connection.setRequestMethod("GET");

        // Handle redirects
        connection.setInstanceFollowRedirects(true);

        // Get content from remote URL
        try (InputStream inputStream = connection.getInputStream()) {
            return inputStream.readAllBytes();
        }
    }

    /**
     * Detect the language of a text string using Google Cloud Translation API
     *
     * @param text The text to analyze for language detection
     * @return The detected language code (e.g., "en", "zh-CN", "ja")
     */
    public String detectLanguage(String text) {
        if (text == null || text.trim().isEmpty()) {
            throw new ZeusServiceException(ErrorCode.InvalidRequest, "Text cannot be empty");
        }

        try {
            return googleTranslateClient.detectLanguage(text);
        } catch (Exception e) {
            log.error("Failed to detect language: {}", e.getMessage(), e);
            throw new ZeusServiceException(ErrorCode.VoiceLanguageDetectionFailed, "Failed to detect language");
        }
    }

    /**
     * Select appropriate voice provider based on audio language
     * Uses 11labs for speech recognition and selects provider based on language:
     * - Chinese audio: minimax provider
     * - Other languages: elevenlabs provider
     *
     * @param audioFiles List of audio files to analyze
     * @return The appropriate VoiceProvider based on detected language
     */
    public VoiceProvider selectCreateVoiceProvider(List<MultipartFile> audioFiles) {
        MultipartFile audioFile = audioFiles.get(0);
        // Step 1: Use 11labs for STT to get text from audio
        final String sttProviderName = "elevenlabs"; // 也只实现了11labs的STT功能，minimax没有实现STT功能
        String transcribedText;

        try {
            // Convert MultipartFile to byte array
            byte[] audioData = audioFile.getBytes();
            transcribedText = speechToText(audioData, sttProviderName, null);

            if (transcribedText == null || transcribedText.trim().isEmpty()) {
                VoiceProvider defaultProvider = providerRegistry.getDefaultProvider();
                log.warn(
                        "Speech-to-text result is empty, using default provider: {}",
                        defaultProvider.getProviderName());
                return defaultProvider;
            }

            // Step 2: Use Google Translate to detect language from the transcribed text
            String detectedLanguage = detectLanguage(transcribedText);
            log.info(
                    "Detected language from audio: {} (text sample: {})",
                    detectedLanguage,
                    transcribedText.length() > 50 ? transcribedText.substring(0, 50) + "..." : transcribedText);

            // Step 3: Select provider based on detected language
            // Chinese languages start with "zh"
            if (detectedLanguage != null && detectedLanguage.startsWith("zh")) {
                return providerRegistry.getProvider(VoiceProviderRegistry.PROVIDER_MINIMAX); // Use minimax for Chinese
            } else {
                return providerRegistry.getProvider(
                        VoiceProviderRegistry.PROVIDER_ELEVENLABS); // Use 11labs for English (and other languages)
            }
        } catch (Exception e) {
            log.error("Failed to detect audio language: {}", e.getMessage(), e);
            throw new ZeusServiceException(ErrorCode.InternalError, "Failed to detect audio language");
        }
    }

    /**
     * Convert speech to text using the specified provider
     * @param audioData Audio data as bytes to convert to text
     * @param providerName ID of the provider to use for conversion
     * @param language Optional language code (e.g., "en", "zh"). If null, auto-detect language
     * @return Transcribed text from the audio
     */
    public String speechToText(byte[] audioData, String providerName, String language) {
        VoiceProvider provider = providerRegistry.getProvider(providerName);
        if (provider == null) {
            throw new ZeusServiceException(ErrorCode.VoiceProviderNotFound);
        }

        try {
            return provider.speechToText(audioData, language);
        } catch (Exception e) {
            log.error("Failed to convert speech to text using provider {}: {}", providerName, e.getMessage(), e);
            throw new ZeusServiceException(ErrorCode.VoiceSpeechToTextFailed);
        }
    }

    /**
     * Get all voices from the database, including provider voices and user-created voices
     * @param userId User ID to include their user-specific voices
     * @return List of all voice DTOs
     */
    public List<Voice> getAllVoices(long userId) {
        List<Voice> result = new ArrayList<>();

        // Get all provider voices from database
        List<Voice> allProviderVoices = new ArrayList<>();
        providerRegistry.getAllProviders().stream()
                .map(VoiceProvider::getProviderName)
                .forEach(provider -> {
                    allProviderVoices.addAll(voiceMapper.getVoicesByProvider(provider, false));
                });

        List<Voice> userVoices;
        if (userId == 0) {
            userVoices = new ArrayList<>();
        } else {
            userVoices = voiceMapper.getVoicesByUserId(Long.valueOf(userId));
        }

        // Filter out deleted voices from both lists
        List<Voice> activeProviderVoices =
                allProviderVoices.stream().filter(voice -> !voice.isDeleted()).collect(Collectors.<Voice>toList());

        List<Voice> activeUserVoices =
                userVoices.stream().filter(voice -> !voice.isDeleted()).collect(Collectors.<Voice>toList());

        // Combine both lists into result and convert to DTOs
        if (!activeProviderVoices.isEmpty()) {
            log.info(
                    "Retrieved {} active provider voices from database (filtered from {})",
                    activeProviderVoices.size(),
                    allProviderVoices.size());
            result.addAll(activeProviderVoices.stream().collect(Collectors.toList()));
        } else {
            log.warn("No active provider voices found in database. Consider running voice sync.");
        }

        if (!activeUserVoices.isEmpty()) {
            log.info(
                    "Retrieved {} active user voices from database for userId: {} (filtered from {})",
                    activeUserVoices.size(),
                    userId,
                    userVoices.size());
            result.addAll(activeUserVoices.stream().collect(Collectors.toList()));
        }

        return result;
    }

    /**
     * Synchronize provider voices with the database
     * This will fetch the latest voices from all providers, compare with what's in the database,
     * add new voices, and mark removed voices as inactive or deleted
     *
     * @param updateExisting If true, update all fields of existing voices with values from provider
     * @return Map with provider names as keys and counts of added/removed/unchanged/updated voices as values
     */
    @Transactional
    public Map<String, Integer> syncProviderVoices(boolean updateExisting, String providerName) {
        Map<String, Integer> syncResults = new HashMap<String, Integer>();

        // Process each provider
        for (VoiceProvider provider : providerRegistry.getAllProviders()) {
            if (!provider.getProviderName().equals(providerName)) {
                continue;
            }

            int addedCount = 0;
            int removedCount = 0;
            int unchangedCount = 0;
            int updatedCount = 0;

            try {
                // Get current voices from provider
                List<VoiceDTO> currentProviderVoices = provider.getVoices();

                // Get ALL existing voices for this provider from DB, including deleted ones
                List<Voice> existingDbVoices = voiceMapper.getVoicesByProvider(providerName, true);

                // Create maps for efficient lookups
                Map<String, VoiceDTO> currentVoiceMap = new HashMap<String, VoiceDTO>();
                for (VoiceDTO voiceDTO : currentProviderVoices) {
                    currentVoiceMap.put(voiceDTO.getId(), voiceDTO);
                }

                Map<String, Voice> existingVoiceMap = new HashMap<String, Voice>();
                for (Voice voice : existingDbVoices) {
                    existingVoiceMap.put(voice.getVoiceId(), voice);
                }

                // Create sets of voice IDs for comparison
                Set<String> currentVoiceIds = currentVoiceMap.keySet();
                Set<String> existingVoiceIds = existingVoiceMap.keySet();

                // Find voice IDs to add (in current but not in DB)
                Set<String> voiceIdsToAdd = new HashSet<>(currentVoiceIds);
                voiceIdsToAdd.removeAll(existingVoiceIds);

                // Find voice IDs to deactivate (in DB but not in current provider list)
                Set<String> voiceIdsToDeactivate = new HashSet<>(existingVoiceIds);
                voiceIdsToDeactivate.removeAll(currentVoiceIds);

                // Find voice IDs to potentially update (exist in both current and DB)
                Set<String> voiceIdsToCheck = new HashSet<>(currentVoiceIds);
                voiceIdsToCheck.retainAll(existingVoiceIds);

                // Process voices to add
                for (String voiceId : voiceIdsToAdd) {
                    VoiceDTO voiceDTO = currentVoiceMap.get(voiceId);
                    // This is a new voice, add it to DB
                    String rawPreviewUrl = voiceDTO.getPreviewUrl();
                    String previewUrl = processVoicePreview(rawPreviewUrl, voiceDTO.getName(), voiceDTO.getId());

                    // Process verifiedLanguages preview URLs if available
                    String verifiedLanguages = voiceDTO.getVerifiedLanguages();
                    if (verifiedLanguages != null && !verifiedLanguages.isEmpty()) {
                        try {
                            // Process all preview_url fields in verifiedLanguages JSON
                            verifiedLanguages =
                                    normalizeVerifiedLanguagesPreviewUrls(verifiedLanguages, voiceDTO.getName());
                        } catch (Exception e) {
                            log.error(
                                    "Failed to process verifiedLanguages preview URLs for voice: {}",
                                    voiceDTO.getName(),
                                    e);
                            // Continue with original verifiedLanguages if processing fails
                        }
                    }

                    Voice newVoice = Voice.builder()
                            .id(idGenerator.getNextId())
                            .voiceId(voiceDTO.getId())
                            .name(voiceDTO.getName())
                            .description(voiceDTO.getDescription())
                            .language(voiceDTO.getLanguage())
                            .previewUrl(previewUrl)
                            .isCloned(false)
                            .gender(voiceDTO.getGender())
                            .provider(providerName)
                            .userId(0) // 0 indicates system/provider voice
                            .orgId(0) // 0 indicates system/provider voice
                            .verifiedLanguages(verifiedLanguages)
                            .build();

                    voiceMapper.insertVoice(newVoice);
                    addedCount++;
                }

                // Process voices that exist in both provider and DB
                for (String voiceId : voiceIdsToCheck) {
                    VoiceDTO currentVoice = currentVoiceMap.get(voiceId);
                    Voice existingVoice = existingVoiceMap.get(voiceId);

                    if (updateExisting) {
                        // Simplified approach: update all fields except ID and voice_id when flag is true
                        try {
                            // Process the preview URL
                            String processedPreviewUrl = processVoicePreview(
                                    currentVoice.getPreviewUrl(), currentVoice.getName(), currentVoice.getId());

                            // Process verified languages if available
                            String verifiedLanguagesJson = currentVoice.getVerifiedLanguages();
                            String processedVerifiedLanguages = verifiedLanguagesJson;
                            if (verifiedLanguagesJson != null && !verifiedLanguagesJson.isEmpty()) {
                                processedVerifiedLanguages = normalizeVerifiedLanguagesPreviewUrls(
                                        verifiedLanguagesJson, currentVoice.getName());
                            }

                            // Create updated voice entity with processed fields
                            Voice updatedVoice = Voice.builder()
                                    .id(existingVoice.getId())
                                    .voiceId(existingVoice.getVoiceId()) // Keep original voice_id
                                    .name(currentVoice.getName())
                                    .description(currentVoice.getDescription())
                                    .language(currentVoice.getLanguage())
                                    .previewUrl(processedPreviewUrl)
                                    .isCloned(false)
                                    .provider(existingVoice.getProvider()) // Keep original provider
                                    .userId(existingVoice.getUserId()) // Keep original userId if any
                                    .orgId(existingVoice.getOrgId()) // Keep original orgId if any
                                    .verifiedLanguages(processedVerifiedLanguages)
                                    .deleted(false)
                                    .build();

                            // Update all fields in one operation
                            voiceMapper.updateVoiceAllFields(updatedVoice);
                            log.info("Updated all fields for voice {}", currentVoice.getName());
                            updatedCount++;
                        } catch (Exception e) {
                            log.error("Failed to update voice {}: {}", currentVoice.getName(), e.getMessage(), e);
                            unchangedCount++;
                        }
                    } else {
                        // If updateExisting is false, we don't update existing voices at all
                        // Simply increment unchangedCount and continue
                        unchangedCount++;
                    }
                }

                // Process voices to deactivate/remove
                for (String voiceId : voiceIdsToDeactivate) {
                    Voice voice = existingVoiceMap.get(voiceId);
                    if (!voice.isDeleted()) {
                        // Use updateVoiceDeleted method to mark voice as deleted instead of physically deleting
                        voiceMapper.updateVoiceDeleted(voice.getId(), true);
                        removedCount++;
                    }
                }

                // Record results for this provider
                syncResults.put(providerName + "_added", addedCount);
                syncResults.put(providerName + "_removed", removedCount);
                syncResults.put(providerName + "_unchanged", unchangedCount);
                syncResults.put(providerName + "_updated", updatedCount);

                log.info(
                        "Synced voices for provider {}: added={}, removed={}, unchanged={}, updated={}",
                        providerName,
                        addedCount,
                        removedCount,
                        unchangedCount,
                        updatedCount);

            } catch (Exception e) {
                String errorDetails = e.getClass().getName() + ": " + e.getMessage();
                log.error("Error syncing voices for provider {}: {}", providerName, errorDetails, e);
                // Add detailed error logs but maintain only Integer values in the map
                syncResults.put(providerName + "_error", 1);
                // Log more details since we can't store the error message in the map
                log.warn("Error details for {}: {}", providerName, errorDetails);
            }
        }

        return syncResults;
    }

    /**
     * Process a voice preview URL by downloading the audio, normalizing the loudness,
     * and uploading to our artifact storage.
     *
     * @param rawPreviewUrl The original preview URL from the provider
     * @param voiceName The name of the voice for logging and file naming
     * @param voiceId The ID of the voice for fallback naming
     * @return The new URL pointing to our normalized audio file
     */
    private String processVoicePreview(String rawPreviewUrl, String voiceName, String voiceId) {
        if (rawPreviewUrl == null) {
            return null;
        }

        String fileExtension = "mp3";
        try {
            // Download original audio data
            byte[] originalData = downloadFromUrl(rawPreviewUrl);

            // Normalize audio loudness
            log.info("Normalizing audio loudness for voice preview: {}", voiceName);
            byte[] normalizedData;
            try {
                normalizedData = AudioHelper.normalizeLoudness(originalData);
            } catch (Exception e) {
                // Handle audio normalization errors (including unsupported formats)
                if (e.getClass().getName().contains("UnsupportedAudioFileException")) {
                    log.warn(
                            "Unsupported audio format for voice preview: {}, {}, skipping normalization",
                            voiceName,
                            rawPreviewUrl,
                            e);
                } else {
                    log.error("Error normalizing audio for voice preview: {}, {}", voiceName, rawPreviewUrl, e);
                }
                return rawPreviewUrl;
            }

            // Generate a name for the file if not available
            String name = voiceName != null ? voiceName.replaceAll("[^a-zA-Z0-9.-]", "_") : "voice_" + voiceId;

            // Upload to our artifact storage
            ArtifactClient.ArtifactUploadResponseDTO artifactUploadResponseDTO =
                    artifactClient.uploadArtifact(name, fileExtension, normalizedData);
            return artifactUploadResponseDTO.url();
        } catch (IOException e) {
            log.error("Failed to download preview audio from URL: {}", rawPreviewUrl, e);
            // If downloading fails, return the original URL as fallback
            return rawPreviewUrl;
        } catch (Exception e) {
            log.error("Unexpected error processing voice preview: {}", rawPreviewUrl, e);
            return rawPreviewUrl;
        }
    }

    /**
     * Create a new voice
     * @param name Voice name
     * @param description Voice description
     * @param audioFiles Audio files
     * @param userId User ID
     * @param orgId Organization ID
     * @return Created voice DTO
     */
    @Transactional
    public Voice createVoice(String name, String description, List<MultipartFile> audioFiles, long userId, long orgId)
            throws IOException {

        int voiceQuota = 0;
        if (authService.checkAuth(userId, "create_2_custom_voices")) {
            voiceQuota = 2;
        } else if (authService.checkAuth(userId, "create_5_custom_voices")) {
            voiceQuota = 5;
        } else if (authService.checkAuth(userId, "create_10_custom_voices")) {
            voiceQuota = 10;
        }

        if (voiceQuota == 0) {
            throw ZeusServiceException.forbidden(
                    "Sorry, this feature is exclusive to premium members. Please view membership benefits and upgrade now!");
        }

        List<Voice> userVoices = voiceMapper.getVoicesByUserId(userId);
        if (userVoices.size() >= voiceQuota) {
            throw ZeusServiceException.forbidden("You have reached the maximum number of voices");
        }

        VoiceProvider provider = selectCreateVoiceProvider(audioFiles);

        // Create voice with provider
        VoiceDTO voiceDTO = provider.createVoice(name, description, audioFiles);

        String fileExtension = "mp3";
        byte[] originalData = audioFiles.get(0).getBytes();

        // Normalize audio loudness for consistent user experience
        byte[] data;
        try {
            log.info("Normalizing audio loudness for voice preview: {}", name);
            data = AudioHelper.normalizeLoudness(originalData);
            log.info("Audio normalization completed for voice preview");
        } catch (IOException e) {
            log.warn("Failed to normalize audio, using original audio data", e);
            data = originalData;
        }

        ArtifactClient.ArtifactUploadResponseDTO artifactUploadResponseDTO =
                artifactClient.uploadArtifact(name, fileExtension, data);

        String previewUrl = artifactUploadResponseDTO.url();

        // Persist voice data to database
        Voice voice = Voice.builder()
                .id(idGenerator.getNextId())
                .voiceId(voiceDTO.getId())
                .name(voiceDTO.getName())
                .description(voiceDTO.getDescription())
                // .language(voiceDTO.getLanguage())
                .previewUrl(previewUrl)
                .isCloned(true)
                .provider(provider.getProviderName())
                .userId(userId)
                .orgId(orgId)
                .build();

        voiceMapper.insertVoice(voice);

        return voice;
    }

    @Transactional(rollbackFor = Throwable.class)
    public Asset textToSpeech(Long internalVoiceId, String text, Long userId, Long orgId) {
        Voice voice = voiceMapper.getVoiceById(internalVoiceId);
        if (voice == null) {
            throw new ZeusServiceException(ErrorCode.NotFound, "Voice not found for ID: " + internalVoiceId);
        }

        // 确保 credits > 0
        long totalCredits = userCreditService.getUserCreditSum(userId);
        if (totalCredits <= 0) {
            throw new ZeusServiceException(ErrorCode.InsufficientCredit, "Insufficient credit balance");
        }

        // 生成音频
        VoiceProvider provider = providerRegistry.getProvider(voice.getProvider());
        byte[] audioData = provider.textToSpeech(voice.getVoiceId(), text);
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy_MM_dd_HH_mm");
        String filename = String.format("tts_%s.mp3", now.format(formatter));

        long targetUserId = userId;
        long targetOrgId = Objects.nonNull(orgId) ? orgId : 0L;
        Asset audioAsset =
                assetService.addTextToSpeechAsset(audioData, targetUserId, targetOrgId, "mp3", filename, voice.getId());
        float duration = audioAsset.getDuration();

        // 扣减 credits，TODO：不能预先获知生成的声音的 duration，暂且后扣费
        int creditAmount = (int) Math.ceil((double) duration * ((double) 20 / 60));
        UserCreditTransactionDO.ReasonSubTypeEnum reasonSubType =
                UserCreditTransactionDO.ReasonSubTypeEnum.AUDIO_GENERATION;
        userCreditService.deductCredit(
                userId, orgId, creditAmount, UserCreditTransactionDO.ReasonTypeEnum.SPENT, reasonSubType);

        return audioAsset;
    }

    /**
     * Process verifiedLanguages JSON to normalize all preview_url audio files
     *
     * @param verifiedLanguagesJson JSON string containing language and preview_url entries
     * @param voiceName Name of the voice (for logging and filename generation)
     * @return Updated JSON string with normalized preview URLs
     * @throws JsonProcessingException If JSON processing fails
     * @throws IOException If audio download or processing fails
     */
    private String normalizeVerifiedLanguagesPreviewUrls(String verifiedLanguagesJson, String voiceName)
            throws JsonProcessingException, IOException {

        if (verifiedLanguagesJson == null || verifiedLanguagesJson.isEmpty()) {
            return verifiedLanguagesJson;
        }

        ObjectMapper mapper = new ObjectMapper();

        // Deserialize to list of VerifiedLanguageDTO objects
        List<VerifiedLanguageDTO> verifiedLanguages;
        try {
            verifiedLanguages =
                    mapper.readValue(verifiedLanguagesJson, new TypeReference<List<VerifiedLanguageDTO>>() {});
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse verifiedLanguages JSON for voice: {}, skipping normalization", voiceName, e);
            return verifiedLanguagesJson;
        }

        if (verifiedLanguages == null || verifiedLanguages.isEmpty()) {
            return verifiedLanguagesJson;
        }

        String fileExtension = "mp3";

        // Process each verified language entry
        for (VerifiedLanguageDTO verifiedLanguage : verifiedLanguages) {
            String language = verifiedLanguage.getLanguage();
            String previewUrl = verifiedLanguage.getPreview_url();

            // Process preview_url if it exists
            if (previewUrl != null && !previewUrl.isEmpty()) {
                try {
                    // Download audio
                    log.info("Normalizing audio for verifiedLanguage: {} in voice: {}", language, voiceName);
                    byte[] originalData = downloadFromUrl(previewUrl);

                    // Normalize the audio
                    byte[] normalizedData;
                    try {
                        normalizedData = AudioHelper.normalizeLoudness(originalData);
                    } catch (Exception e) {
                        // Check if it's an unsupported audio format exception
                        if (e.getClass().getName().contains("UnsupportedAudioFileException")) {
                            log.warn(
                                    "Unsupported audio format for language: {} in voice: {}, {}, skipping normalization",
                                    language,
                                    voiceName,
                                    previewUrl);
                        } else {
                            log.error(
                                    "Error normalizing audio for language: {} in voice: {}, {}, {}",
                                    language,
                                    voiceName,
                                    previewUrl,
                                    e);
                        }
                        // Keep original preview_url if normalization fails (already set in the DTO)
                        continue;
                    }

                    // Generate a name for the file
                    String safeName = (voiceName != null ? voiceName : "voice") + "_" + language;
                    String name = safeName.replaceAll("[^a-zA-Z0-9.-]", "_");

                    // Upload normalized audio
                    ArtifactClient.ArtifactUploadResponseDTO artifactUploadResponseDTO =
                            artifactClient.uploadArtifact(name, fileExtension, normalizedData);

                    // Update preview_url in the DTO
                    verifiedLanguage.setPreview_url(artifactUploadResponseDTO.url());
                    log.info("Normalized and uploaded audio for language: {} in voice: {}", language, voiceName);
                } catch (IOException e) {
                    log.error("Failed to download audio for language: {} in voice: {}", language, voiceName, e);
                    // Keep original preview_url if download fails (already set in the DTO)
                } catch (Exception e) {
                    log.error("Failed to process preview_url for language: {} in voice: {}", language, voiceName, e);
                    // Keep original preview_url if processing fails (already set in the DTO)
                }
            }
        }

        // Serialize back to JSON string
        return mapper.writeValueAsString(verifiedLanguages);
    }

    /**
     * Delete a voice by its ID
     * Currently only implemented for ElevenLabs provider
     *
     * @param voiceId Internal ID of the voice to delete
     * @param userId User ID requesting the deletion (for authorization)
     * @return true if deletion was successful, false otherwise
     */
    @Transactional
    public boolean deleteVoice(Long voiceId, long userId) {
        try {
            // Get the voice by ID
            Voice voice = voiceMapper.getVoiceById(voiceId);
            if (voice == null) {
                log.error("Voice not found for deletion: {}", voiceId);
                return false;
            }

            // Check if it's user's own voice or system voice
            if (voice.getUserId() != 0 && voice.getUserId() != userId) {
                throw ZeusServiceException.forbidden("User " + userId + " not authorized to delete voice " + voiceId);
            }

            // Get the provider and call delete method
            VoiceProvider provider = providerRegistry.getProvider(voice.getProvider());
            boolean deleted = provider.deleteVoice(voice.getVoiceId());

            if (deleted) {
                // Mark voice as deleted in database rather than actual deletion
                voiceMapper.updateVoiceDeleted(voice.getId(), true);
                log.info("Voice {} successfully deleted", voiceId);
                return true;
            } else {
                log.error("Failed to delete voice {} with provider {}", voiceId, voice.getProvider());
                return false;
            }
        } catch (Exception e) {
            log.error("Error deleting voice: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Update voice details including gender
     * @param voiceId Voice ID
     * @param name Voice name (can be null to keep current)
     * @param description Voice description (can be null to keep current)
     * @param gender Voice gender (can be null to keep current)
     * @param currentUser Current user (for authorization)
     * @return Updated voice
     */
    @Transactional
    public Voice updateVoice(Long voiceId, String name, String description, String gender, User currentUser) {
        Voice voice = voiceMapper.getVoiceById(voiceId);
        if (voice == null) {
            throw new ZeusServiceException(ErrorCode.NotFound, "Voice not found for ID: " + voiceId);
        }

        // Check authorization - only allow users to update their own voices or super users to update any voice
        boolean isSuperUser = whiteListService.isSuperUser(currentUser.getEmail());

        if (voice.getUserId() != 0 && voice.getUserId() != currentUser.getId() && !isSuperUser) {
            throw ZeusServiceException.forbidden(
                    "User " + currentUser.getId() + " not authorized to update voice " + voiceId);
        }

        // Update fields if provided
        if (name != null && !name.trim().isEmpty()) {
            voice.setName(name.trim());
        }
        if (description != null) {
            voice.setDescription(description.trim());
        }
        if (gender != null && !gender.trim().isEmpty()) {
            voice.setGender(gender.trim());
        }

        // Update in database
        voiceMapper.updateVoiceAllFields(voice);

        return voice;
    }

    /**
     * Update voice gender only
     * @param voiceId Voice ID
     * @param gender New gender value
     * @param currentUser Current user (for authorization)
     * @return Updated voice
     */
    @Transactional
    public Voice updateVoiceGender(Long voiceId, String gender, User currentUser) {
        Voice voice = voiceMapper.getVoiceById(voiceId);
        if (voice == null) {
            throw new ZeusServiceException(ErrorCode.NotFound, "Voice not found for ID: " + voiceId);
        }

        // Check authorization - only allow users to update their own voices or super users to update any voice
        boolean isSuperUser = whiteListService.isSuperUser(currentUser.getEmail());

        if (voice.getUserId() != 0 && voice.getUserId() != currentUser.getId() && !isSuperUser) {
            throw ZeusServiceException.forbidden(
                    "User " + currentUser.getId() + " not authorized to update voice " + voiceId);
        }

        // Update gender
        voiceMapper.updateVoiceGender(voiceId, gender);

        // Return updated voice
        return voiceMapper.getVoiceById(voiceId);
    }
}
