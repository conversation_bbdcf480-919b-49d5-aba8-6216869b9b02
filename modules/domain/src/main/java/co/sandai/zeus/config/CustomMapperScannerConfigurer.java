package co.sandai.zeus.config;

import java.io.IOException;
import org.mybatis.spring.mapper.ClassPathMapperScanner;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.core.type.filter.TypeFilter;

/**
 * 自定义MyBatis扫描配置，用于排除VoiceProvider接口被错误识别为Mapper
 */
public class CustomMapperScannerConfigurer implements ImportBeanDefinitionRegistrar {

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        ClassPathMapperScanner scanner = new ClassPathMapperScanner(registry);

        // 添加类型过滤器，排除VoiceProvider接口
        scanner.addExcludeFilter(new TypeFilter() {
            @Override
            public boolean match(MetadataReader metadataReader, MetadataReaderFactory metadataReaderFactory)
                    throws IOException {
                String className = metadataReader.getClassMetadata().getClassName();
                return className.equals("co.sandai.zeus.domain.voice.service.provider.VoiceProvider");
            }
        });

        // 设置扫描包
        scanner.addIncludeFilter((metadataReader, metadataReaderFactory) -> true);
        scanner.scan("co.sandai.zeus.domain");
    }
}
