package co.sandai.zeus.domain.auth.service;

import static co.sandai.zeus.domain.auth.service.AuthService.KeyTypeEnum.USER_ID;

import co.sandai.zeus.domain.common.WhiteListService;
import co.sandai.zeus.domain.plan.UserPlanAuthService;
import jakarta.annotation.Resource;
import java.util.*;
import org.springframework.stereotype.Service;

@Service
public class AuthService {

    @Resource
    private UserPlanAuthService userPlanAuthService;

    @Resource
    private WhiteListService whiteListService;

    public enum KeyTypeEnum {
        USER_ID,
        EMAIL
    }

    private String buildWhiteListKey(KeyTypeEnum keyType, String authCode) {
        return "WHITE_LIST_FOR_AUTH_%%" + keyType.name() + "%%_" + authCode;
    }

    public boolean checkAuth(long userId, String authCode) {
        boolean hasAuth = userPlanAuthService.hasAuth(userId, authCode);
        return hasAuth || getWhiteList(USER_ID, authCode).contains(String.valueOf(userId));
    }

    /**
     *
     * @param keyType
     * @param authCode
     * @return notNull
     */
    private Set<String> getWhiteList(KeyTypeEnum keyType, String authCode) {
        String key = buildWhiteListKey(keyType, authCode);

        return whiteListService.getAndRefresh(key);
    }
}
