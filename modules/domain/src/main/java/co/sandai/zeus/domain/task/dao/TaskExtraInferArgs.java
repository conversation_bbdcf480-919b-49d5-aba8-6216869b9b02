package co.sandai.zeus.domain.task.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskExtraInferArgs {
    List<String> specialTokens = new ArrayList<>();
    String vaeModel = "";
    String resolution = "480p";
    String enhancementType;
    String extra = "";

    @JsonProperty("nSampleSteps")
    int nSampleSteps = 0;

    String modelVersion = "";

    boolean dryRun;
    boolean enableWatermark;
    boolean enableInputVideoToTs;
}
