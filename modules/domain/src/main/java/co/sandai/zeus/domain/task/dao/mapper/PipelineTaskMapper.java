package co.sandai.zeus.domain.task.dao.mapper;

import co.sandai.zeus.domain.task.dao.PipelineTask;
import java.util.List;
import org.apache.ibatis.annotations.*;

@Mapper
public interface PipelineTaskMapper {
    @Insert("INSERT INTO pipeline_task(id, pipeline_id, task_id, task_index) "
            + "VALUES(#{id}, #{pipelineId}, #{taskId}, #{taskIndex})")
    void insert(PipelineTask pipelineTask);

    @Select("SELECT * FROM pipeline_task WHERE pipeline_id = #{pipelineId} ORDER BY task_index")
    List<PipelineTask> getByPipelineId(Long pipelineId);

    @Select("SELECT * FROM pipeline_task WHERE task_id = #{taskId}")
    PipelineTask getByTaskId(Long taskId);

    @Select("SELECT task_id FROM pipeline_task WHERE pipeline_id = #{pipelineId} ORDER BY task_index")
    List<Long> getTaskIdsByPipelineId(Long pipelineId);

    @Select("SELECT task_id FROM pipeline_task WHERE pipeline_id = #{pipelineId} AND task_index = #{taskIndex}")
    Long getTaskIdByPipelineIdAndTaskIndex(@Param("pipelineId") Long pipelineId, @Param("taskIndex") Integer taskIndex);
}
