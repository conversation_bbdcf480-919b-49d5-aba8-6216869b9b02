package co.sandai.zeus.domain.share.service;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.utils.StringUtil;
import co.sandai.zeus.domain.share.dao.Share;
import co.sandai.zeus.domain.share.dao.ShareMapper;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.infra.IDGenerator;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ShareService {

    private final TaskService taskService;
    private final ShareMapper shareMapper;
    private final IDGenerator idGenerator;

    public ShareService(TaskService taskService, ShareMapper shareMapper, IDGenerator idGenerator) {
        this.taskService = taskService;
        this.shareMapper = shareMapper;
        this.idGenerator = idGenerator;
    }

    public Share shareTaskByTaskId(long taskId, long userId) {
        Task task = taskService.getTaskById(taskId);
        if (Objects.isNull(task)) {
            throw new ZeusServiceException(ErrorCode.NotFound, "task not found");
        }
        return shareTask(task, userId);
    }

    public Share shareTask(Task task, long userId) {
        if (task.getUserId() != userId) {
            throw ZeusServiceException.forbidden("can not share this task");
        }
        Share share = Share.builder().taskId(task.getId()).userId(userId).build();
        if (isTaskSharedTaskById(task.getId())) {
            return share;
        }
        shareMapper.insertTaskShare(share);
        return share;
    }

    public Share getShareById(long shareId) {
        return shareMapper.getShareById(shareId);
    }

    public Share shareGenerations(String content, long userId, List<Long> relatedGenerationIds) {
        // TODO: 上线 share Canvas 之前，暂时关闭这个校验，便于给投资人 share 官方 Canvas
        // if (!relatedGenerationIds.isEmpty()) {
        //     int count = taskService.getTaskCountByUser(relatedGenerationIds, userId);
        //     if (count != relatedGenerationIds.size()) {
        //         throw ZeusServiceException.forbidden("can not share generations that don't belong to you");
        //     }
        // }
        String contentHash = StringUtil.strHashHex(content);
        Share share = shareMapper.getShareByContentHash(contentHash);
        if (Objects.nonNull(share)) {
            return share;
        }
        share = Share.builder()
                .id(idGenerator.getNextId())
                .contentHash(contentHash)
                .content(content)
                .userId(userId)
                .build();
        shareMapper.insertGenerationShare(share);
        return share;
    }

    public boolean isTaskSharedTask(Task task) {
        Share share = shareMapper.getShareByTaskId(task.getId());
        return Objects.nonNull(share);
    }

    public boolean isTaskSharedTaskById(long taskId) {
        Share share = shareMapper.getShareByTaskId(taskId);
        return Objects.nonNull(share);
    }

    public void cancelShare(long taskId) {
        if (!isTaskSharedTaskById(taskId)) {
            throw new ZeusServiceException(ErrorCode.NotFound, "share record not found");
        }
        shareMapper.deleteShareByTaskId(taskId);
        log.info("task: {}, share record deleted", taskId);
    }

    public int getCountOfStaffPickedSharedTasks() {
        return shareMapper.getCountOfStaffPickedSharedTasks();
    }

    public List<Task> getStaffPickedSharedTasks(int limit, int offset) {
        return shareMapper.getStaffPickedSharedTasks(limit, offset);
    }

    public List<Task> getSharedTasks(int limit, int offset) {
        return shareMapper.getAllSharedTasks(limit, offset);
    }

    public List<Task> getSharedTasksByUserId(long userId, int limit, int offset) {
        return shareMapper.getUserSharedTasks(userId, limit, offset);
    }
}
