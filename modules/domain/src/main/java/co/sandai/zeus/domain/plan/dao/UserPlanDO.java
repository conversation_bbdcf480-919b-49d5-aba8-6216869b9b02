package co.sandai.zeus.domain.plan.dao;

import co.sandai.zeus.domain.payment.dao.OrderDO;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class UserPlanDO {

    private Long id;
    private String planCode;
    /**
     * userId是唯一约束，一个用户只能有一个订阅
     */
    private Long userId;

    /**
     * 计划过期时间
     */
    private Timestamp expireTime;
    /**
     * 下次发放credit时间
     */
    private Timestamp nextRenewTime;

    /**
     * 计划来源，标识该计划是如何获得的
     */
    private OrderDO.FromEnum from;

    private Timestamp createTime;
    private Timestamp modifiedTime;

    // 订阅的状态，不是plan的状态
    public enum SubscriptionStatusEnum {
        ACTIVE,
        CANCELLED,
        CANCEL_AT_PERIOD_END;

        public static SubscriptionStatusEnum fromString(String status, Boolean cancelAtPeriodEnd) {
            if (status == null) {
                return null;
            }
            /*
             * 详见 {@link com.stripe.model.Subscription#status}
             */
            if (status.equals("canceled")) {
                return CANCELLED;
            }

            if (status.equals("active")) {
                if (cancelAtPeriodEnd != null && cancelAtPeriodEnd) {
                    return CANCEL_AT_PERIOD_END; // 如果是active但是设置了取消订阅，那么也视为CANCELLED
                }
                return ACTIVE;
            }
            return null;
        }
    }
}
