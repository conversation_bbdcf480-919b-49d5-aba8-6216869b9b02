package co.sandai.zeus.domain.voice.service.provider;

import co.sandai.zeus.domain.voice.dto.VoiceDTO;
import co.sandai.zeus.infra.voice.client.ElevenLabsClient;
import co.sandai.zeus.infra.voice.dto.ElevenLabsVoiceDTO;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * Adapter that implements VoiceProvider interface using ElevenLabsClient
 * This class translates between domain layer DTOs and infrastructure layer DTOs
 */
@Service
public class ElevenLabsVoiceAdapter implements VoiceProvider {

    private final ElevenLabsClient elevenLabsClient;

    public ElevenLabsVoiceAdapter(ElevenLabsClient elevenLabsClient) {
        this.elevenLabsClient = elevenLabsClient;
    }

    @Override
    public String getProviderName() {
        return "elevenlabs";
    }

    @Override
    public List<VoiceDTO> getVoices() {
        List<ElevenLabsVoiceDTO> infraVoices = elevenLabsClient.getVoices();
        return infraVoices.stream().map(this::toVoiceDTO).collect(Collectors.toList());
    }

    @Override
    public VoiceDTO createVoice(String name, String description, List<MultipartFile> files) {
        ElevenLabsVoiceDTO infraVoice = elevenLabsClient.createVoice(name, description, files);
        return toVoiceDTO(infraVoice);
    }

    /**
     * Download voice preview content from preview URL
     * @param previewUrl The URL to download the preview from
     * @return Byte array containing the preview audio data
     */
    public byte[] downloadVoicePreview(String previewUrl) {
        return null;
    }

    @Override
    public byte[] textToSpeech(String voiceId, String text) {
        return elevenLabsClient.textToSpeech(voiceId, text);
    }

    @Override
    public boolean deleteVoice(String voiceId) {
        return elevenLabsClient.deleteVoice(voiceId);
    }

    /**
     * Convert infrastructure layer DTO to domain layer DTO
     */
    private VoiceDTO toVoiceDTO(ElevenLabsVoiceDTO infraVoice) {
        return VoiceDTO.builder()
                .id(infraVoice.getVoiceId())
                .name(infraVoice.getName())
                .description(infraVoice.getDescription())
                .language(infraVoice.getLanguage())
                .previewUrl(infraVoice.getPreviewUrl())
                .verifiedLanguages(infraVoice.getVerifiedLanguages())
                .isCloned(infraVoice.isCloned())
                .gender(extractGender(infraVoice))
                .build();
    }

    /**
     * Extract gender information from ElevenLabs voice data
     * @param infraVoice ElevenLabs voice DTO
     * @return Gender string or null if not available
     */
    private String extractGender(ElevenLabsVoiceDTO infraVoice) {
        // ElevenLabs may provide gender information in labels
        // For now, return null as we don't have the labels structure implemented
        // This can be enhanced once we have the full ElevenLabs voice details structure
        return null;
    }

    @Override
    public String speechToText(byte[] audioData, String language) {
        return elevenLabsClient.speechToText(audioData, language);
    }
}
