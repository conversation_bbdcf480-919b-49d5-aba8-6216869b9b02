package co.sandai.zeus.domain.plan.dao;

import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface UserPlanMapper {

    @Insert("insert into user_plan(id, plan_code, user_id, expire_time, next_renew_time, `from`) "
            + "values (#{id}, #{planCode}, #{userId}, #{expireTime}, #{nextRenewTime}, #{from})")
    void insert(UserPlanDO userPlanDO);

    @Select("select * from user_plan where user_id = #{userId}")
    UserPlanDO queryByUserId(Long userId);

    @Update("<script>update user_plan <set> " + "<if test='planCode != null'>plan_code = #{planCode},</if> "
            + "<if test='expireTime != null'>expire_time = #{expireTime},</if> "
            + "<if test='nextRenewTime != null'>next_renew_time = #{nextRenewTime},</if> "
            + "</set> where user_id = #{userId}</script>")
    int update(UserPlanDO userPlanDO);

    /**
     * 查询未过期的、需要renew credit的年计划。 并且要排除年续费最后一个月的情况
     * 条件顺序写成这样主要是为了索引优化
     * @param lowerId
     * @param batchSize
     * @param nextRenewTimeLowerBound
     * @param nextRenewTimeUpperBound
     * @return
     */
    @Select(
            """
            <script>select * from user_plan
            where plan_code in<foreach collection='planCodes' item='planCode' open='(' separator=',' close=')'>#{planCode}</foreach>
            and next_renew_time &lt;= #{nextRenewTimeUpperBound}
            <if test='lowerId != null'> and id &gt; #{lowerId}</if>
            and next_renew_time &gt; #{nextRenewTimeLowerBound}
            and expire_time &gt; #{expireTimeLowerBound}
            order by id asc
            limit #{batchSize}</script>
            """)
    List<UserPlanDO> queryYearlyRenewPlan(
            Long lowerId,
            int batchSize,
            List<String> planCodes,
            LocalDateTime nextRenewTimeLowerBound,
            LocalDateTime nextRenewTimeUpperBound,
            LocalDateTime expireTimeLowerBound);

    @Update("<script>update user_plan set next_renew_time = DATE_ADD(next_renew_time, INTERVAL 1 MONTH) where id in "
            + "<foreach collection='userPlans' item='userPlan' open='(' separator=',' close=')'>#{userPlan.id}</foreach> "
            + "</script>")
    void batchUpdateNextRenewTime(List<UserPlanDO> userPlans);
}
