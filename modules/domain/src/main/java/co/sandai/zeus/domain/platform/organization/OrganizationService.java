package co.sandai.zeus.domain.platform.organization;

import co.sandai.zeus.domain.platform.organization.dao.Organization;
import co.sandai.zeus.domain.platform.organization.dao.OrganizationMapper;
import co.sandai.zeus.infra.IDGenerator;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrganizationService {

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private IDGenerator iDGenerator;

    public Organization getOrCreatePersonalOrganization(long userId) {
        Organization organization = organizationMapper.getOrganizationByUserId(userId);
        if (Objects.isNull(organization)) {
            organization = new Organization();
            organization.setUserId(userId);
            organization.setName("Personal");
            organization.setId(iDGenerator.getNextId());
            organizationMapper.createOrganization(organization);
            log.info("Created personal organization: {}, userId: {}", organization, userId);
        }
        return organization;
    }
}
