package co.sandai.zeus.domain.voice.dao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Domain entity representing a Voice in the system
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Voice {
    private long id;
    private String voiceId; // Provider's voice ID
    private String name;
    private String description;
    private String language;
    private String previewUrl;
    private boolean isCloned;
    private String provider; // Name of voice provider
    private long userId;
    private long orgId;
    private String verifiedLanguages; // JSON string of supported languages with their details
    private String gender; // Voice gender: male, female

    @Builder.Default
    private boolean deleted = false; // Flag to indicate if voice is deleted

    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;
}
