package co.sandai.zeus.domain.user.service;

import co.sandai.zeus.domain.user.dao.Session;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.dao.UserMapper;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.Instant;
import java.util.Base64;
import java.util.Date;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class SessionService {

    private static final Logger log = LoggerFactory.getLogger(SessionService.class);

    @Value("${zeus.auth.redis.session-prefix:session:}")
    private String sessionCachePrefix;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedissonClient redissonClient;

    private String genSessionId(User user) {
        // 使用 SHA-256 哈希邮箱，生成 sessionId
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        String content = String.format("%s.%s", user.getEmail(), new Date().getTime());
        byte[] hash = digest.digest(content.getBytes());
        return Base64.getEncoder().encodeToString(hash);
    }

    private String getSessionKey(String token) {
        String key = sessionCachePrefix + token;
        log.debug("Generated Redis session key: {}", key);
        return key;
    }

    public Session saveSession(User user, int expireIn) {
        String token = this.genSessionId(user);
        Session session = new Session();
        session.setToken(token);
        session.setUserId(user.getId());
        session.setExpireIn(expireIn);
        Instant now = Instant.now();
        session.setCreateTime(now);
        session.setUpdateTime(now);

        // Cache in Redis
        String sessionKey = getSessionKey(token);
        RBucket<String> sessionBucket = redissonClient.getBucket(sessionKey);
        String sessionJson = JSON.toJSONString(session);
        sessionBucket.set(sessionJson, Duration.ofSeconds(expireIn));

        return session;
    }

    public Session getSession(String token) {
        // Try to get from Redis cache first
        String sessionKey = getSessionKey(token);
        RBucket<String> sessionBucket = redissonClient.getBucket(sessionKey);
        String sessionJson = sessionBucket.get();
        if (sessionJson == null) {
            log.warn("No session found in Redis for token: {}", token);
            return null;
        }
        return JSON.parseObject(sessionJson, Session.class);
    }

    public User getUserBySessionToken(String token) {
        Session session = getSession(token);
        if (session == null) {
            return null;
        }

        return userMapper.getUserById(session.getUserId());
    }
}
