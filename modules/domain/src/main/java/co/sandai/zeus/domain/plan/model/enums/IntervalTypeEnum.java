package co.sandai.zeus.domain.plan.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum IntervalTypeEnum {
    MONTHLY("MONTHLY"),

    YEARLY("YEARLY"),
    ;

    private String code;

    private IntervalTypeEnum findbyCode(String code) {
        if (code == null) {
            return null;
        }

        for (IntervalTypeEnum intervalTypeEnum : IntervalTypeEnum.values()) {
            if (intervalTypeEnum.getCode().equals(code)) {
                return intervalTypeEnum;
            }
        }
        return null;
    }
}
