package co.sandai.zeus.domain.favorite.dao;

import co.sandai.zeus.domain.task.dao.Task;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface TaskFavoriteMapper {
    @Insert("INSERT INTO favorite (id, task_id, favorite.user_id)" + "VALUE (#{fav.id}, #{fav.taskId}, #{fav.userId})")
    void insertFavorite(@Param("fav") Favorite fav);

    @Select("DELETE  from favorite WHERE task_id = #{taskId} AND favorite.user_id=#{userId}")
    void deleteUserFavoriteTask(@Param("userId") long userId, @Param("taskId") long taskId);

    @Select("SELECT COUNT(*) FROM favorite WHERE user_id = #{userId} AND task_id = #{taskId}")
    int userHasFavoriteTask(@Param("userId") long userId, @Param("taskId") long taskId);

    @Select("SELECT task.* FROM task "
            + "LEFT JOIN favorite f ON task.id = f.task_id "
            + "WHERE f.user_id = #{userId} "
            + "ORDER BY f.create_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<Task> getUserFavoriteTasks(
            @Param("userId") long userId, @Param("limit") int limit, @Param("offset") int offset);
}
