package co.sandai.zeus.domain.credit.dao;

import java.sql.Timestamp;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserCreditDO {

    private long id;
    private Long userId;
    private Long orgId;
    private String creditType;
    private Long amount;
    private Timestamp expireTime;
    private String requestId;
    /**
     * 临时积分，不可renew。 对subscription\free类型有效
     */
    private Boolean tempCredit;

    private boolean deleted;
    private Timestamp createTime;
    private Timestamp modifiedTime;
}
