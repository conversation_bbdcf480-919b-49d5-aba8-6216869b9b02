package co.sandai.zeus.domain.report.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "reports")
public class ReportReasonConfig {
    private List<ReasonCategory> categories;

    @Data
    public static class ReasonCategory {
        private String type; // Category type code
        private String name; // Display name
        private List<Tag> tags;
    }

    @Data
    public static class Tag {
        private String code; // Reason code
        private String description; // Display description
    }
}
