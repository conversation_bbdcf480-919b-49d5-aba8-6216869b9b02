package co.sandai.zeus.domain.asset.service;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.helper.FFmpegHelper;
import co.sandai.zeus.common.helper.ImageHelper;
import co.sandai.zeus.common.helper.LarkHelper;
import co.sandai.zeus.common.utils.StringUtil;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetMapper;
import co.sandai.zeus.domain.asset.dao.AssetMediaType;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.asset.dao.AssetVideoModerationStatus;
import co.sandai.zeus.domain.asset.dto.AssetFilterParam;
import co.sandai.zeus.domain.task.dao.ModerationFailType;
import co.sandai.zeus.domain.task.dao.mapper.TaskMapper;
import co.sandai.zeus.infra.Green;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.infer.ArtifactClient;
import co.sandai.zeus.infra.oss.OssClient;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class AssetService {
    private final AssetMapper assetMapper;
    private final RestTemplate restTemplate;
    private final TaskMapper taskMapper;
    private final Green green;
    private final IDGenerator idGenerator;
    public final OssClient ossClient;
    private final ArtifactClient artifactClient;
    private final StringRedisTemplate redisTemplate;

    @Value("${zeus.lark-robot-webhook.invalid-video:}")
    private String larkRobotWebhookInvalidVideo;

    @Value("${aliyun.uid:1552113873580453}")
    private String aliyunUid;

    public AssetService(
            AssetMapper assetMapper,
            IDGenerator idGenerator,
            RestTemplate restTemplate,
            TaskMapper taskMapper,
            Green green,
            OssClient ossClient,
            ArtifactClient artifactClient,
            StringRedisTemplate redisTemplate) {
        this.idGenerator = idGenerator;
        this.assetMapper = assetMapper;
        this.ossClient = ossClient;

        this.restTemplate = restTemplate;
        this.taskMapper = taskMapper;
        this.green = green;
        this.artifactClient = artifactClient;
        this.redisTemplate = redisTemplate;
    }

    public AssetMediaType getMediaTypeByFileExt(String ext) {
        return AssetMediaType.fromFileExtension(ext);
    }

    public List<Asset> filterUserManageableAssets(long userId, AssetFilterParam filterParam, int offset, int limit) {
        if (filterParam.getSources().length == 0 || filterParam.getTypes().length == 0) {
            return new ArrayList<>();
        }
        return assetMapper.filterAssets(userId, filterParam.getTypes(), filterParam.getSources(), offset, limit);
    }

    public int countUserManageableAssets(long userId, AssetFilterParam filterParam) {
        return assetMapper.countAssets(userId, filterParam.getTypes(), filterParam.getSources());
    }

    public void saveAssetPoster(Asset asset, Asset posterAsset) {
        long assetId = asset.getId();
        if (asset.getWidth() == 0) {
            assetMapper.updateAssetPosterAndSizeById(
                    assetId, posterAsset.getId(), posterAsset.getWidth(), posterAsset.getHeight());
        } else {
            assetMapper.updateAssetPosterById(assetId, posterAsset.getId());
        }
        log.info("save asset {} poster to: {}", assetId, posterAsset.getOssPath());
    }

    public String storeFileInS3(byte[] data, String ext, String name) {
        AssetMediaType mediaType = getMediaTypeByFileExt(ext);

        if (mediaType == null) {
            throw new ZeusServiceException(ErrorCode.AssetExtNotSupported);
        }

        String contentType = mediaType.contentType();
        String keyName = String.format("%s.%s", name, ext);
        ossClient.storeFileInS3(data, keyName, contentType);

        return ossClient.getPublicUrl(keyName, 60);
    }

    public Asset addAssetByBytes(
            byte[] bytes,
            long userId,
            long orgId,
            long taskId,
            String ext,
            AssetSource source,
            long index,
            int width,
            int height) {
        return this.addAssetByBytes(bytes, userId, orgId, taskId, ext, source, index, width, height, 0.0f, null, null);
    }

    public Asset addAssetByBytes(
            byte[] bytes,
            long userId,
            long orgId,
            long taskId,
            String ext,
            AssetSource source,
            long index,
            int width,
            int height,
            float duration) {
        return addAssetByBytes(bytes, userId, orgId, taskId, ext, source, index, width, height, duration, null, null);
    }

    public Asset addAssetByBytes(
            byte[] bytes,
            long userId,
            long orgId,
            long taskId,
            String ext,
            AssetSource source,
            long index,
            int width,
            int height,
            float duration,
            String filename) {
        return addAssetByBytes(
                bytes, userId, orgId, taskId, ext, source, index, width, height, duration, filename, null);
    }

    public Asset addAssetByBytes(
            byte[] bytes,
            long userId,
            long orgId,
            long taskId,
            String ext,
            AssetSource source,
            long index,
            int width,
            int height,
            float duration,
            String filename,
            Long voiceId) {
        long id = this.idGenerator.getNextId();

        AssetMediaType mediaType = getMediaTypeByFileExt(ext);
        if (mediaType == null) {
            throw new ZeusServiceException(ErrorCode.AssetExtNotSupported);
        }

        String urn = "";
        String ossPath = String.format("%s.%s", id, ext);
        OssClient.OSSFileMeta fileMeta = null;
        try {
            ArtifactClient.ArtifactUploadResponseDTO res = artifactClient.uploadArtifact(ossPath, ext, bytes);
            fileMeta = OssClient.parseOSSUrl(res.url());
            urn = res.urn();
        } catch (Exception e) {
            throw ZeusServiceException.internalError("upload to artifact failed", e);
        }

        Asset asset = Asset.builder()
                .id(id)
                .userId(userId)
                .orgId(orgId)
                .taskId(taskId)
                .width(width)
                .height(height)
                .urn(urn)
                .ossBucket(fileMeta.getBucket())
                .ossSource(fileMeta.getSource().toString())
                .ossPath(fileMeta.getPath())
                .mediaType(mediaType)
                .source(source)
                .index(index)
                .duration(duration)
                .filename(filename)
                .voiceId(voiceId)
                .build();

        this.assetMapper.insertAsset(asset);
        log.debug(
                "save asset to database id: {}, source: {}, bucket: {}, path: {}, width: {}, height: {}, duration: {}, ext: {}, filename: {}",
                id,
                fileMeta.getSource(),
                fileMeta.getBucket(),
                ossPath,
                width,
                height,
                duration,
                ext,
                filename);
        return asset;
    }

    public Asset addInferOutput(
            String ossBucket,
            String ossPath,
            String endpoint,
            String urn,
            long userId,
            long orgId,
            long taskId,
            Long taskChunkId,
            long chunkIndex,
            int width,
            int height,
            String ext,
            AssetSource source,
            float duration,
            Boolean watermarked) {
        AssetMediaType mediaType = getMediaTypeByFileExt(ext);
        Asset existingAsset = assetMapper.getByUrn(urn);
        if (existingAsset != null) {
            log.info(
                    "Asset with URN [{}] already exists. Skipping duplicate for taskId={}, userId={}",
                    urn,
                    taskId,
                    userId);
            return existingAsset;
        }
        Asset taskChunkAsset =
                assetMapper.getTsByChunk(taskId, taskChunkId, chunkIndex, mediaType.toString(), AssetSource.Generate);
        OssClient.OSSEndpointMeta endpointMeta = OssClient.parseOSSEndpoint(endpoint);
        long id = taskChunkAsset != null ? taskChunkAsset.getId() : this.idGenerator.getNextId();
        Asset asset = Asset.builder()
                .id(id)
                .userId(userId)
                .taskId(taskId)
                .orgId(orgId)
                .taskChunkId(taskChunkId)
                .index(chunkIndex)
                .mediaType(mediaType)
                .ossBucket(ossBucket)
                .ossSource(endpointMeta.getSource().toString())
                .ossPath(ossPath)
                .urn(urn)
                .width(width)
                .height(height)
                .duration(duration)
                .source(source)
                .build();

        if (taskChunkAsset != null) {
            assetMapper.overwriteAssetByTaskInfo(asset);
            log.info(
                    "Updated asset: id={}, taskId={}, taskChunkId={}, index={}, mediaType={}, ossBucket={}, ossPath={}, urn={}, width={}, height={}, duration={}, source={}, watermarked={}",
                    id,
                    taskId,
                    taskChunkId,
                    chunkIndex,
                    mediaType,
                    ossBucket,
                    ossPath,
                    urn,
                    width,
                    height,
                    duration,
                    source,
                    watermarked);
        } else {
            this.assetMapper.insertAsset(asset);
            log.info(
                    "Inserted asset: id={}, taskId={}, taskChunkId={}, index={}, mediaType={}, ossBucket={}, ossPath={}, urn={}, width={}, height={}, duration={}, source={}, watermarked={}",
                    id,
                    taskId,
                    taskChunkId,
                    chunkIndex,
                    mediaType,
                    ossBucket,
                    ossPath,
                    urn,
                    width,
                    height,
                    duration,
                    source,
                    watermarked);
        }
        return asset;
    }

    public void moderateVideoAsset(Asset videoAsset) {
        try {
            /*
             这里强制使用 internal endpoint, 所以必须保证审核服务和 oss 仓库为同一个 VPC
             TODO：支持多 OSS 存储 artifacts 的时候需要调整
            */
            String videoUrl = getAssetOssPublicUrl(videoAsset, 5, true);
            Green.SubmitVideoModerationTaskResult result = green.submitVideoModerationTask(videoUrl, videoAsset.id);
            assetMapper.updateVideoAssetModerateInfo(
                    videoAsset.id, AssetVideoModerationStatus.Running, result.getTaskId(), result.getSeed());
            log.info(
                    "video moderation task submitted, moderationTaskId: {}, assetId: {}",
                    result.getTaskId(),
                    videoAsset.id);
        } catch (Exception e) {
            // 审核逻辑不影响主链路
            String message = String.format("moderate video asset %d failed: ", videoAsset.getId());
            log.error(message, e);
        }
    }

    public Green.ModerateImageResult moderateImageAsset(Asset imageAsset) {
        /*
         这里强制使用 internal endpoint, 所以必须保证审核服务和 oss 仓库为同一个 VPC
         TODO：支持多 OSS 存储 artifacts 的时候需要调整
        */
        String imageUrl = getAssetOssPublicUrl(imageAsset, 5, true);
        return green.moderateImage(imageUrl, imageAsset.id);
    }

    public Asset parseVideoModerationResult(Green.ModerateVideoCallbackRequest result) {
        log.info(
                "received video moderation result, checksum: {}, content: {}",
                result.getChecksum(),
                result.getContent());
        Green.ModerateVideoResult res = green.parseVideoModerationCallbackRequest(result);
        long assetId = res.getDataId();
        Asset asset = getAssetById(assetId);
        if (Objects.isNull(asset)) {
            throw ZeusServiceException.notFound("asset not found with asset id: " + assetId);
        }

        String expectedCheckSum =
                StringUtil.strSHA256Hex(aliyunUid + asset.getVideoModerationSeed() + result.getContent());
        if (!expectedCheckSum.equals(result.getChecksum())) {
            throw ZeusServiceException.badRequest("checksum mismatch");
        }

        // 使用包含置信度的方法更新资源审核结果
        assetMapper.updateVideoModerateResult(
                assetId, res.isValid(), AssetVideoModerationStatus.Completed, res.getConfidence());

        // 更新内存中的资源对象
        asset.setVideoModerationStatus(AssetVideoModerationStatus.Completed);
        asset.setModerationPassed(res.isValid());
        asset.setModerationConfidence(res.getConfidence());

        // 更新相关联的任务记录，统一设置置信度和失败类型(如果失败)
        if (asset.getTaskId() > 0) {
            ModerationFailType failType = res.isValid() ? null : ModerationFailType.RESULT_VIDEO;
            taskMapper.updateTaskModerationResult(asset.getTaskId(), res.isValid(), failType, res.getConfidence());
        }

        log.info(
                "Process video moderation result complete: asset_id={}, result={}, taskId={}, confidence={}",
                asset.id,
                res.isValid(),
                asset.getTaskId(),
                res.getConfidence());

        // 审核未通过时发送警报
        if (!res.isValid()) {
            CompletableFuture.runAsync(() -> {
                String message = String.format(
                        "Invalid Result Video: %d, confidence: %.2f, url: %s",
                        assetId, res.getConfidence(), getAssetPublicUrl(asset, 60));
                LarkHelper.sendLarkBotMessage(larkRobotWebhookInvalidVideo, message);
            });
        }

        return asset;
    }

    private Asset addCheckedAssetByBytes(
            byte[] data, long userId, long orgId, String ext, String filename, AssetSource source, Long voiceId) {
        if (data == null || data.length == 0) {
            log.error("addAssetByUploadData error, no data, userId: {}, orgId: {}, ext: {}", userId, orgId, ext);
            throw new ZeusServiceException(ErrorCode.AssetInvalidContent, "No asset content to upload.");
        }

        int width = 0;
        int height = 0;

        // Audio metadata variables (will be used later if this is an audio file)
        float duration = 0.0f;
        int sampleRate = 0;
        int channels = 0;
        int bitrate = 0;

        AssetMediaType mediaType = getMediaTypeByFileExt(ext);
        if (mediaType == null) {
            throw new ZeusServiceException(ErrorCode.AssetExtNotSupported);
        }

        // Validate image files
        if (mediaType.isImage()) {
            // Try to fix image orientation first
            data = ImageHelper.tryFixImageOrientation(data);

            // Validate the image
            log.debug("validateImage start");
            ImageHelper.ImageValidationResult validationResult = ImageHelper.validateImage(data);
            log.debug("validateImage end");
            if (!validationResult.isValid()) {
                log.error(
                        "addAssetByUploadData error, invalid image, userId: {}, orgId: {}, ext: {}, error: {}",
                        userId,
                        orgId,
                        ext,
                        validationResult.getErrorMessage());
                throw new ZeusServiceException(ErrorCode.AssetInvalidContent, validationResult.getErrorMessage());
            }

            // 使用验证结果中的尺寸信息，避免重复调用 getMediaSize
            width = validationResult.getWidth();
            height = validationResult.getHeight();
        }
        // Validate video files
        else if (mediaType.isVideo()) {
            // Validate the video
            log.debug("validateVideo start");
            FFmpegHelper.VideoValidationResult validationResult = FFmpegHelper.validateVideo(data);
            log.debug("validateVideo end");
            if (!validationResult.isValid()) {
                log.error(
                        "addAssetByUploadData error, invalid video, userId: {}, orgId: {}, ext: {}, error: {}",
                        userId,
                        orgId,
                        ext,
                        validationResult.getErrorMessage());
                throw new ZeusServiceException(ErrorCode.AssetInvalidContent, validationResult.getErrorMessage());
            }

            // 使用验证结果中的尺寸信息，避免重复调用 getMediaSize
            width = validationResult.getWidth();
            height = validationResult.getHeight();
            duration = validationResult.getDuration();
        }
        // Validate audio files
        else if (mediaType.isAudio()) {
            // Validate the audio and extract metadata
            FFmpegHelper.AudioValidationResult validationResult = FFmpegHelper.validateAudio(data);
            if (!validationResult.isValid()) {
                log.error(
                        "addAssetByUploadData error, invalid audio, userId: {}, orgId: {}, ext: {}, error: {}",
                        userId,
                        orgId,
                        ext,
                        validationResult.getErrorMessage());
                throw new ZeusServiceException(ErrorCode.AssetInvalidContent, validationResult.getErrorMessage());
            }

            // For audio files, we don't need width/height
            width = 0;
            height = 0;

            // Save audio metadata for use after asset creation
            duration = (float) validationResult.getDuration();
            sampleRate = validationResult.getSampleRate();
            channels = validationResult.getChannels();
            bitrate = validationResult.getBitrate();

            // Log audio metadata
            log.debug(
                    "Audio metadata - duration: {}, sampleRate: {}, channels: {}, bitrate: {}",
                    duration,
                    sampleRate,
                    channels,
                    bitrate);
        }

        Asset asset =
                addAssetByBytes(data, userId, orgId, 0, ext, source, 0, width, height, duration, filename, voiceId);

        if (asset.mediaType.isAudio()) {
            // TODO: 音频审核 - 可以在未来实现音频内容审核

        } else if (asset.mediaType.isImage()) {
            Green.ModerateImageResult result = moderateImageAsset(asset);
            if (!result.isValid()) {
                log.warn("user {}, upload a unsafe image, id: {}", userId, asset.id);
                assetMapper.updateAssetModerateResult(asset.id, false, result.getConfidence()); // 审核失败时使用中等置信度
                throw new ZeusServiceException(ErrorCode.InvalidParameters, "Please try again with a different image.");
            }
        } else if (asset.mediaType.isVideo()) {
            try {
                Asset posterAsset = addKeyFrameAsset(new ByteArrayInputStream(data), userId, orgId, 0);
                saveAssetPoster(asset, posterAsset);
                asset.setPosterAssetId(posterAsset.getId());
                Green.ModerateImageResult result = moderateImageAsset(posterAsset);
                if (!result.isValid()) {
                    log.warn(
                            "user {}, upload a video with unsafe poster, video id: {}, poster id: {}, reason: {}",
                            userId,
                            asset.id,
                            posterAsset.id,
                            result.getReason());
                    assetMapper.updateAssetModerateResult(
                            posterAsset.id, false, result.getConfidence()); // 审核失败时使用中等置信度
                    assetMapper.updateAssetModerateResult(asset.id, false, result.getConfidence()); // 审核失败时使用中等置信度
                    throw new ZeusServiceException(
                            ErrorCode.InvalidParameters, "Please try again with a different image.");
                }
            } catch (Exception e) {
                log.error("get keyframe of uploaded video error", e);
                assetMapper.deleteAsset(asset.getId());
                throw new ZeusServiceException(ErrorCode.AssetInvalidContent, "invalid video content");
            }
        }
        return asset;
    }

    // public Asset addTextToSpeechAsset(byte[] data, long userId, long orgId, String ext, String filename) {
    //     return this.addCheckedAssetByBytes(data, userId, orgId, ext, filename, AssetSource.TextToSpeech);
    // }

    public Asset addUploadAsset(byte[] data, long userId, long orgId, String ext, String filename, AssetSource source) {
        if (source != AssetSource.Upload && source != AssetSource.AvatarUpload) {
            throw ZeusServiceException.badRequest("Only Upload and AvatarUpload assets supported.");
        }
        return this.addCheckedAssetByBytes(data, userId, orgId, ext, filename, source, null);
    }

    public Asset addGenerateAsset(byte[] data, long userId, long orgId, String ext, String filename) {
        return this.addCheckedAssetByBytes(data, userId, orgId, ext, filename, AssetSource.Generate, null);
    }

    public Asset addTextToSpeechAsset(byte[] data, long userId, long orgId, String ext, String filename, Long voiceId) {
        return this.addCheckedAssetByBytes(data, userId, orgId, ext, filename, AssetSource.TextToSpeech, voiceId);
    }

    public Asset addAssetByWebUrl(String url, long userId, long orgId, String ext, AssetSource source) {
        byte[] data = restTemplate.getForObject(url, byte[].class);
        return this.addAssetByBytes(data, userId, orgId, 0, ext, source, 0, 0, 0, 0.0f);
    }

    public InputStream getAssetInputStream(Asset asset) {
        return ossClient
                .newInstanceFromSource(asset.getOssProviderSourceEnum(), asset.getOssBucket())
                .getInputStream(asset.getOssPath());
    }

    public InputStream getAssetInputStreamById(long id) {
        Asset asset = this.assetMapper.getAssetById(id);
        return getAssetInputStream(asset);
    }

    public Asset addKeyFrameAsset(InputStream inputStream, long userId, long orgId, long taskId) {
        FFmpegHelper.ExtractFrameResult result = FFmpegHelper.extractKeyFrame(inputStream);
        if (Objects.isNull(result)) {
            throw new ZeusServiceException(ErrorCode.InternalError);
        }
        return addAssetByBytes(
                result.getFrameData(),
                userId,
                orgId,
                taskId,
                "jpeg",
                AssetSource.ExtractVideoPoster,
                0,
                result.getWidth(),
                result.getHeight(),
                0.0f);
    }

    public List<Asset> getContinueChunkAssetByTaskId(long taskId) {
        List<Asset> assetList =
                assetMapper.getChunkAssetsByTaskId(taskId, AssetMediaType.TransportStream, AssetSource.Generate);
        return assetList;
    }

    public Asset getTsOfInputVideoByTaskId(long taskId) {
        List<Asset> assetList =
                assetMapper.getChunkAssetsByTaskId(taskId, AssetMediaType.TransportStream, AssetSource.SourceVideoToTs);
        if (assetList.isEmpty()) {
            return null;
        }
        return assetList.get(0);
    }

    public List<Asset> getContinueChunkAssetByTaskChunk(long taskChunkId) {
        return assetMapper.findByTaskChunkId(taskChunkId);
    }

    public void updateUrn(long id, String urn) {
        assetMapper.updateAssetUrn(id, urn);
    }

    public Map<Long, String> getAssetPublicUrlByIds(List<Asset> assets, int expirationMinutes) {
        if (assets == null || assets.isEmpty()) {
            return Map.of();
        }

        Map<Long, String> result = new HashMap<>();
        for (Asset asset : assets) {
            String url = this.ossClient
                    .newInstanceFromSource(asset.getOssProviderSourceEnum(), asset.getOssBucket())
                    .getPublicUrl(asset.getOssPath(), expirationMinutes);
            result.put(asset.getId(), url);
        }

        return result;
    }

    public String getAssetPublicUrl(Asset asset, int expirationMinutes) {
        if (asset == null) {
            return "";
        }
        return getAssetPublicUrlByIds(List.of(asset), expirationMinutes).getOrDefault(asset.getId(), "");
    }

    /**
     * 强制获取 oss 的公共 URL，使用场景：提供给类似内容审核等 阿里云内网服务等
     */
    public String getAssetOssPublicUrl(Asset asset, int expirationMinutes, boolean useInternalEndpoint) {
        return this.ossClient
                .newInstanceFromSource(asset.getOssProviderSourceEnum(), asset.getOssBucket(), useInternalEndpoint)
                .createPreSignedGetUrl(asset.getOssPath(), expirationMinutes);
    }

    public byte[] getAssetBytes(Asset asset) {
        InputStream res = ossClient
                .newInstanceFromSource(asset.getOssProviderSourceEnum(), asset.getOssBucket())
                .getInputStream(asset.getOssPath());
        try {
            return res.readAllBytes();
        } catch (IOException e) {
            log.error(String.format("read asset %s bytes error", asset.getId()), e);
            throw new ZeusServiceException(ErrorCode.InternalError, "read asset bytes error");
        }
    }

    public Asset getAssetById(long id) {
        return assetMapper.getAssetById(id);
    }

    /**
     * 添加从MP4转换的TS输入文件
     * 注意：对于ExtendDuration任务，始终使用无水印版本的输入
     *
     * @param ossBucket OSS存储桶
     * @param ossPath OSS路径
     * @param endpoint OSS端点
     * @param urn 资源URN
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param width 宽度
     * @param height 高度
     * @param sourceMp4Urn 原始MP4的URN
     * @param duration 时长
     * @return 创建的Asset对象
     */
    public Asset addConvertedTsFromMp4(
            String ossBucket,
            String ossPath,
            String endpoint,
            String urn,
            long userId,
            long taskId,
            int width,
            int height,
            String sourceMp4Urn,
            float duration,
            Boolean watermarked) {

        AssetMediaType mediaType = getMediaTypeByFileExt("ts");
        Asset existingAsset = getTsOfInputVideoByTaskId(taskId);
        OssClient.OSSEndpointMeta endpointMeta = OssClient.parseOSSEndpoint(endpoint);
        long id = existingAsset != null ? existingAsset.getId() : this.idGenerator.getNextId();

        Asset asset = Asset.builder()
                .id(id)
                .userId(userId)
                .taskId(taskId)
                .taskChunkId(null) // 没有关联的step
                .index(0) // 没有关联的chunk
                .mediaType(mediaType)
                .ossBucket(ossBucket)
                .ossSource(endpointMeta.getSource().toString())
                .ossPath(ossPath)
                .urn(urn)
                .width(width)
                .height(height)
                .duration(duration)
                .source(AssetSource.SourceVideoToTs)
                .build();

        if (existingAsset != null) {
            log.info(
                    "Updating existing asset for task {} with converted TS file. Asset ID: {}, URN: {}, Source MP4: {}",
                    taskId,
                    id,
                    urn,
                    sourceMp4Urn);
            assetMapper.overwriteAssetByTaskInfo(asset);
        } else {
            log.info(
                    "Creating new asset for task {} with converted TS file. Asset ID: {}, URN: {}, Source MP4: {}",
                    taskId,
                    id,
                    urn,
                    sourceMp4Urn);
            this.assetMapper.insertAsset(asset);
        }

        return asset;
    }

    public byte[] getAssetBytesById(long id) {
        Asset asset = assetMapper.getAssetById(id);
        if (asset == null) {
            return null;
        }
        return getAssetBytes(asset);
    }

    public List<Asset> getAssetsByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        return assetMapper.getAssetsByIds(ids);
    }

    public String getAssetPublicUrlById(long assetId, int expirationMinutes) {
        Asset asset = getAssetById(assetId);
        if (asset == null) {
            return "";
        }
        return getAssetPublicUrl(asset, expirationMinutes);
    }

    public String getAssetPreviewUrl(Asset asset, int expiresInMinutes) {
        if (asset == null) {
            return "";
        }
        if (asset.getPosterAssetId() <= 0) {
            return getAssetPublicUrl(asset, expiresInMinutes);
        }
        Asset poster = getAssetById(asset.getPosterAssetId());
        return poster != null ? getAssetPublicUrl(poster, expiresInMinutes) : "";
    }

    public List<String> getAssetPublicUrls(List<Asset> assets, int expirationMinutes) {
        if (assets == null || assets.isEmpty()) {
            return List.of();
        }
        Map<Long, String> urlMap = getAssetPublicUrlByIds(assets, expirationMinutes);
        return assets.stream()
                .map(asset -> urlMap.getOrDefault(asset.getId(), ""))
                .toList();
    }

    public void deleteAssetById(Long id) {
        assetMapper.logitDelete(id);
    }

    public byte[] mergeTsList(List<Asset> assets) {
        byte[][] tsDataArr = new byte[assets.size()][];
        for (int i = 0; i < assets.size(); i++) {
            tsDataArr[i] = getAssetBytes(assets.get(i));
        }
        try {
            return FFmpegHelper.mergeTsToMp4(tsDataArr);
        } catch (IOException | InterruptedException e) {
            log.error(e.getMessage(), e);
            throw new ZeusServiceException(ErrorCode.InternalError);
        }
    }

    /**
     * Streams TS files to MP4 format directly to the provided output stream
     * This eliminates the need to load all files into memory at once
     *
     * @param assets The list of TS assets to convert and stream
     * @param outputStream The output stream to write the MP4 content to
     * @throws IOException If an I/O error occurs during streaming
     * @throws InterruptedException If the process is interrupted
     */
    /**
     * 并行下载TS文件到指定目录
     *
     * @param assets 需要下载的TS资产列表
     * @param tempDir 临时目录，用于存储下载的TS文件
     * @return 下载的TS文件列表
     */
    public List<File> downloadTsFilesInParallel(List<Asset> assets, File tempDir) {
        log.info("Starting parallel download of {} TS files", assets.size());
        long startTime = System.currentTimeMillis();

        // 创建并发下载任务
        List<CompletableFuture<File>> fileFutures = new ArrayList<>();
        for (int i = 0; i < assets.size(); i++) {
            final int index = i;
            fileFutures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    Asset asset = assets.get(index);
                    File tsFile = new File(tempDir, "file" + index + ".ts");
                    log.debug("Downloading TS file for asset {}: {}", asset.getId(), asset.getOssPath());

                    byte[] assetBytes = getAssetBytes(asset);
                    try (FileOutputStream fos = new FileOutputStream(tsFile)) {
                        fos.write(assetBytes);
                    }
                    log.debug("Downloaded TS file: {}, size: {} bytes", tsFile.getName(), tsFile.length());
                    return tsFile;
                } catch (Exception e) {
                    log.error("Error downloading TS file at index {}: {}", index, e.getMessage(), e);
                    throw new CompletionException(e);
                }
            }));
        }

        // 等待所有下载完成并收集文件
        List<File> tsFiles = fileFutures.stream().map(CompletableFuture::join).collect(Collectors.toList());

        log.info(
                "Downloaded {} TS files in parallel, time taken: {}ms",
                tsFiles.size(),
                System.currentTimeMillis() - startTime);

        return tsFiles;
    }

    public void streamTsListToMp4(List<Asset> assets, OutputStream outputStream, boolean enableWatermark)
            throws IOException, InterruptedException {
        if (assets == null || assets.isEmpty()) {
            throw new IllegalArgumentException("Asset list cannot be empty for streaming conversion");
        }

        log.info("Starting streaming conversion of {} TS files to MP4", assets.size());
        long startTime = System.currentTimeMillis();

        // Create temporary directory for TS files
        File tempDir = Files.createTempDirectory("streaming_ts_files").toFile();
        tempDir.deleteOnExit();

        // We'll use a list to track all the temporary files we create
        List<File> tempFiles = new ArrayList<>();

        try {
            // 使用抽象的并行下载方法
            List<File> tsFiles = downloadTsFilesInParallel(assets, tempDir);
            tempFiles.addAll(tsFiles);

            // 验证下载的文件列表不为空
            if (tsFiles.isEmpty()) {
                throw new ZeusServiceException(
                        ErrorCode.InvalidParameters,
                        "Downloaded TS files list is empty, cannot proceed with conversion");
            }

            // Stream the conversion directly to the output stream
            FFmpegHelper.streamTsToMp4(tsFiles, outputStream, enableWatermark);

            log.info("Completed streaming conversion, total time: {}ms", System.currentTimeMillis() - startTime);

        } finally {
            // Clean up all temporary files
            for (File file : tempFiles) {
                try {
                    if (file.exists()) {
                        file.delete();
                    }
                } catch (Exception e) {
                    log.warn("Failed to delete temporary file: {}", file.getAbsolutePath(), e);
                }
            }

            // Clean up the temp directory
            if (tempDir.exists()) {
                try {
                    tempDir.delete();
                } catch (Exception e) {
                    log.warn("Failed to delete temporary directory: {}", tempDir.getAbsolutePath(), e);
                }
            }
        }
    }
}
