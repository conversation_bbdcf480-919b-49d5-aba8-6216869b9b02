package co.sandai.zeus.domain.project.dao;

import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface ProjectMapper {

    @Select(
            "SELECT * FROM project WHERE user_id = #{userId} AND deleted = 0 order by create_time desc LIMIT #{limit} OFFSET #{offset}")
    List<Project> getProjectsByUserId(
            @Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);

    @Select("SELECT COUNT(*) FROM project WHERE user_id = #{userId} AND deleted = 0")
    int countProjectsByUserId(@Param("userId") Long userId);

    @Select("SELECT * FROM project WHERE id = #{id} AND deleted = 0")
    Project getProjectById(@Param("id") Long id);

    @Insert("INSERT INTO project (id, user_id, canvas_json, title, poster_asset_id, create_time, update_time) "
            + "VALUES (#{id}, #{userId}, #{canvasJson}, #{title}, #{posterAssetId}, NOW(), NOW())")
    void insertProject(Project project);

    @Update("UPDATE project SET title = #{title}, " + "poster_asset_id = #{posterAssetId} "
            + "WHERE id = #{id} AND deleted = 0")
    void updateProject(Project project);

    @Update("UPDATE project SET deleted = 1, update_time = NOW() WHERE id = #{id}")
    void deleteProject(@Param("id") Long id);

    @Update("UPDATE project SET canvas_json = #{canvasJson}, update_time = NOW() WHERE id = #{id} AND deleted = 0")
    void updateCanvasJson(@Param("id") Long id, @Param("canvasJson") String canvasJson);

    @Update(
            "UPDATE project SET poster_asset_id = #{posterAssetId}, update_time = NOW() WHERE id = #{id} AND deleted = 0")
    void updatePosterAssetId(@Param("id") Long id, @Param("posterAssetId") Long posterAssetId);
}
