package co.sandai.zeus.domain.task;

import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;

public class HighQualityStrategyUtils {

    public static int creditTimesAccordingToQuality(TaskExtraInferArgs taskExtraInferArgs) {
        // 32步以上是high quality，倍数2
        if (taskExtraInferArgs.getNSampleSteps() >= 32) {
            return 2;
        }
        // 其他（16步）普通质量，倍数1
        return 1;
    }
}
