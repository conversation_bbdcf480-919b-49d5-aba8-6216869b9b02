package co.sandai.zeus.domain.user.dao;

import java.util.List;
import org.apache.ibatis.annotations.*;

@Mapper
public interface UserMapper {

    @Select("select * from user where id=#{id}")
    User getUserById(long id);

    @Select("select * from user where email = #{email}")
    User getUserByEmail(String email);

    @Insert(
            "insert into user(id, nickname, source, email, password, is_email_verified) values (#{user.id}, #{user.nickname}, #{user.source}, #{user.email}, #{user.password}, #{user.isEmailVerified})")
    void insertUser(@Param("user") User user);

    @Update("UPDATE user SET avatar_id=#{avatarId} WHERE id=#{id}")
    void updateUserAvatarId(long id, long avatarId);

    @Update("UPDATE user SET is_email_verified=#{isEmailVerified} WHERE id=#{id}")
    void updateEmailVerifyResult(long id, boolean isEmailVerified);

    @Update("UPDATE user SET password=#{password} WHERE id=#{id}")
    void updateUserPasswordById(long id, String password);

    @Update("UPDATE user SET nickname=#{nickname}, description=#{description}, avatar_id=#{avatarId} WHERE id=#{id}")
    void updateUserProfileById(long id, String nickname, String description, long avatarId);

    @Update("UPDATE user SET out_customer_id=#{outCustomerId}, out_customer_type=#{outCustomerType} WHERE id=#{id}")
    void updateOutCustomer(long id, String outCustomerId, String outCustomerType);

    @Select("select * from user where out_customer_id=#{outCustomerId}")
    User getUserByOutCustomerId(String outCustomerId);

    @Select(
            """
            <script>
                select * from user
                <if test="lowerId != null">
                    where id > #{lowerId}
                </if>
                order by id asc
                limit #{pageSize}
            </script>
            """)
    List<User> getUsers(Long lowerId, int pageSize);
}
