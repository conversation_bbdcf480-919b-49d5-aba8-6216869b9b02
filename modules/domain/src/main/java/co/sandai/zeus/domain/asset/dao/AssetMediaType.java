package co.sandai.zeus.domain.asset.dao;

import lombok.Getter;

@Getter
public enum AssetMediaType {
    // image
    JPEG,
    PNG,
    WEBP,

    // video
    MP4,
    TransportStream,

    // audio
    WAV,
    MP3;

    public String contentType() {
        if (this == WEBP) {
            return "image/webp";
        }
        if (this == JPEG) {
            return "image/jpeg";
        }
        if (this == PNG) {
            return "image/png";
        }
        if (this == MP4) {
            return "video/mp4";
        }
        if (this == TransportStream) {
            return "video/mp2t";
        }
        if (this == WAV) {
            return "audio/wav";
        }
        if (this == MP3) {
            return "audio/mpeg";
        }
        return "";
    }

    public boolean isImage() {
        return this == JPEG || this == PNG || this == WEBP;
    }

    public boolean isVideo() {
        return this == MP4 || this == TransportStream;
    }

    public boolean isAudio() {
        return this == WAV || this == MP3;
    }

    public static AssetMediaType fromFileExtension(String fileExtension) {
        String fileExtensionLowerCase = fileExtension.toLowerCase();
        return switch (fileExtensionLowerCase) {
            case "mp4" -> AssetMediaType.MP4;
            case "ts" -> AssetMediaType.TransportStream;
            case "jpeg", "jpg" -> AssetMediaType.JPEG;
            case "png" -> AssetMediaType.PNG;
            case "webp" -> AssetMediaType.WEBP;
            case "wav" -> AssetMediaType.WAV;
            case "mp3" -> AssetMediaType.MP3;
            default -> null;
        };
    }
}
