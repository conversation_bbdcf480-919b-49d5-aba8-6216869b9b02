package co.sandai.zeus.domain.platform.apikey;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.utils.StringUtil;
import co.sandai.zeus.domain.platform.apikey.dao.ApiKey;
import co.sandai.zeus.domain.platform.apikey.dao.ApiKeyMapper;
import co.sandai.zeus.infra.IDGenerator;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ApiKeyService {

    private final ApiKeyMapper apiKeyMapper;
    private final IDGenerator iDGenerator;

    public ApiKeyService(ApiKeyMapper apiKeyMapper, IDGenerator iDGenerator) {
        this.apiKeyMapper = apiKeyMapper;
        this.iDGenerator = iDGenerator;
    }

    public List<ApiKey> getOrgApiKeys(long orgId) {
        return apiKeyMapper.getOrgApiKeys(orgId);
    }

    public ApiKey getApiKeyByValue(String apiKeyValue) {
        return apiKeyMapper.getApiKeyByValue(apiKeyValue);
    }

    public void deleteApiKeyById(long id) {
        apiKeyMapper.deleteApiKey(id);
    }

    public ApiKey createApiKey(long userId, long organizationId, String name) {
        int count = apiKeyMapper.countOrgApiKeys(organizationId);

        if (count >= 3) {
            throw ZeusServiceException.badRequest("Cannot create more than 3 api keys.");
        }

        ApiKey apiKey = new ApiKey();
        apiKey.setUserId(userId);
        apiKey.setOrgId(organizationId);
        apiKey.setId(iDGenerator.getNextId());
        apiKey.setName(name);
        apiKey.setValue(createApiKeyValue());
        apiKeyMapper.createApiKey(apiKey);
        log.info("Created api key, orgId: {}, userId: {}", apiKey.getOrgId(), apiKey.getUserId());

        apiKey.setCreateTime(LocalDateTime.now());
        apiKey.setUpdateTime(LocalDateTime.now());

        return apiKey;
    }

    public String createApiKeyValue() {
        return StringUtil.randomString(48);
    }
}
