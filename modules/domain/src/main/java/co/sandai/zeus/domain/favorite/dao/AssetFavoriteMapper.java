package co.sandai.zeus.domain.favorite.dao;

import co.sandai.zeus.domain.asset.dao.AssetMediaType;
import java.util.List;
import org.apache.ibatis.annotations.*;

@Mapper
public interface AssetFavoriteMapper {
    @Insert(
            "INSERT INTO asset_favorite (id, user_id, asset_id, media_type) VALUES (#{id}, #{userId}, #{assetId}, #{mediaType})")
    void insertAssetFavorite(AssetFavorite favorite);

    @Select("SELECT COUNT(*) FROM asset_favorite WHERE user_id = #{userId} AND asset_id = #{assetId}")
    int userHasFavoriteAsset(@Param("userId") long userId, @Param("assetId") long assetId);

    @Delete("DELETE FROM asset_favorite WHERE user_id = #{userId} AND asset_id = #{assetId}")
    int deleteUserFavoriteAsset(@Param("userId") long userId, @Param("assetId") long assetId);

    @Select("<script>" + "SELECT * FROM asset_favorite "
            + "WHERE user_id = #{userId} "
            + "<if test='types != null and types.length > 0'>"
            + "  AND media_type IN "
            + "  <foreach collection='types' item='type' open='(' separator=',' close=')'>"
            + "    #{type}"
            + "  </foreach>"
            + "</if>"
            + "ORDER BY id DESC "
            + "LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<AssetFavorite> getUserFavoriteAssets(
            @Param("userId") long userId,
            @Param("types") AssetMediaType[] types,
            @Param("limit") int limit,
            @Param("offset") int offset);

    @Select("<script>" + "SELECT COUNT(*) FROM asset_favorite "
            + "WHERE user_id = #{userId} "
            + "<if test='types != null and types.length > 0'>"
            + "  AND media_type IN "
            + "  <foreach collection='types' item='type' open='(' separator=',' close=')'>"
            + "    #{type}"
            + "  </foreach>"
            + "</if>"
            + "</script>")
    int getFavoriteAssetCount(@Param("userId") long userId, @Param("types") AssetMediaType[] types);

    @Select("<script>" + "SELECT asset_id FROM asset_favorite "
            + "WHERE user_id = #{userId} AND asset_id IN "
            + "<foreach item='id' collection='assetIds' open='(' separator=',' close=')'>"
            + "#{id}"
            + "</foreach>"
            + "</script>")
    List<Long> getFavoriteAssetIdsByAssetIds(@Param("userId") long userId, @Param("assetIds") List<Long> assetIds);
}
