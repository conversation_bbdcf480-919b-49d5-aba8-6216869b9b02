package co.sandai.zeus.domain.voice.service;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.domain.voice.service.provider.VoiceProvider;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * Registry managing all available voice providers
 */
@Service
public class VoiceProviderRegistry {
    /**
     * Constant for Minimax provider name
     */
    public static final String PROVIDER_MINIMAX = "minimax";

    /**
     * Constant for ElevenLabs provider name
     */
    public static final String PROVIDER_ELEVENLABS = "elevenlabs";

    public static final String DEFAULT_PROVIDER = PROVIDER_ELEVENLABS;
    private final Map<String, VoiceProvider> providers = new HashMap<>();
    private final VoiceProviderSelector selector;

    public VoiceProviderRegistry(
            List<VoiceProvider> availableProviders,
            @Qualifier("languageDetectionSelector") VoiceProviderSelector selector) {
        // 确保提供者列表被正确初始化
        if (availableProviders != null && !availableProviders.isEmpty()) {
            for (VoiceProvider provider : availableProviders) {
                if (provider != null && provider.getProviderName() != null) {
                    providers.put(provider.getProviderName().toLowerCase(), provider);
                }
            }
        } else {
            throw new IllegalStateException(
                    "No voice providers available. Both 'minimax' and 'elevenlabs' providers are required.");
        }

        // 确保minimax和elevenlabs提供者存在
        if (!providers.containsKey(PROVIDER_MINIMAX) || !providers.containsKey(PROVIDER_ELEVENLABS)) {
            throw new IllegalStateException("Required providers missing. Both '" + PROVIDER_MINIMAX + "' and '"
                    + PROVIDER_ELEVENLABS + "' providers must be available.");
        }

        this.selector = selector;
    }

    /**
     * Get a voice provider by name
     * @param providerName Name of the provider
     * @return Voice provider implementation
     */
    public VoiceProvider getProvider(String providerName) {
        VoiceProvider provider = providers.get(providerName.toLowerCase());
        if (provider == null) {
            throw new ZeusServiceException(ErrorCode.NotFound, "Voice provider not found: " + providerName);
        }
        return provider;
    }

    /**
     * Get all available provider names
     * @return List of provider names
     */
    public List<String> getAvailableProviderNames() {
        return new ArrayList<>(providers.keySet());
    }

    /**
     * Get all available providers
     * @return List of providers
     */
    public List<VoiceProvider> getAllProviders() {
        return new ArrayList<>(providers.values());
    }

    /**
     * Get the default provider
     * @return Default voice provider
     */
    public VoiceProvider getDefaultProvider() {
        if (providers.isEmpty()) {
            throw new ZeusServiceException(ErrorCode.InternalError, "No voice providers available");
        }
        return providers.get(PROVIDER_ELEVENLABS);
    }

    /**
     * Select appropriate provider based on text content
     * @param text Text to analyze
     * @return Selected provider
     */
    public VoiceProvider selectProviderForText(String text) {
        String providerName = selector.selectProviderForText(text);
        return getProvider(providerName);
    }
}
