package co.sandai.zeus.domain.credit.config;

import java.util.List;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

@Component
public class CreditConfigService {

    @Setter(value = AccessLevel.PACKAGE)
    @Getter
    private List<CreditPackage> creditPackageList;

    public CreditPackage getById(String outCode) {
        return creditPackageList.stream()
                .filter(x -> x.getId().equals(outCode))
                .findFirst()
                .orElse(null);
    }
}
