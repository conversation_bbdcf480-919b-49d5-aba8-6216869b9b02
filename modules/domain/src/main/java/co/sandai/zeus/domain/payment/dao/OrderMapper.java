package co.sandai.zeus.domain.payment.dao;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface OrderMapper {
    void createOrder(OrderDO orderDO);

    List<OrderWithUserDO> getOrgOrderList(long orgId, int offset, int limit);

    @Select("select * from `order` where session_id=#{sessionId}")
    OrderDO selectBySessionId(String sessionId);

    @Select("select * from `order` where outer_order_id=#{outerOrderId} and outer_platform=#{outerPlatform}")
    OrderDO queryByOuterOrderIdAndOuterPlatform(String outerOrderId, String outerPlatform);

    @Select("SELECT COUNT(*) FROM `order` WHERE org_id=#{orgId}")
    int countOrgOrder(long orgId);

    @Select(
            "SELECT * FROM `order` WHERE user_id=#{userId} AND `from` IN ('SUBSCRIBE', 'ADMIN_GRANT', 'RENEW') ORDER BY create_time DESC LIMIT 1")
    OrderDO getLatestPlanOrderByUserId(long userId);
}
