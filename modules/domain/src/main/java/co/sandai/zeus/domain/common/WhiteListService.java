package co.sandai.zeus.domain.common;

import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class WhiteListService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    static final Map<String, Set<String>> WHITE_LIST_MAP = new ConcurrentHashMap<>();
    static final Map<String, Date> UPDATE_TIME_MAP = new ConcurrentHashMap<>();
    /**
     * 过期时间 ，单位ms
     * 这里暂定3分钟过期
     */
    static final long EXPIRE_TIME = 3 * 60 * 1000;

    private static final String SUPER_USER_KEY = "SUPER_USER_LIST";

    @Autowired
    private UserService userService;

    public Set<String> getSuperUserEmails() {
        return getAndRefresh(SUPER_USER_KEY);
    }

    public boolean isSuperUser(String email) {
        return email.endsWith("@temp.sandai.co")
                || email.endsWith("@sand.ai")
                || getSuperUserEmails().contains(email);
    }

    public boolean isSuperUser(long userId) {
        User user = userService.getUserById(userId);
        if (user == null) {
            return false;
        }
        return isSuperUser(user.getEmail());
    }

    @NonNull
    public Set<String> getAndRefresh(String key) {
        Set<String> cacheIds = WHITE_LIST_MAP.get(key);
        Date date = UPDATE_TIME_MAP.get(key);
        // 之前没有缓存 或者 缓存已过期
        if (cacheIds == null || TimeUtil.isExpire(date, EXPIRE_TIME)) {
            // 没有缓存，从redis里重新拉取
            Set<String> result = refreshWhiteList(key);

            // 更新过期时间
            UPDATE_TIME_MAP.put(key, new Date());

            return result;
        }

        // 未过期，返回当前值
        return cacheIds;
    }

    @NonNull
    private Set<String> refreshWhiteList(String key) {
        Set<String> result = redisTemplate.opsForSet().members(key);
        WHITE_LIST_MAP.put(key, result);
        return Objects.requireNonNullElse(result, Collections.emptySet());
    }
}
