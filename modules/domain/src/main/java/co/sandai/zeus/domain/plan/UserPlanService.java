package co.sandai.zeus.domain.plan;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.config.CreditConfigService;
import co.sandai.zeus.domain.credit.config.CreditPackage;
import co.sandai.zeus.domain.payment.PaySuccessEvent;
import co.sandai.zeus.domain.payment.dao.OrderDO;
import co.sandai.zeus.domain.payment.dao.OrderMapper;
import co.sandai.zeus.domain.plan.bo.UserPlanDetailBO;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.dao.UserPlanDao;
import co.sandai.zeus.domain.plan.dao.UserPlanMapper;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.PriceModel;
import co.sandai.zeus.domain.plan.model.enums.IntervalTypeEnum;
import co.sandai.zeus.domain.plan.model.enums.PlanStatusEnum;
import co.sandai.zeus.domain.platform.credit.PlatformCreditService;
import co.sandai.zeus.domain.platform.credit.dto.PlatformCreditPackage;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.stripe.StripeApiClient;
import com.stripe.exception.StripeException;
import com.stripe.model.Subscription;
import jakarta.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UserPlanService {

    @Resource
    private IDGenerator idGenerator;

    @Resource
    private PlanConfigService planConfigService;

    @Resource
    private UserPlanDao userPlanDao;

    @Resource
    private UserCreditService userCreditService;

    @Autowired
    private UserPlanMapper userPlanMapper;

    @Autowired
    private CreditConfigService creditConfigService;

    @Autowired
    private PlatformCreditService platformCreditService;

    @Autowired
    private UserService userService;

    @Autowired
    private StripeApiClient stripeApiClient;

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 处理支付成功事件
     * 注意可重入设计
     */
    @EventListener
    @Retryable(retryFor = Throwable.class, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void dealUserPlanAndIssueCredits(PaySuccessEvent paySuccessEvent) {
        OrderDO.FromEnum from = paySuccessEvent.getFrom();
        String priceId = paySuccessEvent.getPriceId();
        PriceModel priceModel = planConfigService.getPriceModelByPriceId(priceId);
        CreditPackage creditPackage;

        switch (from) {
            case ADMIN_GRANT:
                // 管理员免费分配会员计划
                // fallthrough 到 SUBSCRIBE，使用相同的处理逻辑
            case SUBSCRIBE:
                // 发送场景
                // checkout回跳页面重定向；
                // invoice.paid回调
                Plan plan = planConfigService.getPlanByPriceId(priceId, PlanStatusEnum.ACTIVE);
                UserPlanDO userPlanDO = new UserPlanDO();
                userPlanDO.setId(idGenerator.getNextId());
                userPlanDO.setPlanCode(priceModel.getOutCode());
                userPlanDO.setUserId(paySuccessEvent.getUserId());
                userPlanDO.setFrom(from);
                if (plan.getIntervalTypeEnum() == IntervalTypeEnum.MONTHLY) {
                    userPlanDO.setExpireTime(new Timestamp(
                            TimeUtil.toEpochMilli(paySuccessEvent.getPayTime().plusMonths(1))));
                } else if (plan.getIntervalTypeEnum() == IntervalTypeEnum.YEARLY) {
                    userPlanDO.setExpireTime(new Timestamp(
                            TimeUtil.toEpochMilli(paySuccessEvent.getPayTime().plusYears(1))));
                }
                userPlanDO.setNextRenewTime(new Timestamp(
                        TimeUtil.toEpochMilli(paySuccessEvent.getPayTime().plusMonths(1))));
                // 更新plan
                userPlanDao.generatePlan(userPlanDO);
                // 发放credit
                userCreditService.issueSubscriptionCredit(
                        paySuccessEvent.getUserId(), plan, paySuccessEvent.getPayTime());
                break;
            case RENEW:
                // invoice.paid事件消息接收
                plan = planConfigService.getPlanByPriceId(priceId, PlanStatusEnum.ACTIVE);
                UserPlanDO userPlanDOInDb = userPlanMapper.queryByUserId(paySuccessEvent.getUserId());
                UserPlanDO updateUserPlan = new UserPlanDO();
                updateUserPlan.setPlanCode(priceModel.getOutCode());
                if (plan.getIntervalTypeEnum() == IntervalTypeEnum.MONTHLY) {
                    updateUserPlan.setExpireTime(TimeUtil.addMonths(userPlanDOInDb.getExpireTime(), 1));
                } else {
                    updateUserPlan.setExpireTime(TimeUtil.addYears(userPlanDOInDb.getExpireTime(), 1));
                }
                updateUserPlan.setNextRenewTime(TimeUtil.addMonths(userPlanDOInDb.getNextRenewTime(), 1));
                updateUserPlan.setUserId(paySuccessEvent.getUserId());
                userPlanMapper.update(updateUserPlan);

                userCreditService.renewSubscriptionCredit(
                        paySuccessEvent.getUserId(), plan, paySuccessEvent.getPayTime());
                break;
            case PURCHASE_CREDIT:
                // checkout回跳页面重定向；
                // invoice.paid回调；
                creditPackage = creditConfigService.getById(priceModel.getOutCode());
                // 直接发放credit
                userCreditService.issuePurchasedUserCredit(
                        paySuccessEvent.getUserId(),
                        paySuccessEvent.getRequestId(),
                        creditPackage,
                        paySuccessEvent.getPayTime());
                break;
            case PURCHASE_PLATFORM_CREDIT:
                PlatformCreditPackage platformCreditPackage =
                        platformCreditService.getPlatformCreditPackageByPriceId(priceId);
                userCreditService.issuePurchasedOrgCredit(
                        paySuccessEvent.getOrgId(),
                        paySuccessEvent.getRequestId(),
                        platformCreditPackage,
                        paySuccessEvent.getPayTime());
                break;
            case UPGRADE:
                // 不在这里处理，不实现了
                break;
            default:
                break;
        }
    }

    /**
     * 更新订阅信息
     */
    public void changeSubscription(long userId, Plan currentPlan, LocalDateTime payTime) {
        UserPlanDO userPlanDo = new UserPlanDO();
        userPlanDo.setUserId(userId);
        userPlanDo.setPlanCode(currentPlan.getPlanCode());
        userPlanDo.setExpireTime(Timestamp.valueOf(
                currentPlan.getIntervalTypeEnum() == IntervalTypeEnum.MONTHLY
                        ? TimeUtil.addMonths(payTime, 1)
                        : TimeUtil.addYears(payTime, 1)));
        userPlanDo.setNextRenewTime(Timestamp.valueOf(TimeUtil.addMonths(payTime, 1)));

        int updateRows = userPlanMapper.update(userPlanDo);
        if (updateRows < 1) {
            log.warn(
                    "update user plan not success, affected rows less than 1, userId: {}, planCode: {}",
                    userId,
                    currentPlan.getPlanCode());
        }
    }

    public UserPlanDO queryUserPlan(long userId) {
        return userPlanMapper.queryByUserId(userId);
    }

    public UserPlanDO queryNotExpiredUserPlan(long userPlanId) {
        UserPlanDO userPlanDO = userPlanMapper.queryByUserId(userPlanId);
        if (userPlanDO != null && !TimeUtil.isExpire(userPlanDO.getExpireTime())) {
            return userPlanDO;
        }
        return null;
    }

    public UserPlanDetailBO queryUserPlanDetail(long userId) {
        UserPlanDO userPlanDO = queryNotExpiredUserPlan(userId);

        if (userPlanDO == null) {
            return null;
        }

        String planCode = userPlanDO.getPlanCode();
        PriceModel priceModel = planConfigService.getPriceModelOfPlan(planCode, "USD");
        if (priceModel == null) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "plan not found for user");
        }

        if (userPlanDO.getFrom() == OrderDO.FromEnum.ADMIN_GRANT) {
            return UserPlanDetailBO.convert(userPlanDO, "active", false);
        }

        // 付费计划，查询Stripe订阅状态
        User user = userService.getUserById(userId);
        Subscription subscription = null;
        try {
            subscription = stripeApiClient.querySubscription(user.getOutCustomerId(), priceModel.getOutPriceId());
        } catch (StripeException e) {
            log.error("query subscription from stripe failed", e);
            throw new ZeusServiceException(ErrorCode.ThirdPartyError, "subscription query failed");
        }
        if (subscription == null) {
            return UserPlanDetailBO.convert(userPlanDO, null, false);
        }
        return UserPlanDetailBO.convert(userPlanDO, subscription.getStatus(), subscription.getCancelAtPeriodEnd());
    }

    /**
     * 获取需要renew账号的用户
     * 排除掉最后一个月到期的情况。这种需要等待续费才会renew
     */
    public List<UserPlanDO> queryYearlyRenewPlan(Long lowerId, int batchSize) {
        LocalDateTime now = TimeUtil.utcTime();
        LocalDateTime lowerBoundTime = now.minusDays(1);
        // 排除掉最后一个月到期的情况。这种需要等待续费才会renew。所以需要下次续费时间至少在n天以后
        LocalDateTime nextPayTimeLowerBound = now.plusDays(15);
        List<String> planCodes = planConfigService.getActivePlanList().stream()
                .filter(x -> x.getIntervalTypeEnum() == IntervalTypeEnum.YEARLY)
                .filter(x -> x.getStatus() == PlanStatusEnum.ACTIVE || x.getStatus() == PlanStatusEnum.DEPRECATED)
                .map(Plan::getPlanCode)
                .toList();
        return userPlanMapper.queryYearlyRenewPlan(
                lowerId, batchSize, planCodes, lowerBoundTime, now, nextPayTimeLowerBound);
    }

    public void batchUpdateNextRenewTime(List<UserPlanDO> userPlans) {
        userPlanMapper.batchUpdateNextRenewTime(userPlans);
    }

    public void cancelSubscriptionWithoutRefund(User user, String priceId) {
        String outCustomerId = user.getOutCustomerId();
        if (outCustomerId == null) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "you haven't subscribed");
        }

        try {
            stripeApiClient.cancelSubscriptionAtPeriodEnd(outCustomerId, priceId);
        } catch (StripeException e) {
            log.error("cancel subscription failed", e);
            throw new ZeusServiceException(ErrorCode.ThirdPartyError, "cancel subscription failed");
        }
    }
}
