package co.sandai.zeus.domain.common;

import co.sandai.zeus.common.exception.ZeusServiceException;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CropArea {
    public int x;
    public int y;
    public int width;
    public int height;

    public static Map<String, Integer> convertToMap(CropArea cropArea) {
        Map<String, Integer> cropAreaMap = new HashMap<>();
        cropAreaMap.put("x", cropArea.getX());
        cropAreaMap.put("y", cropArea.getY());
        cropAreaMap.put("width", cropArea.getWidth());
        cropAreaMap.put("height", cropArea.getHeight());
        return cropAreaMap;
    }

    public void checkParams(int totalWidth, int totalHeight) {
        if (x < 0 || y < 0 || width <= 0 || height <= 0) {
            throw ZeusServiceException.badRequest("Invalid crop area parameters");
        }
        if (totalHeight > 0 && totalWidth > 0 && (x + width > totalWidth || y + height > totalHeight)) {
            throw ZeusServiceException.badRequest("Invalid crop area parameters, out of bound");
        }
    }

    /**
     * Check if the crop area needCrop
     *
     * @param totalWidth  The total width of the image
     * @param totalHeight The total height of the image
     * @return true if the crop area is valid, false otherwise
     */
    public boolean needCrop(int totalWidth, int totalHeight) {
        return x > 0 || y > 0 || width < totalWidth || height < totalHeight;
    }
}
