package co.sandai.zeus.domain.plan.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MemberShipStatusEnum {
    ACTIVE("ACTIVE"),
    DEPRECATED("DEPRECATED"),
    OFFLINE("OFFLINE"),
    ;

    private String code;

    private MemberShipStatusEnum findbyCode(String code) {
        if (code == null) {
            return null;
        }

        for (MemberShipStatusEnum memberShipStatusEnum : MemberShipStatusEnum.values()) {
            if (memberShipStatusEnum.getCode().equals(code)) {
                return memberShipStatusEnum;
            }
        }
        return null;
    }
}
