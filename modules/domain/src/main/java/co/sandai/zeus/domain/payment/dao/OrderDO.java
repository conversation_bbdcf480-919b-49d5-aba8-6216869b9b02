package co.sandai.zeus.domain.payment.dao;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class OrderDO {

    private long id;
    private String outerOrderId;
    private String outerPlatform;
    private String sessionId;
    private String from;
    private Long amount;
    private String currency;
    private Long userId;
    private Long orgId;
    private String status;
    private Long deleted;
    private LocalDateTime createTime;
    private LocalDateTime modifiedTime;

    public enum OutPlatformEnum {
        STRIPE,
        ADMIN, // 管理员操作
        ;
    }

    public enum OrderStatusEnum {
        PAID,
        FAILED,
        ;
    }

    public enum FromEnum {
        /**
         * 订阅购买
         */
        SUBSCRIBE,

        /**
         * 续费更新
         */
        RENEW,

        /**
         * 升级
         */
        UPGRADE,

        /**
         * credit购买
         */
        PURCHASE_CREDIT,

        // 购买 API platform credits
        PURCHASE_PLATFORM_CREDIT,

        /**
         * 管理员免费分配会员计划
         */
        ADMIN_GRANT;
    }
}
