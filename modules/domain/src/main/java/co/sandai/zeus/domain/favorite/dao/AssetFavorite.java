package co.sandai.zeus.domain.favorite.dao;

import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssetFavorite {
    private long id;
    private long userId;
    private long assetId;
    private String mediaType;
    private Timestamp createTime;
    private Timestamp updateTime;
}
