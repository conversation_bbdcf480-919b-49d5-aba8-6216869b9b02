package co.sandai.zeus.domain.user.service;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.dao.UserMapper;
import co.sandai.zeus.infra.DirectMessage;
import co.sandai.zeus.infra.Green;
import co.sandai.zeus.infra.IDGenerator;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserService {
    private final Green green;

    @Value("${zeus.base}")
    private String base;

    private final IDGenerator idGenerator;
    private final UserMapper userMapper;
    private final DirectMessage directMessage;
    private final RedisTemplate<String, Object> redisTemplate;

    @Resource
    private UserCreditService userCreditService;

    public UserService(
            UserMapper userMapper,
            IDGenerator idGenerator,
            DirectMessage directMessage,
            RedisTemplate<String, Object> redisTemplate,
            Green green) {
        this.userMapper = userMapper;
        this.idGenerator = idGenerator;
        this.directMessage = directMessage;
        this.redisTemplate = redisTemplate;
        this.green = green;
    }

    public User getUserByEmail(String email) {
        return userMapper.getUserByEmail(email);
    }

    public void updateUserAvatarId(long userId, long avatarId) {
        userMapper.updateUserAvatarId(userId, avatarId);
    }

    public User addUser(User user) {
        long id = idGenerator.getNextId();
        user.setId(id);
        this.userMapper.insertUser(user);

        // 发放福利
        try {
            userCreditService.issueFreeCredit(id);
        } catch (ZeusServiceException e) {
            log.error("Failed to issue free credit for user: {},", user.getEmail(), e);
        }

        log.info("new user User joined, email: {}, source: {}", user.getEmail(), user.getSource());

        return user;
    }

    public void updateUser(User user) {
        Green.ModerateTextResult result =
                green.moderateText(user.getNickname() + "; " + user.getDescription(), user.getId());
        if (!result.isValid()) {
            throw new ZeusServiceException(
                    ErrorCode.InvalidParameters,
                    "The display name or description contains invalid characters. Please modify it and try again.");
        }
        userMapper.updateUserProfileById(user.getId(), user.getNickname(), user.getDescription(), user.getAvatarId());
    }

    public void updateUserPassword(long userId, String password) {
        userMapper.updateUserPasswordById(userId, password);
    }

    public String getEmailVerifyKey(String code) {
        return String.format("email_verify_key:%s", code);
    }

    public String getEmailVerifyContent(User user, String verifyCode) {
        return String.format(
                """
                        <div style="
                            box-sizing: border-box;
                            width: 460px;
                            padding: 24px;
                            border-radius: 24px;
                            background: #eee;
                            font-family: Arial, Helvetica, sans-serif;
                            ">
                                <img src="https://static.sandaii.cn/build/website/9/_next/static/media/logo.fdda4d71.svg" alt="" width="80" />
                                <div style="
                                margin-top: 24px;
                                padding: 40px;
                                border-radius: 16px;
                                border: 1px solid rgba(32, 32, 32, 0.04);
                                background: #eee;
                                box-shadow: -3px -3px 6px 0px rgba(255, 255, 255, 0.6) inset,
                                3px 3px 6px 0px rgba(32, 32, 32, 0.08) inset;
                            ">
                                  <p style="
                                margin: 0px;
                                padding: 0px;
                                margin-bottom: 8px;
                                color: rgba(32, 32, 32, 0.8);
                                font-size: 16px;
                                font-weight: 700;
                                line-height: 140%%;
                                text-align: center;
                                ">
                                    Hi, %s
                                  </p>
                                  <p style="
                                margin: 0px;
                                padding: 0px;
                                color: rgba(32, 32, 32, 0.6);
                                font-size: 12px;
                                font-weight: 400;
                                line-height: 140%%;
                                ">
                                    Please click this link to verify your email for signup to Magi:
                                  </p>
                                  <p style="text-align: center;">
                                    <a href="%s" target="_blank" style="
                                margin: 24px auto 0;
                                color: #e4b274;
                                text-align: center;
                                font-size: 12px;
                                font-weight: 700;
                                line-height: 140%%;
                                text-decoration-line: underline;
                                text-decoration-style: solid;
                                text-decoration-skip-ink: auto;
                                text-decoration-thickness: auto;
                                text-underline-offset: auto;
                                text-underline-position: from-font;
                                ">Verify Email</a>
                                  </p>
                                </div>
                                <p style="
                                margin-top: 24px;
                                color: rgba(32, 32, 32, 0.4);
                                text-align: center;
                                font-size: 12px;
                                font-weight: 400;
                                line-height: normal;
                            ">
                                  Sent by Discord • Check out our blog • @discord
                                </p>
                            </div>
                            """,
                user.getNickname(), base + "/app/register/verify?verificationCode=" + verifyCode);
    }

    public void sendEmailVerificationEmail(User user) {
        String verifyCode = UUID.randomUUID().toString();
        redisTemplate.opsForValue().set(getEmailVerifyKey(verifyCode), user.getEmail(), Duration.ofMinutes(5));
        String emailBody = getEmailVerifyContent(user, verifyCode);
        directMessage.sendEmail(user.getEmail(), "Verify your email for Signup to Magi", emailBody);
        log.info("send verification email to: {}", user.getEmail());
    }

    public String getResetPasswordContent(User user, String verifyCode) {
        return String.format(
                """
                        <div style="
                            box-sizing: border-box;
                            width: 460px;
                            padding: 24px;
                            border-radius: 24px;
                            background: #eee;
                            font-family: Arial, Helvetica, sans-serif;
                            ">
                              <img src="https://static.sandaii.cn/build/website/9/_next/static/media/logo.fdda4d71.svg" alt="" width="80" />
                              <div style="
                                margin-top: 24px;
                                padding: 40px;
                                border-radius: 16px;
                                border: 1px solid rgba(32, 32, 32, 0.04);
                                background: #eee;
                                box-shadow: -3px -3px 6px 0px rgba(255, 255, 255, 0.6) inset,
                                3px 3px 6px 0px rgba(32, 32, 32, 0.08) inset;
                            ">
                                <p style="
                                margin: 0px;
                                padding: 0px;
                                margin-bottom: 8px;
                                color: rgba(32, 32, 32, 0.8);
                                font-size: 16px;
                                font-weight: 700;
                                line-height: 140%%;
                                ">
                                  Hi, %s
                                </p>
                                <p style="
                                margin: 0px;
                                padding: 0px;
                                color: rgba(32, 32, 32, 0.6);
                                font-size: 12px;
                                font-weight: 400;
                                line-height: 140%%;
                                ">
                                  You can reset your Magi password by clicking the button below. If you
                                  did not request a new password, please ignore this email.
                                </p>
                                <a href="%s" target="_blank">
                                  <button style="
                                    width: 100%%;
                                    box-sizing: border-box;
                                    margin-top: 40px;
                                    position: relative;
                                    width: 100%%;
                                    height: 48px;
                                    text-align: center;
                                    font-size: 12px;
                                    font-weight: 700;
                                    background: #e4b274cc;
                                    border: 2px solid transparent;
                                    box-shadow: 0 2px 0 rgba(255, 188, 5, 0.06);
                                    border-radius: 100px;
                                    ">
                                    <span style="
                                        color: #fff;
                                        font-size: 16px;
                                        font-weight: 700;
                                        line-height: 140%%;
                                    ">Reset Password</span>
                                  </button>
                                </a>
                                <p style="
                                margin: 0px;
                                padding: 0px;
                                margin-top: 40px;
                                color: rgba(32, 32, 32, 0.6);
                                font-size: 12px;
                                font-weight: 400;
                                line-height: 140%%;
                                ">
                                  Need help? Please contact our support team or find us on Twitter
                                  @discord. Have some feedback? Let us know your thoughts through our
                                  feedback page.
                                </p>
                              </div>
                              <p style="
                                color: rgba(32, 32, 32, 0.4);
                                margin-top: 24px;
                                text-align: center;
                                font-size: 12px;
                                font-weight: 400;
                                line-height: normal;
                            ">
                                Sent by Discord • Check out our blog • @discord
                              </p>
                            </div>
                            """,
                user.getNickname(), base + "/app/login/change-password?code=" + verifyCode);
    }

    public void sendResetPasswordEmail(User user) {
        String verifyCode = UUID.randomUUID().toString();
        redisTemplate.opsForValue().set(verifyCode, user.getEmail(), Duration.ofMinutes(5));
        String emailBody = getResetPasswordContent(user, verifyCode);
        directMessage.sendEmail(user.getEmail(), "Reset Account Password of Magi", emailBody);
        log.info("send reset password email to: {}", user.getEmail());
    }

    public String getResetPasswordEmailByCode(String code) {
        String email = (String) redisTemplate.opsForValue().get(code);
        if (Objects.isNull(email)) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "invalid code");
        }
        return email;
    }

    public User verifyEmail(String verifyCode) {
        String emailVerifyKey = getEmailVerifyKey(verifyCode);
        String email = (String) redisTemplate.opsForValue().get(emailVerifyKey);
        if (Objects.isNull(email)) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "invalid verification code");
        }

        User user = getUserByEmail(email);
        if (Objects.isNull(user)) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "user not found");
        }

        userMapper.updateEmailVerifyResult(user.getId(), true);
        return user;
    }

    private Boolean isInWhitelist(User user, String whiteListKey) {
        Set<String> keys = redisTemplate.keys(whiteListKey);
        if (keys.isEmpty()) {
            return true;
        }
        return redisTemplate.opsForSet().isMember(whiteListKey, user.getEmail());
    }

    private void addInWhitelist(User user, String whiteListKey) {
        redisTemplate.opsForSet().add(whiteListKey, user.getEmail());
    }

    public User getUserByOutCustomerId(String outCustomerId) {
        return userMapper.getUserByOutCustomerId(outCustomerId);
    }

    public User getUserById(long userId) {
        return userMapper.getUserById(userId);
    }

    public List<User> getUsers(Long lowerId, int pageSize) {
        return userMapper.getUsers(lowerId, pageSize);
    }

    public Boolean isInAdminWhiteList(User user) {
        String key = "AdminWhiteList";
        return isInWhitelist(user, key);
    }

    public Boolean isInLoginWhiteList(User user) {
        String key = "UserLoginWhiteList";
        return isInWhitelist(user, key);
    }

    public void addInLoginWhiteList(User user) {
        String key = "UserLoginWhiteList";
        addInWhitelist(user, key);
    }
}
