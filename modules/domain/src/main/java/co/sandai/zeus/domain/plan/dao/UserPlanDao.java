package co.sandai.zeus.domain.plan.dao;

import co.sandai.zeus.common.utils.TimeUtil;
import jakarta.annotation.Resource;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

@Component
public class UserPlanDao {

    @Resource
    private UserPlanMapper userPlanMapper;

    public void generatePlan(UserPlanDO userPlanDO) {
        try {
            userPlanMapper.insert(userPlanDO);
        } catch (DuplicateKeyException e) {
            UserPlanDO userPlanDOInDb = userPlanMapper.queryByUserId(userPlanDO.getUserId());
            if (!compare(userPlanDOInDb, userPlanDO)) {
                // 对比失败，说明不是重入的情况，需要直接更新
                userPlanMapper.update(userPlanDO);
            }
        }
    }

    private boolean compare(UserPlanDO userPlanDOInDb, UserPlanDO userPlanDO) {
        if (userPlanDOInDb == null) {
            return false;
        }
        if (!userPlanDOInDb.getPlanCode().equals(userPlanDO.getPlanCode())) {
            return false;
        }
        if (!userPlanDOInDb.getUserId().equals(userPlanDO.getUserId())) {
            return false;
        }
        // 时间戳允许1分钟内的误差
        if (!TimeUtil.isSameMinute(userPlanDOInDb.getExpireTime(), userPlanDO.getExpireTime())) {
            return false;
        }
        if (!TimeUtil.isSameMinute(userPlanDOInDb.getNextRenewTime(), userPlanDO.getNextRenewTime())) {
            return false;
        }
        return true;
    }
}
