package co.sandai.zeus.domain.task.dao;

import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PipelineTask {
    private Long id;
    private Long pipelineId;
    private Long taskId;
    private Integer taskIndex;
    private Timestamp createTime;
    private Timestamp updateTime;
}
