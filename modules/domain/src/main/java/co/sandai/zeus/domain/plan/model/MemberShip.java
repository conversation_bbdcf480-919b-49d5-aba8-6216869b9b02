package co.sandai.zeus.domain.plan.model;

import co.sandai.zeus.domain.plan.model.enums.MemberShipStatusEnum;
import java.util.List;
import lombok.Data;

@Data
public class MemberShip {

    private String memberCode;
    private String name;
    private String description;

    private int subscriptionCreditNum;
    private String descriptionForCredit;
    private String toolTipForCredit;

    private List<Auth> authList;
    // 为方便加载添加
    private List<String> authCodes;

    private MemberShipStatusEnum memberShipStatus;

    private String outProductCode;
    private OutProductTypeEnum outProductType;

    public enum OutProductTypeEnum {
        STRIPE;
    }
}
