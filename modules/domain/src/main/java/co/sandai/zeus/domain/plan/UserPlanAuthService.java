package co.sandai.zeus.domain.plan;

import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.model.Auth;
import co.sandai.zeus.domain.plan.model.MemberShip;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.enums.PlanStatusEnum;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserPlanAuthService {

    @Resource
    private UserPlanService userPlanService;

    @Resource
    private PlanConfigService planConfigService;

    public List<String> queryAuthCodes(long userId) {
        UserPlanDO userPlanDO = userPlanService.queryUserPlan(userId);
        Plan plan;
        if (userPlanDO == null || TimeUtil.isExpire(userPlanDO.getExpireTime())) {
            // 默认为free计划
            plan = planConfigService.getFreePlan();
        } else {
            plan = planConfigService.getPlanByCode(userPlanDO.getPlanCode(), PlanStatusEnum.ACTIVE);
        }

        MemberShip ms = plan.getMemberShip();
        List<String> codes = ms.getAuthCodes();
        return codes;
    }

    public boolean hasAuth(long userId, String authCode) {
        Auth auth = planConfigService.queryAuth(authCode);
        // 查不到该权益，或该权益未开放
        if (auth == null || (auth.getWorking() != null && !auth.getWorking())) {
            return false;
        }
        return queryAuthCodes(userId).contains(authCode);
    }
}
