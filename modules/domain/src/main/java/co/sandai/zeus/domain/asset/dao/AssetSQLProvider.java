package co.sandai.zeus.domain.asset.dao;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class AssetSQLProvider {

    private boolean isOnlyFilteringFeaturedAssets(Map<String, Object> params) {
        Object paramSources = params.get("sources");
        if (Objects.nonNull(paramSources) && paramSources instanceof AssetSource[] sources) {
            // 检查是否只包含Featured类型的资源（Featured或HumanFeatured）
            for (AssetSource source : sources) {
                if (source != AssetSource.Featured && source != AssetSource.HumanFeatured) {
                    return false; // 包含其他类型
                }
            }
            return sources.length > 0;
        }
        return false;
    }

    private StringBuilder filterAssetSQL(Map<String, Object> params, String baseSQL) {
        StringBuilder sql = new StringBuilder(baseSQL);
        Object paramSources = params.get("sources");
        Object paramTypes = params.get("types");
        // TODO: 更好的方法？
        if (Objects.nonNull(paramSources) && paramSources instanceof AssetSource[] sources && sources.length > 0) {
            sql.append(" AND source in (")
                    .append(Arrays.stream(sources)
                            .map(i -> String.format("'%s'", i.toString()))
                            .collect(Collectors.joining(",")))
                    .append(")");
        }
        if (Objects.nonNull(paramTypes) && paramTypes instanceof AssetMediaType[] types && types.length > 0) {
            sql.append(" AND media_type in (")
                    .append(Arrays.stream(types)
                            .map(i -> String.format("'%s'", i.toString()))
                            .collect(Collectors.joining(",")))
                    .append(")");
        }
        return sql;
    }

    public String filterAssets(Map<String, Object> params) {
        String baseSQL = "SELECT * FROM asset WHERE 1=1";
        StringBuilder sb = filterAssetSQL(params, baseSQL);
        sb.append(" AND deleted = 0 AND is_moderation_passed=1");

        // 只查询 Featured Assets 的时候，所有用户可见
        boolean onlyFilteringFeaturedAssets = isOnlyFilteringFeaturedAssets(params);
        if (!onlyFilteringFeaturedAssets) {
            sb.append(" AND user_id=#{userId}");
        }

        sb.append(" ORDER BY create_time desc limit #{limit} offset #{offset}");
        return sb.toString();
    }

    public String countAssets(Map<String, Object> params) {
        String baseSQL = "SELECT count(*) FROM asset WHERE 1=1";
        StringBuilder sb = filterAssetSQL(params, baseSQL);
        sb.append(" AND deleted = 0 AND is_moderation_passed=1");

        boolean onlyFilteringFeaturedAssets = isOnlyFilteringFeaturedAssets(params);
        if (!onlyFilteringFeaturedAssets) {
            sb.append(" AND user_id=#{userId}");
        }

        return sb.toString();
    }
}
