package co.sandai.zeus.domain.report.service;

import co.sandai.zeus.domain.report.dao.ReportDO;
import co.sandai.zeus.domain.report.dao.ReportMapper;
import co.sandai.zeus.infra.IDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ReportService {

    @Autowired
    private ReportMapper reportMapper;

    @Autowired
    private IDGenerator idGenerator;

    /**
     * Create a new report
     * @param report the report to create
     * @return the created report with generated ID
     */
    public void createReport(ReportDO report) {
        // Generate a new ID for the report
        long reportId = idGenerator.getNextId();
        report.setId(reportId);

        // Insert the report into database
        reportMapper.insertOrUpdate(report);
        log.info("Created new report with ID: {}", reportId);
    }
}
