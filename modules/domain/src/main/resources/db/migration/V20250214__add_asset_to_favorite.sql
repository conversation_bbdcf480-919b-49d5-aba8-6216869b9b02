-- Add asset_id and type columns to favorite table
ALTER TABLE favorite ADD COLUMN asset_id BIGINT DEFAULT NULL;
ALTER TABLE favorite ADD COLUMN type VARCHAR(10) NOT NULL DEFAULT 'task';

-- Update existing records to have type='task' and ensure task_id is not null
UPDATE favorite SET type = 'task' WHERE task_id IS NOT NULL;

-- Add indexes for better query performance
CREATE INDEX idx_favorite_asset_id ON favorite(asset_id);

CREATE INDEX idx_favorite_user_task ON favorite(user_id, task_id, type);
CREATE INDEX idx_favorite_user_asset ON favorite(user_id, asset_id, type);
