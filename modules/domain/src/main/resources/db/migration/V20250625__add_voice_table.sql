-- Create voice table
CREATE TABLE IF NOT EXISTS voice (
    `id` bigint NOT NULL,
    voice_id VARCHAR(128) NOT NULL,
    name VARCHAR(128) NOT NULL,
    description TEXT,
    language VARCHAR(50),
    preview_url TEXT,
    is_cloned BOOLEAN DEFAULT FALSE,
    provider VARCHAR(50) NOT NULL,
    `user_id` bigint NOT NULL DEFAULT '0',
    `org_id` bigint NOT NULL DEFAULT '0',
    create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX idx_voice_user_id ON voice(user_id);
CREATE INDEX idx_voice_provider_voice_id ON voice(provider, voice_id);
