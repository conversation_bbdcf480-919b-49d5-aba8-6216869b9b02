<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="co.sandai.zeus.domain.credit.dao.UserCreditMapper">
    
    <select id="getUserCreditsIncludingDeleted">
        SELECT * FROM user_credit WHERE user_id = #{userId}
        <if test='creditType != null'>AND credit_type = #{creditType}</if>
        <if test='forUpdate'>FOR UPDATE</if>
    </select>

    <select id="getOrgCreditsIncludingDeleted">
        SELECT * FROM user_credit WHERE org_id = #{orgId}
        <if test='creditType != null'>AND credit_type = #{creditType}</if>
        <if test='forUpdate'>FOR UPDATE</if>
    </select>

    <select id="getNotExpiredOrgCredits">
        SELECT * FROM user_credit WHERE org_id = #{orgId} AND expire_time > NOW()
        <if test='creditType != null'> AND credit_type = #{creditType}</if>
        AND deleted = 0
    </select>
</mapper>
