<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="co.sandai.zeus.domain.asset.dao.AssetMapper">

    <select id="insertAsset">
        INSERT INTO asset(
            id,
            oss_path,
            oss_source,
            oss_bucket,
            media_type,
            source,
            width,
            height,
            duration,
            user_id,
            org_id,
            task_id,
            `index`,
            task_chunk_id,
            urn,
            filename,
            voice_id
        )
        VALUE (
            #{asset.id},
            #{asset.ossPath},
            #{asset.ossSource},
            #{asset.ossBucket},
            #{asset.mediaType},
            #{asset.source},
            #{asset.width},
            #{asset.height},
            #{asset.duration},
            #{asset.userId},
            #{asset.orgId},
            #{asset.taskId},
            #{asset.index},
            #{asset.taskChunkId},
            #{asset.urn},
            #{asset.filename},
            #{asset.voiceId}
        )
    </select>

</mapper>
