<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="co.sandai.zeus.domain.payment.dao.OrderMapper">

    <select id="getOrgOrderList" resultMap="OrderWithUserResultMap">
        SELECT
            *
        FROM `order`
        LEFT JOIN user
            ON user.id=`order`.user_id
        WHERE org_id = #{orgId} AND deleted = 0
        ORDER BY `order`.create_time DESC
        LIMIT #{limit}
        OFFSET #{offset}
    </select>

    <resultMap id="OrderWithUserResultMap" type="co.sandai.zeus.domain.payment.dao.OrderWithUserDO" autoMapping="true">
        <!-- 映射 User 对象 -->
        <association property="user" javaType="co.sandai.zeus.domain.user.dao.User" autoMapping="true">
            <id property="id" column="user_id"/>
        </association>
    </resultMap>

    <insert id="createOrder">
        INSERT INTO `order`(
            id, outer_order_id, outer_platform, session_id, `from`,
            amount, currency, user_id, status, org_id
        )
        VALUES(
            #{id}, #{outerOrderId}, #{outerPlatform}, #{sessionId}, #{from},
            #{amount}, #{currency}, #{userId}, #{status}, #{orgId}
        )
    </insert>

</mapper>
