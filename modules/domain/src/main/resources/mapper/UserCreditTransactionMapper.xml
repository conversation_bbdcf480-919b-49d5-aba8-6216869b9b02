<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="co.sandai.zeus.domain.credit.dao.UserCreditTransactionMapper">

    <select id="getOrgCreditUsage">
        SELECT SUM(user_credit_transaction.amount) AS amount, DATE(create_time) as date
        FROM user_credit_transaction
        WHERE org_id=#{orgId} AND direction='OUT' AND reason_sub_type='VIDEO_GENERATION'
        GROUP BY DATE(create_time)
        ORDER BY create_time desc
        LIMIT #{limit}
    </select>

</mapper>