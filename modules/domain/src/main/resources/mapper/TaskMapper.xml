<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="co.sandai.zeus.domain.task.dao.mapper.TaskMapper">

    <insert id="insertTask">
        INSERT INTO
        task(
            id, prompt, enhanced_prompt, enable_prompt_enhancement, enable_auto_gen_first_frame, source_video_id,
            source_video_start_timestamp, source_video_end_timestamp, seed, t_scheduler_func, t_scheduler_args, aspect_ratio,
            crop_area, extra_infer_args, source_video_task_id, source_image_id, source_audio_id, type, model, status, user_id, 
            duration, chunk_count, inspiration_id, task_source
        )
        VALUE (
            #{task.id}, #{task.prompt}, #{task.enhancedPrompt}, #{task.enablePromptEnhancement}, #{task.enableAutoGenFirstFrame},
            #{task.sourceVideoId}, #{task.sourceVideoStartTimestamp}, #{task.sourceVideoEndTimestamp}, 
            #{task.seed}, #{task.tSchedulerFunc}, #{task.tSchedulerArgs}, #{task.aspectRatio}, #{task.cropArea}, #{task.extraInferArgs},
            #{task.sourceVideoTaskId}, #{task.sourceImageId}, #{task.sourceAudioId}, #{task.type}, #{task.model}, #{task.status},
            #{task.userId}, #{task.duration}, #{task.chunkCount}, #{task.inspirationId}, #{task.taskSource}
        )
    </insert>

    <select id="getTasksByUserId" resultType="co.sandai.zeus.domain.task.dao.Task">
        SELECT * FROM task 
        WHERE user_id = #{userId} AND deleted=0 AND task_source IN
        <foreach item='item' index='index' collection='source_list' open='(' separator=',' close=')'>
            #{item}
        </foreach>
        ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="getTasksByUserIdAndFilters" resultType="co.sandai.zeus.domain.task.dao.Task">
        SELECT * FROM task
        WHERE user_id = #{userId} AND deleted=0
        <if test='source_list != null and source_list.size() > 0'>
            AND task_source IN
            <foreach item='item' index='index' collection='source_list' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
        <if test='type_list != null and type_list.size() > 0'>
            AND type IN
            <foreach item='item' index='index' collection='type_list' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
        ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="countTasksByUserIdAndFilters" resultType="int">
        SELECT COUNT(*) FROM task 
        WHERE user_id = #{userId} AND deleted=0
        <if test='source_list != null and source_list.size() > 0'>
            AND task_source IN
            <foreach item='item' index='index' collection='source_list' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
        <if test='type_list != null and type_list.size() > 0'>
            AND type IN
            <foreach item='item' index='index' collection='type_list' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTasksByStatus" resultType="co.sandai.zeus.domain.task.dao.Task">
        SELECT * FROM task 
        WHERE status = #{status} AND deleted=0 AND task_source IN
        <foreach item='item' index='index' collection='source_list' open='(' separator=',' close=')'>
            #{item}
        </foreach>
        ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="getTaskPrompts" resultType="java.lang.String">
        SELECT DISTINCT prompt FROM task
        ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="getTaskById" resultType="co.sandai.zeus.domain.task.dao.Task">
        SELECT * FROM task 
        WHERE id=#{userId} AND deleted=0
    </select>

    <select id="getTotalTaskCountByUserId" resultType="int">
        SELECT count(*) FROM task
        WHERE user_id=#{userId}
    </select>

    <select id="getTaskCountByUserId" resultType="int">
        SELECT count(*) FROM task 
        WHERE user_id=#{userId} AND deleted=0
    </select>

    <select id="getTaskCountByStatus" resultType="int">
        SELECT count(*) FROM task 
        WHERE status=#{status} AND deleted=0
    </select>

    <select id="getTaskCountOfUser" resultType="int">
        SELECT count(*) FROM task 
        WHERE id IN
        <foreach item='item' index='index' collection='taskIds' open='(' separator=',' close=')'>
            #{item}
        </foreach>
        AND task.user_id=#{userId}
    </select>

    <select id="getUnSafeCount" resultType="int">
        SELECT count(*) FROM task 
        WHERE id IN
        <foreach item='item' index='index' collection='taskIds' open='(' separator=',' close=')'>
            #{item}
        </foreach>
        AND task.is_moderation_passed=0
    </select>

    <select id="getNonMergedSuccessTask" resultType="co.sandai.zeus.domain.task.dao.Task">
        SELECT * FROM task 
        WHERE status='Success' AND result_video_id=0 AND deleted=0
    </select>

    <update id="updateTaskStatus">
        UPDATE task 
        SET status=#{status} 
        WHERE id=#{taskId}
    </update>

    <update id="updateTaskPrompt">
        UPDATE task 
        SET enhanced_prompt=#{enhancedPrompt}, first_frame_prompt=#{firstFramePrompt} 
        WHERE id=#{taskId}
    </update>

    <update id="updateEnhancedPrompt">
        UPDATE task 
        SET enhanced_prompt=#{enhancedPrompt} 
        WHERE id=#{taskId}
    </update>

    <update id="updateTaskSourceVideoId">
        UPDATE task 
        SET source_video_id=#{videoId} 
        WHERE id=#{taskId}
    </update>

    <update id="updateTaskResultVideoId">
        UPDATE task 
        SET result_video_id=#{assetId} 
        WHERE id=#{taskId}
    </update>

    <update id="updateTaskModerationResult">
        UPDATE task 
        SET is_moderation_passed=#{isPassed}, moderation_fail_type=#{failType}, moderation_confidence=#{confidence} 
        WHERE id=#{taskId}
    </update>

    <update id="updateTaskInferChunkResult">
        UPDATE task 
        SET completed_chunk_count=#{completedChunkCount} 
        WHERE id=#{taskId}
    </update>

    <update id="updateTaskPosterId">
        UPDATE task 
        SET task.result_video_poster_id=#{posterId} 
        WHERE id=#{taskId}
    </update>

    <update id="updateTaskResultInfo">
        UPDATE task 
        SET task.result_video_poster_id=#{posterId}, width=#{width}, height=#{height}, 
            underlying_model=#{modelVersion}, seed=#{seed} 
        WHERE id=#{taskId}
    </update>

    <update id="deleteTaskById">
        UPDATE task 
        SET task.deleted=1 
        WHERE id=#{taskId}
    </update>

</mapper>
