<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="co.sandai.zeus.domain.platform.feedback.dao.CustomApiPackageRequestMapper">

    <insert id="createCustomApiPackageRequest" parameterType="co.sandai.zeus.domain.platform.feedback.dao.CustomApiPackageRequest">
        INSERT INTO custom_api_package_request
        (id, company, business, phone, detail, user_id, org_id)
        VALUES
        (#{id}, #{company}, #{business}, #{phone}, #{detail}, #{userId}, #{orgId})
    </insert>

</mapper>