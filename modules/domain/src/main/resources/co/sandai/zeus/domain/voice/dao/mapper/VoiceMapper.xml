<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="co.sandai.zeus.domain.voice.dao.mapper.VoiceMapper">

    <resultMap id="voiceResultMap" type="co.sandai.zeus.domain.voice.dao.Voice">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="description" column="description" />
        <result property="voiceId" column="voice_id" />
        <result property="provider" column="provider" />
        <result property="orgId" column="org_id" />
        <result property="userId" column="user_id" />
        <result property="verifiedLanguages" column="verified_languages" />
        <result property="deleted" column="deleted" />
        <result property="previewUrl" column="preview_url" />
        <result property="language" column="language" />
        <result property="isCloned" column="is_cloned" />
        <result property="gender" column="gender" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <insert id="insertVoice" parameterType="co.sandai.zeus.domain.voice.dao.Voice">
        INSERT INTO voice (
            id,
            name,
            description,
            voice_id,
            provider,
            org_id,
            user_id,
            verified_languages,
            deleted,
            preview_url,
            language,
            is_cloned,
            gender,
            create_time,
            update_time
        )
        VALUES (
            #{voice.id},
            #{voice.name},
            #{voice.description},
            #{voice.voiceId},
            #{voice.provider},
            #{voice.orgId},
            #{voice.userId},
            #{voice.verifiedLanguages},
            #{voice.deleted},
            #{voice.previewUrl},
            #{voice.language},
            #{voice.isCloned},
            #{voice.gender},
            NOW(),
            NOW()
        )
    </insert>

    <select id="getVoicesByUserId" resultMap="voiceResultMap">
        SELECT * FROM voice
        WHERE user_id = #{userId} AND deleted = 0
        ORDER BY create_time DESC
    </select>
    
    <select id="getVoicesByProvider" resultMap="voiceResultMap">
        SELECT * FROM voice
        WHERE provider = #{provider} AND user_id = 0
        <if test="includeDeleted == false">
            AND deleted = 0
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="getVoiceById" resultMap="voiceResultMap">
        SELECT * FROM voice
        WHERE id = #{id} AND deleted = 0
    </select>

    <select id="getVoiceByVoiceId" resultMap="voiceResultMap">
        SELECT * FROM voice
        WHERE voice_id = #{voiceId} AND deleted = 0
    </select>
    
    <select id="getVoiceByVoiceIdIncludingDeleted" resultMap="voiceResultMap">
        SELECT * FROM voice
        WHERE voice_id = #{voiceId}
    </select>

    <select id="getVoiceCountByUserId" resultType="int">
        SELECT COUNT(*) FROM voice
        WHERE user_id = #{userId} AND deleted = 0
    </select>

    <update id="updateVoice">
        UPDATE voice
        SET name = #{name},
            description = #{description},
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>
    
    <update id="updateVoiceDeleted">
        UPDATE voice
        SET deleted = #{deleted},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <delete id="deleteVoiceById">
        UPDATE voice
        SET deleted = 1,
            update_time = NOW()
        WHERE id = #{id}
    </delete>
    
    <update id="updateVoicePreviewUrl">
        UPDATE voice
        SET preview_url = #{previewUrl},
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>
    
    <update id="updateVoiceVerifiedLanguages">
        UPDATE voice
        SET verified_languages = #{verifiedLanguages},
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>
    
    <update id="updateVoiceGender">
        UPDATE voice
        SET gender = #{gender},
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>
    
    <update id="updateVoiceAllFields">
        UPDATE voice
        SET name = #{voice.name},
            description = #{voice.description},
            provider = #{voice.provider},
            language = #{voice.language},
            verified_languages = #{voice.verifiedLanguages},
            preview_url = #{voice.previewUrl},
            is_cloned = #{voice.isCloned},
            gender = #{voice.gender},
            deleted = #{voice.deleted},
            update_time = NOW()
        WHERE id = #{voice.id}
    </update>

</mapper>
