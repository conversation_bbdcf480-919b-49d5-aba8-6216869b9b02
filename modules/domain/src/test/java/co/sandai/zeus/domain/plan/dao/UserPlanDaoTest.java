package co.sandai.zeus.domain.plan.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import co.sandai.zeus.MyBatisConfig;
import co.sandai.zeus.TestApplication;
import co.sandai.zeus.common.utils.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

@MapperScan("co.sandai.zeus.domain.plan.dao")
@Import(MyBatisConfig.class)
@SpringBootTest(classes = {TestApplication.class, UserPlanDao.class})
@RunWith(SpringRunner.class)
public class UserPlanDaoTest extends TestApplication {

    @Autowired
    private UserPlanDao userPlanDao;

    @Autowired
    private UserPlanMapper userPlanMapper;

    @Test
    public void generatePlan() {
        // Arrange
        UserPlanDO userPlanDO = new UserPlanDO();
        userPlanDO.setId(100001L);

        userPlanDO.setUserId(1L);
        userPlanDO.setPlanCode("planCode");
        userPlanDO.setExpireTime(TimeUtil.utcTimeTimeStamp());
        userPlanDO.setNextRenewTime(TimeUtil.utcTimeTimeStamp());

        // Act
        userPlanDao.generatePlan(userPlanDO);
        // 测试重入
        userPlanDao.generatePlan(userPlanDO);

        // Assert
        UserPlanDO retrievedPlan = userPlanMapper.queryByUserId(1L);
        assertNotNull(retrievedPlan);
        assertEquals("planCode", retrievedPlan.getPlanCode());
        assertEquals(userPlanDO.getExpireTime(), retrievedPlan.getExpireTime());
        assertEquals(userPlanDO.getNextRenewTime(), retrievedPlan.getNextRenewTime());
    }
}
