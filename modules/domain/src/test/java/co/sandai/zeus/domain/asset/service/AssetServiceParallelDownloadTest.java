package co.sandai.zeus.domain.asset.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetMapper;
import co.sandai.zeus.domain.asset.dao.AssetMediaType;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.task.dao.mapper.TaskMapper;
import co.sandai.zeus.infra.Green;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.infer.ArtifactClient;
import co.sandai.zeus.infra.oss.OSSProviderSource;
import co.sandai.zeus.infra.oss.OssClient;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;

/**
 * 测试AssetService的并行下载功能
 */
public class AssetServiceParallelDownloadTest {

    // 为AssetService创建必要的依赖
    @Mock
    private AssetMapper assetMapper;

    @Mock
    private IDGenerator idGenerator;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private TaskMapper taskMapper;

    @Mock
    private Green green;

    @Mock
    private OssClient ossClient;

    @Mock
    private ArtifactClient artifactClient;

    @Mock
    private StringRedisTemplate redisTemplate;

    // AssetService实例 - 使用spy而不是直接创建
    private AssetService assetService;

    private List<Asset> mockAssets;
    private File tempDir;

    @BeforeEach
    void setUp() throws IOException {
        // 初始化所有mock对象
        MockitoAnnotations.openMocks(this);

        // 使用mock依赖创建AssetService实例
        assetService = Mockito.spy(new AssetService(
                assetMapper, idGenerator, restTemplate, taskMapper, green, ossClient, artifactClient, redisTemplate));

        // 创建临时目录用于测试
        tempDir = Files.createTempDirectory("test_parallel_download").toFile();
        tempDir.deleteOnExit();

        // 准备测试数据 - 从您提供的TS URL列表创建Asset对象
        mockAssets = new ArrayList<>();
        List<String> tsUrls = getTsFileUrls();

        // 使用URL列表创建测试对象，最多使用3个
        int testCount = Math.min(3, tsUrls.size());
        System.out.println("准备测试数据，使用 " + testCount + " 个TS URL:");

        for (int i = 0; i < testCount; i++) {
            String tsUrl = tsUrls.get(i);
            System.out.println("  - " + tsUrl);

            OssClient.OSSFileMeta fileMeta = parseOssUrl(tsUrl);

            // 创建Asset模拟对象
            Asset asset = Mockito.mock(Asset.class);
            Mockito.when(asset.getId()).thenReturn((long) i);
            Mockito.when(asset.getOssPath()).thenReturn(fileMeta.getPath());
            Mockito.when(asset.getOssSource()).thenReturn(fileMeta.getSource().value);
            Mockito.when(asset.getOssBucket()).thenReturn(fileMeta.getBucket());
            Mockito.when(asset.getOssProviderSourceEnum()).thenReturn(fileMeta.getSource());
            Mockito.when(asset.getMediaType()).thenReturn(AssetMediaType.TransportStream);
            Mockito.when(asset.getSource()).thenReturn(AssetSource.Generate);

            mockAssets.add(asset);

            // 为每个Asset准备模拟数据 - 约10KB
            byte[] dummyData = createDummyTsContent(i, 10240);

            // 关键：直接模拟getAssetBytes方法，避免OssClient相关的调用
            Mockito.doReturn(dummyData).when(assetService).getAssetBytes(asset);
        }

        System.out.println("测试准备完毕，创建了 " + mockAssets.size() + " 个模拟Asset对象");
    }

    @AfterEach
    void tearDown() {
        System.out.println("清理测试资源...");
        // 删除测试期间创建的所有文件
        if (tempDir != null && tempDir.exists()) {
            File[] files = tempDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    file.delete();
                }
            }
            tempDir.delete();
        }
    }

    /**
     * 测试并行下载功能的正确性
     */
    @Test
    @DisplayName("测试TS文件并行下载功能")
    void testDownloadTsFilesInParallel() throws IOException {
        System.out.println("开始测试并行下载功能...");

        // 执行并行下载
        long startTime = System.currentTimeMillis();
        List<File> downloadedFiles = assetService.downloadTsFilesInParallel(mockAssets, tempDir);
        long endTime = System.currentTimeMillis();

        // 验证下载结果
        assertNotNull(downloadedFiles, "下载的文件列表不应为null");
        assertEquals(mockAssets.size(), downloadedFiles.size(), "下载文件数量应匹配Asset数量");

        // 验证每个文件都存在且有内容
        for (int i = 0; i < downloadedFiles.size(); i++) {
            File file = downloadedFiles.get(i);
            assertTrue(file.exists(), "文件应该存在: " + file.getAbsolutePath());
            assertTrue(file.length() > 0, "文件不应为空: " + file.getAbsolutePath());
            System.out.println("已验证文件 #" + i + ": " + file.getAbsolutePath() + " (" + file.length() + " 字节)");
        }

        // 验证getAssetBytes方法被调用了正确的次数
        for (Asset asset : mockAssets) {
            Mockito.verify(assetService).getAssetBytes(asset);
        }

        // 输出执行时间
        System.out.println("并行下载 " + downloadedFiles.size() + " 个文件完成，耗时: " + (endTime - startTime) + "ms");
    }

    /**
     * 测试并行下载的性能优势
     */
    @Test
    @DisplayName("测试并行下载性能")
    void testDownloadTsFilesPerformance() throws IOException, InterruptedException {
        System.out.println("开始测试并行下载性能...");

        // 为每个Asset添加不同的延迟，模拟网络延迟差异
        for (int i = 0; i < mockAssets.size(); i++) {
            final int index = i;
            final int delay = 100 * (i + 1); // 第一个文件100ms，第二个200ms，第三个300ms

            // 重设模拟行为，添加延迟
            Mockito.doAnswer(invocation -> {
                        System.out.println("模拟下载文件 #" + index + " 延迟: " + delay + "ms");
                        TimeUnit.MILLISECONDS.sleep(delay);
                        return createDummyTsContent(index, 10240);
                    })
                    .when(assetService)
                    .getAssetBytes(mockAssets.get(index));
        }

        // 计算串行下载的理论总时间（所有延迟之和）
        int totalSerialDelay = 0;
        for (int i = 0; i < mockAssets.size(); i++) {
            totalSerialDelay += 100 * (i + 1);
        }

        // 执行并行下载并测量时间
        long startTime = System.currentTimeMillis();
        List<File> downloadedFiles = assetService.downloadTsFilesInParallel(mockAssets, tempDir);
        long endTime = System.currentTimeMillis();
        long actualTime = endTime - startTime;

        // 验证并行下载比串行快
        System.out.println("并行下载耗时: " + actualTime + "ms");
        System.out.println("理论串行下载耗时: " + totalSerialDelay + "ms");

        // 考虑测试环境额外开销，设定一个宽松的条件：并行时间应该小于串行时间
        assertTrue(actualTime < totalSerialDelay, "并行下载时间(" + actualTime + "ms)应该小于串行时间(" + totalSerialDelay + "ms)");

        // 验证文件是否下载成功
        assertEquals(mockAssets.size(), downloadedFiles.size(), "应下载所有文件");
        for (File file : downloadedFiles) {
            assertTrue(file.exists() && file.length() > 0, "下载的文件应存在且不为空");
        }
    }

    /**
     * 解析OSS URL获取元数据
     */
    private OssClient.OSSFileMeta parseOssUrl(String ossUrl) {
        try {
            URL url = URI.create(ossUrl).toURL();
            String host = url.getHost();
            String path = url.getPath().substring(1); // 移除开头的斜杠

            OssClient.OSSFileMeta fileMeta = new OssClient.OSSFileMeta();
            fileMeta.setPath(path);

            // 从主机名提取bucket和source信息
            if (host.contains("oss-cn-shanghai.aliyuncs.com")) {
                String bucket = host.split("\\.")[0]; // 从主机名获取bucket
                fileMeta.setBucket(bucket);
                fileMeta.setSource(OSSProviderSource.AliYun);
            } else {
                // 默认回退设置
                fileMeta.setBucket("athena-artifacts-stagging");
                fileMeta.setSource(OSSProviderSource.AliYun);
            }

            return fileMeta;
        } catch (Exception e) {
            // 测试回退配置
            OssClient.OSSFileMeta fileMeta = new OssClient.OSSFileMeta();
            fileMeta.setPath("artifacts/default/sample/202503.ts");
            fileMeta.setBucket("athena-artifacts-stagging");
            fileMeta.setSource(OSSProviderSource.AliYun);
            return fileMeta;
        }
    }

    /**
     * 获取TS文件URL列表
     */
    private List<String> getTsFileUrls() {
        List<String> urls = new ArrayList<>();

        // 用户提供的TS文件URL列表
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/cdc46187-40f6-4665-b8bc-8b8d61309e99/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/1510f713-2fe2-4f6b-acf8-ffc727a0d9b0/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/21455e29-501f-44da-ad93-30e2dc172072/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/9b59e1a7-14b7-4922-9773-191f13f18669/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/33a52225-210b-43c0-881b-b2dc3cbfed1a/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/98666608-3c46-4862-b89f-4cfba859c565/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/468011cb-54b5-4f18-88c7-81d44b874667/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/85d30e6b-bdec-45c8-9fea-a6a55999bb6f/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/f8ba2629-e859-4eaa-b26c-ea2eef3f683e/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/e3181b48-c6b0-4281-bd1d-1e866e2246fb/202503.ts");

        return urls;
    }

    /**
     * 创建模拟TS内容用于测试
     */
    private byte[] createDummyTsContent(int index, int size) {
        byte[] content = new byte[size];
        // 填充有规律的数据，使每个文件唯一
        for (int i = 0; i < content.length; i++) {
            content[i] = (byte) ((i + index) % 256);
        }
        return content;
    }

    /**
     * 使用真实的TS文件URL测试并行下载
     * 注意：这个测试使用用户提供的真实TS文件URL构建Asset对象
     */
    @Test
    @DisplayName("使用真实的TS URL测试并行下载")
    @Tag("RealUrlTest")
    void testDownloadTsFilesWithRealUrls() throws IOException {
        // 获取真实的TS文件URL列表
        List<String> tsUrls = getTsFileUrls();
        int testCount = Math.min(5, tsUrls.size()); // 使用5个或更少的URL进行测试

        // 使用真实URL创建Asset列表
        List<Asset> realAssets = new ArrayList<>();
        System.out.println("使用真实URL构建Asset列表：");

        for (int i = 0; i < testCount; i++) {
            String tsUrl = tsUrls.get(i);
            System.out.println("  - " + tsUrl);

            // 从每个URL解析元数据
            OssClient.OSSFileMeta fileMeta = parseOssUrl(tsUrl);

            // 创建Asset对象
            Asset asset = Mockito.mock(Asset.class);
            Mockito.when(asset.getId()).thenReturn((long) i + 1000); // 使用不同的ID范围
            Mockito.when(asset.getOssPath()).thenReturn(fileMeta.getPath());
            Mockito.when(asset.getOssSource()).thenReturn(fileMeta.getSource().value);
            Mockito.when(asset.getOssBucket()).thenReturn(fileMeta.getBucket());
            Mockito.when(asset.getOssProviderSourceEnum()).thenReturn(fileMeta.getSource());
            Mockito.when(asset.getMediaType()).thenReturn(AssetMediaType.TransportStream);
            Mockito.when(asset.getSource()).thenReturn(AssetSource.Generate);

            realAssets.add(asset);

            // 为每个Asset模拟返回不同大小的数据
            byte[] dummyData = createDummyTsContent(i, 20480 + i * 1024); // 每个文件大小稍有不同
            Mockito.doReturn(dummyData).when(assetService).getAssetBytes(asset);
        }

        // 创建临时目录用于测试
        File realTestDir = Files.createTempDirectory("real_ts_test").toFile();
        realTestDir.deleteOnExit();

        try {
            // 进行真实并行下载测试
            System.out.println("开始使用真实URL测试并行下载...");
            long startTime = System.currentTimeMillis();
            List<File> downloadedFiles = assetService.downloadTsFilesInParallel(realAssets, realTestDir);
            long endTime = System.currentTimeMillis();
            long timeTaken = endTime - startTime;

            // 验证下载结果
            assertNotNull(downloadedFiles, "下载的文件列表不应为null");
            assertEquals(realAssets.size(), downloadedFiles.size(), "下载文件数量应匹配Asset数量");

            // 验证每个文件都存在且有内容
            long totalSize = 0;
            for (int i = 0; i < downloadedFiles.size(); i++) {
                File file = downloadedFiles.get(i);
                assertTrue(file.exists(), "文件应该存在: " + file.getAbsolutePath());
                assertTrue(file.length() > 0, "文件不应为空: " + file.getAbsolutePath());
                totalSize += file.length();
                System.out.println("验证文件 #" + i + ": " + file.getAbsolutePath() + " (" + file.length() + " 字节)");
            }

            // 输出统计信息
            System.out.println("并行下载完成：");
            System.out.println("  - 下载文件数量: " + downloadedFiles.size());
            System.out.println("  - 总文件大小: " + totalSize + " 字节");
            System.out.println("  - 总耗时: " + timeTaken + " 毫秒");
            System.out.println("  - 平均每个文件下载时间: " + (timeTaken / downloadedFiles.size()) + " 毫秒");

            // 验证getAssetBytes方法被调用了正确的次数
            for (Asset asset : realAssets) {
                Mockito.verify(assetService).getAssetBytes(asset);
            }
        } finally {
            // 清理临时目录
            if (realTestDir.exists()) {
                File[] files = realTestDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        file.delete();
                    }
                }
                realTestDir.delete();
            }
        }
    }
}
