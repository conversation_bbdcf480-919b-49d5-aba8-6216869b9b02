package co.sandai.zeus.domain.asset.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

import co.sandai.zeus.common.helper.FFmpegHelper;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetMapper;
import co.sandai.zeus.domain.asset.dao.AssetMediaType;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.task.dao.mapper.TaskMapper;
import co.sandai.zeus.infra.Green;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.infer.ArtifactClient;
import co.sandai.zeus.infra.oss.OSSProviderSource;
import co.sandai.zeus.infra.oss.OssClient;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletionException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;

/**
 * 测试AssetService的streamTsListToMp4方法
 */
public class AssetServiceStreamTsToMp4Test {

    @Mock
    private AssetMapper assetMapper;

    @Mock
    private IDGenerator idGenerator;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private TaskMapper taskMapper;

    @Mock
    private Green green;

    @Mock
    private OssClient ossClient;

    @Mock
    private ArtifactClient artifactClient;

    @Mock
    private StringRedisTemplate redisTemplate;

    // 被测试类
    private AssetService assetService;

    // 用于存储临时文件的目录
    private File tempDir;

    // 模拟Asset列表
    private List<Asset> mockAssets;

    // 静态mock对象
    private static MockedStatic<FFmpegHelper> mockedFFmpegHelper;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化mock
        MockitoAnnotations.openMocks(this);

        // 创建AssetService实例
        assetService = spy(new AssetService(
                assetMapper, idGenerator, restTemplate, taskMapper, green, ossClient, artifactClient, redisTemplate));

        // 创建临时目录
        tempDir = Files.createTempDirectory("ts_to_mp4_test").toFile();
        tempDir.deleteOnExit();

        // 创建模拟的Asset列表
        mockAssets = createMockAssets(3);

        // 模拟downloadTsFilesInParallel方法的行为
        doAnswer(invocation -> {
                    List<Asset> assets = invocation.getArgument(0);
                    File directory = invocation.getArgument(1);

                    // 创建测试用的TS文件并返回
                    List<File> tsFiles = new ArrayList<>();
                    for (int i = 0; i < assets.size(); i++) {
                        File tsFile = new File(directory, "test_" + i + ".ts");
                        // 创建一个简单的TS文件内容 (这里用随机数据代替真实TS内容)
                        Files.write(tsFile.toPath(), createDummyTsContent(i, 10240));
                        tsFiles.add(tsFile);
                    }
                    return tsFiles;
                })
                .when(assetService)
                .downloadTsFilesInParallel(anyList(), any(File.class));

        // 关闭之前的静态mock（如果存在）
        if (mockedFFmpegHelper != null) {
            mockedFFmpegHelper.close();
        }

        // 创建新的静态mock
        mockedFFmpegHelper = mockStatic(FFmpegHelper.class);

        // 默认模拟行为 - 将MP4内容写入输出流
        mockedFFmpegHelper
                .when(() -> FFmpegHelper.streamTsToMp4(anyList(), any()))
                .then(invocation -> {
                    var outputStream = invocation.getArgument(1);
                    byte[] fakeMP4Header = "MOCK_MP4_HEADER".getBytes();
                    ((ByteArrayOutputStream) outputStream).write(fakeMP4Header);
                    return null;
                });
    }

    @AfterEach
    void tearDown() {
        // 清理临时资源
        if (tempDir != null && tempDir.exists()) {
            File[] files = tempDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    file.delete();
                }
            }
            tempDir.delete();
        }

        // 关闭静态mock
        if (mockedFFmpegHelper != null) {
            mockedFFmpegHelper.close();
            mockedFFmpegHelper = null;
        }
    }

    @Test
    @DisplayName("测试正常流程 - 多个TS文件转换为MP4")
    void testStreamTsListToMp4Normal() throws IOException, InterruptedException {
        // 准备输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 执行方法
        assetService.streamTsListToMp4(mockAssets, outputStream, false);

        // 验证结果
        byte[] result = outputStream.toByteArray();
        assertNotNull(result);
        assertTrue(result.length > 0);
        assertEquals("MOCK_MP4_HEADER", new String(result));

        // 验证调用
        verify(assetService).downloadTsFilesInParallel(eq(mockAssets), any(File.class));

        // 验证FFmpegHelper.streamTsToMp4被调用
        // 注意：不需要再次创建mockStatic，因为它已经在setUp中设置了
    }

    @Test
    @DisplayName("测试异常情况 - 空Asset列表")
    void testStreamTsListToMp4WithEmptyList() {
        // 准备空列表
        List<Asset> emptyList = Collections.emptyList();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 执行并验证异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, () -> assetService.streamTsListToMp4(emptyList, outputStream, false));

        assertEquals("Asset list cannot be empty for streaming conversion", exception.getMessage());
    }

    @Test
    @DisplayName("测试异常情况 - null Asset列表")
    void testStreamTsListToMp4WithNullList() {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 执行并验证异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, () -> assetService.streamTsListToMp4(null, outputStream, false));

        assertEquals("Asset list cannot be empty for streaming conversion", exception.getMessage());
    }

    @Test
    @DisplayName("测试异常情况 - 下载过程中出现异常")
    void testStreamTsListToMp4WithDownloadError() throws IOException, InterruptedException {
        // 模拟下载过程中出现异常 - 使用CompletionException包装IOException
        IOException downloadException = new IOException("Download failed");
        doThrow(new CompletionException(downloadException))
                .when(assetService)
                .downloadTsFilesInParallel(anyList(), any(File.class));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 执行并验证异常 - 测试CompletionException被抛出
        CompletionException exception =
                assertThrows(CompletionException.class, () -> assetService.streamTsListToMp4(mockAssets, outputStream));

        // 验证异常的根本原因是IOException
        assertTrue(exception.getCause() instanceof IOException);
        assertEquals("Download failed", exception.getCause().getMessage());

        // 确保临时目录清理逻辑被执行
        verify(assetService).downloadTsFilesInParallel(eq(mockAssets), any(File.class));
    }

    @Test
    @DisplayName("测试异常情况 - FFmpeg转换过程中出现异常")
    void testStreamTsListToMp4WithConversionError() throws IOException, InterruptedException {
        // 重置模拟行为，让streamTsToMp4方法抛出异常
        mockedFFmpegHelper.reset();
        mockedFFmpegHelper
                .when(() -> FFmpegHelper.streamTsToMp4(anyList(), any()))
                .thenThrow(new InterruptedException("Conversion failed"));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 执行并验证异常
        InterruptedException exception = assertThrows(
                InterruptedException.class, () -> assetService.streamTsListToMp4(mockAssets, outputStream));

        assertEquals("Conversion failed", exception.getMessage());

        // 验证调用与清理
        verify(assetService).downloadTsFilesInParallel(eq(mockAssets), any(File.class));
    }

    /**
     * 创建指定数量的模拟Asset对象
     */
    private List<Asset> createMockAssets(int count) {
        List<Asset> assets = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            Asset asset = mock(Asset.class);
            when(asset.getId()).thenReturn((long) i);
            when(asset.getOssPath()).thenReturn("artifacts/default/test_" + i + "/1234.ts");
            when(asset.getOssSource()).thenReturn(OSSProviderSource.AliYun.value);
            when(asset.getOssBucket()).thenReturn("athena-artifacts-stagging");
            when(asset.getOssProviderSourceEnum()).thenReturn(OSSProviderSource.AliYun);
            when(asset.getMediaType()).thenReturn(AssetMediaType.TransportStream);
            when(asset.getSource()).thenReturn(AssetSource.Generate);

            assets.add(asset);

            // 模拟getAssetBytes方法的返回值
            doReturn(createDummyTsContent(i, 10240)).when(assetService).getAssetBytes(asset);
        }

        return assets;
    }

    /**
     * 创建测试用的TS文件内容
     */
    private byte[] createDummyTsContent(int index, int size) {
        byte[] content = new byte[size];
        // 填充一些数据，使每个文件内容唯一
        Arrays.fill(content, (byte) (index % 256));
        // 添加TS文件特征头部
        if (content.length > 4) {
            content[0] = 0x47; // TS同步字节
            content[1] = (byte) index;
            content[2] = (byte) (index + 1);
            content[3] = (byte) (index + 2);
        }
        return content;
    }
}
