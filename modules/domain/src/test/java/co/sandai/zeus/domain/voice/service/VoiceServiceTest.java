package co.sandai.zeus.domain.voice.service;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

public class VoiceServiceTest {

    private VoiceService voiceService;
    private HttpURLConnection mockConnection;

    @BeforeEach
    public void setUp() {
        voiceService = new VoiceService();
        mockConnection = mock(HttpURLConnection.class);
    }

    @Test
    public void testDownloadFromUrl_Success() throws Exception {
        // Test data
        String testUrl = "https://example.com/audio.mp3";
        byte[] expectedData = "test audio data".getBytes();

        // Mock URL and connection
        URL mockUrl = mock(URL.class);
        try (MockedStatic<URI> uriMock = mockStatic(URI.class)) {
            URI mockUri = mock(URI.class);
            uriMock.when(() -> URI.create(testUrl)).thenReturn(mockUri);
            when(mockUri.toURL()).thenReturn(mockUrl);
            when(mockUrl.openConnection()).thenReturn(mockConnection);

            // Mock input stream with test data
            InputStream mockStream = new ByteArrayInputStream(expectedData);
            when(mockConnection.getInputStream()).thenReturn(mockStream);

            // Test the method
            byte[] result = voiceService.downloadFromUrl(testUrl);

            // Assert the result
            assertArrayEquals(expectedData, result, "Downloaded data should match expected test data");
        }
    }

    @Test
    public void testDownloadFromUrl_IOError() throws Exception {
        // Test data
        String testUrl = "https://example.com/not-found.mp3";

        // Mock URL and connection
        URL mockUrl = mock(URL.class);
        try (MockedStatic<URI> uriMock = mockStatic(URI.class)) {
            URI mockUri = mock(URI.class);
            uriMock.when(() -> URI.create(testUrl)).thenReturn(mockUri);
            when(mockUri.toURL()).thenReturn(mockUrl);
            when(mockUrl.openConnection()).thenReturn(mockConnection);

            // Mock IO Exception
            when(mockConnection.getInputStream()).thenThrow(new IOException("Connection failed"));

            // Test the method - should throw IOException
            assertThrows(
                    IOException.class,
                    () -> {
                        voiceService.downloadFromUrl(testUrl);
                    },
                    "Method should throw IOException on connection failure");
        }
    }

    @Test
    public void testDownloadFromUrl_NullUrl() {
        // Test with null URL
        assertThrows(
                NullPointerException.class,
                () -> {
                    voiceService.downloadFromUrl(null);
                },
                "Method should throw NullPointerException when URL is null");
    }
}
