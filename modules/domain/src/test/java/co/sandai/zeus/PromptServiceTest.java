package co.sandai.zeus;

import static io.github.benas.randombeans.api.EnhancedRandom.randomListOf;
import static org.assertj.core.api.Assertions.assertThat;

import co.sandai.zeus.infra.prompt.PromptService;
import co.sandai.zeus.infra.prompt.dto.ChunkDTO;
import co.sandai.zeus.infra.prompt.dto.PromptEnhancementResultDTO;
import co.sandai.zeus.infra.web.RestTemplateConfig;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;

@MapperScan("co.sandai.zeus.domain")
@Import(MyBatisConfig.class)
@ExtendWith(MockitoExtension.class)
@SpringBootTest(
        classes = {
            TestApplication.class,
            RestTemplateConfig.class,
            PromptService.class,
            RestTemplateAutoConfiguration.class
        })
public class PromptServiceTest extends TestApplication {

    @Autowired
    private PromptService promptService;

    @Test
    public void testPromptEnhancement() {
        PromptEnhancementResultDTO result = promptService.enhancePrompt("hello girl", null, null, false, "t2v_chain");
        System.out.println(result.getGeneratedPrompt());
        System.out.println(result.getGeneratedImagePrompt());
    }

    @Test
    public void testListVersions() {
        List<String> list = promptService.listEnhancementTypes();
        System.out.println(list);
        assertThat(list).isNotEmpty();
    }

    @Test
    void enhanceTextPrompts_Success() {
        // Arrange
        List<ChunkDTO> prompts = randomListOf(2, ChunkDTO.class);
        String enhancementType = "standard";

        // Act
        List<String> results = promptService.enhanceTextPrompts(prompts, enhancementType, null);

        // Assert
        assertThat(results).hasSize(2);
        assertThat(results).allMatch(result -> result != null && !result.isEmpty());
    }
}
