package co.sandai.zeus.domain.plan;

import static io.github.benas.randombeans.api.EnhancedRandom.random;
import static io.github.benas.randombeans.api.EnhancedRandom.randomListOf;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import co.sandai.zeus.TestApplication;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.payment.PaySuccessEvent;
import co.sandai.zeus.domain.payment.dao.OrderDO;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.dao.UserPlanDao;
import co.sandai.zeus.domain.plan.dao.UserPlanMapper;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.PriceModel;
import co.sandai.zeus.domain.plan.model.enums.IntervalTypeEnum;
import co.sandai.zeus.domain.plan.model.enums.PlanStatusEnum;
import co.sandai.zeus.infra.IDGenerator;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = {TestApplication.class})
@ExtendWith(MockitoExtension.class)
public class UserPlanServiceTest extends TestApplication {

    @InjectMocks
    private UserPlanService userPlanService;

    @Mock
    private IDGenerator idGenerator;

    @Mock
    private UserPlanDao userPlanDao;

    @Mock
    private UserPlanMapper userPlanMapper;

    @Mock
    private UserCreditService userCreditService;

    @Mock
    private PlanConfigService planConfigService;

    @BeforeEach
    public void beforeClass() throws Exception {
        // setup injectMocks
        List<Plan> plans = randomListOf(2, Plan.class);
        when(planConfigService.getActivePlanList()).thenReturn(plans);
    }

    @Test
    public void dealUserPlanAndIssueCredits_subscribe() {
        // Arrange
        PaySuccessEvent paySuccessEvent = new PaySuccessEvent(this);
        paySuccessEvent.setFrom(OrderDO.FromEnum.SUBSCRIBE);
        paySuccessEvent.setPriceId("priceId");
        paySuccessEvent.setUserId(1L);
        paySuccessEvent.setPayTime(LocalDateTime.now());

        PriceModel priceModel = new PriceModel();
        priceModel.setOutCode("outCode");

        Plan plan = new Plan();
        plan.setIntervalTypeEnum(IntervalTypeEnum.MONTHLY);

        when(planConfigService.getPriceModelByPriceId("priceId")).thenReturn(priceModel);
        when(planConfigService.getPlanByPriceId("priceId", PlanStatusEnum.ACTIVE))
                .thenReturn(plan);
        when(idGenerator.getNextId()).thenReturn(1L);

        // Act
        userPlanService.dealUserPlanAndIssueCredits(paySuccessEvent);

        // Assert
        verify(userPlanDao, times(1)).generatePlan(any(UserPlanDO.class));
        verify(userCreditService, times(1)).issueSubscriptionCredit(eq(1L), eq(plan), any(LocalDateTime.class));
    }

    @Test
    public void dealUserPlanAndIssueCredits_renew() {
        // Arrange
        PaySuccessEvent paySuccessEvent = new PaySuccessEvent(this);
        paySuccessEvent.setFrom(OrderDO.FromEnum.RENEW);
        paySuccessEvent.setPriceId("priceId");
        paySuccessEvent.setUserId(1L);
        paySuccessEvent.setPayTime(LocalDateTime.now());

        PriceModel priceModel = new PriceModel();
        priceModel.setOutCode("outCode");

        Plan plan = new Plan();
        plan.setIntervalTypeEnum(IntervalTypeEnum.MONTHLY);

        when(planConfigService.getPriceModelByPriceId("priceId")).thenReturn(priceModel);
        when(planConfigService.getPlanByPriceId("priceId", PlanStatusEnum.ACTIVE))
                .thenReturn(plan);
        when(idGenerator.getNextId()).thenReturn(1L);
        UserPlanDO userPlanDO = random(UserPlanDO.class);
        when(userPlanMapper.queryByUserId(any(Long.class))).thenReturn(userPlanDO);

        // Act
        userPlanService.dealUserPlanAndIssueCredits(paySuccessEvent);

        // Assert
        verify(userPlanMapper, times(1)).queryByUserId(any(Long.class));
        verify(userCreditService, times(1)).renewSubscriptionCredit(eq(1L), eq(plan), any(LocalDateTime.class));
    }

    @Test
    public void changeSubscription() {
        // Arrange
        long userId = 1L;
        Plan currentPlan = new Plan();
        currentPlan.setPlanCode("currentPlanCode");
        currentPlan.setIntervalTypeEnum(IntervalTypeEnum.MONTHLY);
        currentPlan.setPlanGrade(1);

        Plan oldPlan = new Plan();
        oldPlan.setPlanCode("oldPlanCode");
        oldPlan.setIntervalTypeEnum(IntervalTypeEnum.YEARLY);
        oldPlan.setPlanGrade(2);

        LocalDateTime payTime = LocalDateTime.now();

        UserPlanDO userPlanDO = new UserPlanDO();
        userPlanDO.setUserId(userId);

        when(userPlanMapper.queryByUserId(userId)).thenReturn(userPlanDO);

        // Act
        userPlanService.changeSubscription(userId, currentPlan, payTime);

        // Assert
        verify(userPlanMapper, times(1)).update(any(UserPlanDO.class));
    }

    @Test
    public void queryUserPlan() {
        // Arrange
        long userId = 1L;
        UserPlanDO userPlanDO = new UserPlanDO();
        userPlanDO.setUserId(userId);

        when(userPlanMapper.queryByUserId(userId)).thenReturn(userPlanDO);

        // Act
        UserPlanDO result = userPlanService.queryUserPlan(userId);

        // Assert
        assertThat(result).isEqualTo(userPlanDO);
    }

    @Test
    public void queryYearlyRenewPlan() {
        // Arrange
        Long lowerId = 1L;
        int batchSize = 10;
        List<Plan> plans = randomListOf(2, Plan.class);
        plans.get(0).setIntervalTypeEnum(IntervalTypeEnum.YEARLY);
        plans.get(0).setStatus(PlanStatusEnum.ACTIVE);
        plans.get(1).setIntervalTypeEnum(IntervalTypeEnum.YEARLY);
        plans.get(0).setStatus(PlanStatusEnum.DEPRECATED);
        List<String> planCodes = plans.stream().map(Plan::getPlanCode).toList();
        when(planConfigService.getActivePlanList()).thenReturn(plans);

        List<UserPlanDO> expectedPlans = List.of(new UserPlanDO());

        when(userPlanMapper.queryYearlyRenewPlan(eq(lowerId), eq(batchSize), eq(planCodes), any(), any(), any()))
                .thenReturn(expectedPlans);

        // Act
        List<UserPlanDO> result = userPlanService.queryYearlyRenewPlan(lowerId, batchSize);

        // Assert
        assertThat(result).isEqualTo(expectedPlans);
    }

    @Test
    public void batchUpdateNextRenewTime() {
        // Arrange
        List<UserPlanDO> userPlans = List.of(new UserPlanDO());

        // Act
        userPlanService.batchUpdateNextRenewTime(userPlans);

        // Assert
        verify(userPlanMapper, times(1)).batchUpdateNextRenewTime(userPlans);
    }
}
