package co.sandai.zeus.domain.credit.dao;

import static org.assertj.core.api.Assertions.assertThat;

import co.sandai.zeus.MyBatisConfig;
import co.sandai.zeus.TestApplication;
import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.credit.enums.CreditTypeEnum;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

@MapperScan("co.sandai.zeus.domain.credit.dao")
@Import(MyBatisConfig.class)
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TestApplication.class, UserCreditAndTransactionDao.class})
public class UserCreditAndTransactionDaoTest extends TestApplication {

    @Autowired
    private UserCreditAndTransactionDao userCreditAndTransactionDao;

    @Autowired
    private UserCreditMapper userCreditMapper;

    @Autowired
    private UserCreditTransactionMapper userCreditTransactionMapper;

    @Test
    public void testIssueSubscriptionDiffCredit() {
        // Arrange
        Long userId = 1L;
        long creditNum = 100L;
        long newCreditNum = 200L;
        userCreditMapper.insertUserCredit(new UserCreditDO()
                .setUserId(userId)
                .setOrgId(0L)
                .setAmount(creditNum)
                .setCreditType(CreditTypeEnum.SUBSCRIPTION.name())
                .setExpireTime(Timestamp.valueOf(LocalDateTime.now().plusMonths(1L)))
                .setTempCredit(false));

        // Act
        userCreditAndTransactionDao.issueSubscriptionDiffCredit(userId, Math.toIntExact(newCreditNum));

        // Assert
        // Add assertions to verify the expected behavior
        assertThat(userCreditAndTransactionDao).isNotNull();
        userCreditMapper
                .getNotExpiredUserCredits(userId, CreditTypeEnum.SUBSCRIPTION.name())
                .forEach(userCreditDO -> {
                    assertThat(userCreditDO.getAmount()).isEqualTo(creditNum + newCreditNum);
                });
        userCreditTransactionMapper
                .getUserCreditTransactionsByUserId(userId, null, 20)
                .forEach(userCreditTransactionDO -> {
                    assertThat(userCreditTransactionDO.getAmount()).isEqualTo(newCreditNum);
                    assertThat(userCreditTransactionDO.getCreditType()).isEqualTo(CreditTypeEnum.SUBSCRIPTION.name());
                    assertThat(userCreditTransactionDO.getDirection())
                            .isEqualTo(UserCreditTransactionDO.DirectionEnum.IN.name());
                    assertThat(userCreditTransactionDO.getReasonType())
                            .isEqualTo(UserCreditTransactionDO.ReasonTypeEnum.PURCHASED.name());
                    assertThat(userCreditTransactionDO.getReasonSubType())
                            .isEqualTo(UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT.name());
                });
    }

    @Test
    public void testIssueSubscriptionCredit() {
        // Arrange
        Long userId = 1L;
        Integer amount = 100;
        LocalDateTime payTime = LocalDateTime.now();

        // Act
        userCreditAndTransactionDao.issueSubscriptionCredit(userId, amount, payTime);

        // Assert
        List<UserCreditDO> userCredits =
                userCreditMapper.getUserCreditsIncludingDeleted(userId, CreditTypeEnum.SUBSCRIPTION.name(), false);
        assertThat(userCredits).isNotEmpty();
        UserCreditDO userCreditDO = userCredits.get(0);
        assertThat(userCreditDO.getAmount()).isEqualTo(Long.valueOf(amount));
        assertThat(userCreditDO.getExpireTime()).isEqualTo(Timestamp.valueOf(payTime.plusMonths(1)));

        List<UserCreditTransactionDO> transactions =
                userCreditTransactionMapper.getUserCreditTransactionsByUserId(userId, null, 20);
        assertThat(transactions).isNotEmpty();
        UserCreditTransactionDO transaction = transactions.get(0);
        assertThat(transaction.getAmount()).isEqualTo(amount);
        assertThat(transaction.getCreditType()).isEqualTo(CreditTypeEnum.SUBSCRIPTION.name());
        assertThat(transaction.getDirection()).isEqualTo(UserCreditTransactionDO.DirectionEnum.IN.name());
        assertThat(transaction.getReasonSubType())
                .isEqualTo(UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT.name());
    }

    @Test
    public void testIssuePurchasedCredit() {
        // Arrange
        Long userId = 1L;
        String requestId = "test-request-id";
        int amount = 100;
        LocalDateTime payTime = LocalDateTime.now();

        // Act
        userCreditAndTransactionDao.issuePurchasedUserCredit(userId, requestId, amount, payTime);

        // Assert
        UserCreditDO userCredit =
                userCreditMapper.getUserCreditByRequestId(userId, requestId, CreditTypeEnum.PURCHASED.name());
        assertThat(userCredit).isNotNull();
        assertThat(userCredit.getAmount()).isEqualTo(Long.valueOf(amount));
        assertThat(userCredit.getExpireTime()).isEqualTo(Timestamp.valueOf(payTime.plusMonths(1)));

        List<UserCreditTransactionDO> transactions =
                userCreditTransactionMapper.getUserCreditTransactionsByUserId(userId, null, 20);
        assertThat(transactions).isNotEmpty();
        UserCreditTransactionDO transaction = transactions.get(0);
        assertThat(transaction.getAmount()).isEqualTo(amount);
        assertThat(transaction.getCreditType()).isEqualTo(CreditTypeEnum.PURCHASED.name());
        assertThat(transaction.getDirection()).isEqualTo(UserCreditTransactionDO.DirectionEnum.IN.name());
        assertThat(transaction.getReasonSubType())
                .isEqualTo(UserCreditTransactionDO.ReasonSubTypeEnum.PURCHASED_CREDIT.name());
    }

    @Test
    public void testIssueFreeCredit() {
        // Arrange
        Long userId = 1L;
        Integer amount = 100;
        LocalDateTime issueAt = LocalDateTime.now();

        // Act
        userCreditAndTransactionDao.issueFreeCredit(userId, amount, issueAt);

        // Assert
        List<UserCreditDO> userCredits =
                userCreditMapper.getUserCreditsIncludingDeleted(userId, CreditTypeEnum.FREE.name(), false);
        assertThat(userCredits).isNotEmpty();
        UserCreditDO userCreditDO = userCredits.get(0);
        assertThat(userCreditDO.getAmount()).isEqualTo(Long.valueOf(amount));
        assertThat(userCreditDO.getExpireTime()).isEqualTo(TimeUtil.getNextMonthStartTimestamp(issueAt));

        List<UserCreditTransactionDO> transactions =
                userCreditTransactionMapper.getUserCreditTransactionsByUserId(userId, null, 20);
        assertThat(transactions).isNotEmpty();
        UserCreditTransactionDO transaction = transactions.get(0);
        assertThat(transaction.getAmount()).isEqualTo(amount);
        assertThat(transaction.getCreditType()).isEqualTo(CreditTypeEnum.FREE.name());
        assertThat(transaction.getDirection()).isEqualTo(UserCreditTransactionDO.DirectionEnum.IN.name());
        assertThat(transaction.getReasonSubType())
                .isEqualTo(UserCreditTransactionDO.ReasonSubTypeEnum.MONTHLY_FREE_CREDIT.name());
    }

    @Test
    public void issueNewSubscriptionCredit() {
        // Arrange
        Long userId = 1L;
        Integer oldAmount = 200;
        LocalDateTime oldIssueAt = LocalDateTime.now().minusDays(3).minusMinutes(100L);
        userCreditAndTransactionDao.issueSubscriptionCredit(userId, oldAmount, oldIssueAt);

        Integer amount = 100;
        LocalDateTime issueAt = LocalDateTime.now();

        // Act
        userCreditAndTransactionDao.issueNewSubscriptionCredit(userId, amount, issueAt);

        // Assert
        List<UserCreditDO> userCredits =
                userCreditMapper.getUserCreditsIncludingDeleted(userId, CreditTypeEnum.SUBSCRIPTION.name(), false);
        assertThat(userCredits).isNotEmpty();
        assertThat(userCredits).hasSize(2);
        UserCreditDO userCreditDO = userCredits.get(0);
        assertThat(userCreditDO.getAmount()).isEqualTo(Long.valueOf(oldAmount));
        assertThat(userCreditDO.getTempCredit()).isEqualTo(true);

        assertThat(TimeUtil.format(userCreditDO.getExpireTime()))
                .isEqualTo(TimeUtil.format(TimeUtil.addMonths(oldIssueAt, 1)));

        UserCreditDO userCreditDO1 = userCredits.get(1);
        assertThat(userCreditDO1.getAmount()).isEqualTo(Long.valueOf(amount));
        assertThat(userCreditDO1.getTempCredit()).isEqualTo(false);
        assertThat(TimeUtil.format(userCreditDO1.getExpireTime()))
                .isEqualTo(TimeUtil.format(TimeUtil.addMonths(issueAt, 1)));

        List<UserCreditTransactionDO> transactions =
                userCreditTransactionMapper.getUserCreditTransactionsByUserId(userId, null, 20);
        assertThat(transactions).isNotEmpty();
        UserCreditTransactionDO transaction = transactions.get(1);
        assertThat(transaction.getAmount()).isEqualTo(amount);
        assertThat(transaction.getCreditType()).isEqualTo(CreditTypeEnum.SUBSCRIPTION.name());
        assertThat(transaction.getDirection()).isEqualTo(UserCreditTransactionDO.DirectionEnum.IN.name());
        assertThat(transaction.getReasonSubType())
                .isEqualTo(UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT.name());
    }

    @Test
    public void renewSubscriptionCredit() {
        // Arrange
        Long userId = 1L;
        Integer oldAmount = 50;
        LocalDateTime oldPayTime = LocalDateTime.now().minusMonths(1);
        userCreditAndTransactionDao.issueSubscriptionCredit(userId, oldAmount, oldPayTime);

        Integer newAmount = 100;
        LocalDateTime newPayTime = LocalDateTime.now();

        // Act
        userCreditAndTransactionDao.renewSubscriptionCredit(userId, newAmount, newPayTime);

        // Assert
        List<UserCreditDO> userCredits =
                userCreditMapper.getUserCreditsIncludingDeleted(userId, CreditTypeEnum.SUBSCRIPTION.name(), false);
        assertThat(userCredits).isNotEmpty();
        UserCreditDO userCreditDO = userCredits.get(0);
        assertThat(userCreditDO.getAmount()).isEqualTo(Long.valueOf(newAmount));
        assertThat(userCreditDO.getExpireTime()).isEqualTo(Timestamp.valueOf(newPayTime.plusMonths(1)));

        List<UserCreditTransactionDO> transactions =
                userCreditTransactionMapper.getUserCreditTransactionsByUserId(userId, null, 20);
        assertThat(transactions).isNotEmpty();
        assertThat(transactions).hasSize(3);
        UserCreditTransactionDO transaction = transactions.get(1);
        assertThat(transaction.getAmount()).isEqualTo(oldAmount);
        assertThat(transaction.getCreditType()).isEqualTo(CreditTypeEnum.SUBSCRIPTION.name());
        assertThat(transaction.getDirection()).isEqualTo(UserCreditTransactionDO.DirectionEnum.OUT.name());
        assertThat(transaction.getReasonSubType())
                .isEqualTo(UserCreditTransactionDO.ReasonSubTypeEnum.EXPIRED_CREDITS.name());

        UserCreditTransactionDO transaction1 = transactions.get(2);
        assertThat(transaction1.getAmount()).isEqualTo(newAmount);
        assertThat(transaction1.getCreditType()).isEqualTo(CreditTypeEnum.SUBSCRIPTION.name());
        assertThat(transaction1.getDirection()).isEqualTo(UserCreditTransactionDO.DirectionEnum.IN.name());
        assertThat(transaction1.getReasonSubType())
                .isEqualTo(UserCreditTransactionDO.ReasonSubTypeEnum.SUBSCRIPTION_CREDIT.name());
    }

    @Test
    public void subtractCredits() {
        // Arrange
        Long userId = 1L;
        LocalDateTime payTime = LocalDateTime.now();
        userCreditAndTransactionDao.issueSubscriptionCredit(userId, 100, payTime.minusMonths(1));
        userCreditAndTransactionDao.issuePurchasedUserCredit(userId, "1111", 200, payTime.minusDays(1));

        // Act
        int costAmount = 150;
        userCreditAndTransactionDao.deductCredits(
                userId,
                null,
                costAmount,
                UserCreditTransactionDO.ReasonTypeEnum.SPENT,
                UserCreditTransactionDO.ReasonSubTypeEnum.VIDEO_GENERATION);

        // Assert
        List<UserCreditDO> userCredits = userCreditMapper.getUserCreditsIncludingDeleted(userId, null, false);
        assertThat(userCredits).isNotEmpty();
        UserCreditDO userCreditDO = userCredits.get(0);
        assertThat(userCreditDO.getAmount()).isEqualTo(0L);
        UserCreditDO userCreditDO1 = userCredits.get(1);
        assertThat(userCreditDO1.getAmount()).isEqualTo(150L);

        List<UserCreditTransactionDO> transactions =
                userCreditTransactionMapper.getUserCreditTransactionsByUserId(userId, null, 20);
        assertThat(transactions).isNotEmpty();
        assertThat(transactions).hasSize(3);
        UserCreditTransactionDO transaction = transactions.get(2);
        assertThat(transaction.getAmount()).isEqualTo(costAmount);
        assertThat(transaction.getDirection()).isEqualTo(UserCreditTransactionDO.DirectionEnum.OUT.name());
        assertThat(transaction.getReasonSubType())
                .isEqualTo(UserCreditTransactionDO.ReasonSubTypeEnum.VIDEO_GENERATION.name());
    }
}
