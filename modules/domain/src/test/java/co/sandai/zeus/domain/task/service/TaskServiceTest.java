package co.sandai.zeus.domain.task.service;

import co.sandai.zeus.MyBatisConfig;
import co.sandai.zeus.infra.Green;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;

@SpringBootTest
@MapperScan("co.sandai.zeus.domain")
@Import(MyBatisConfig.class)
public class TaskServiceTest {

    @Autowired
    private Green green;

    @Test
    public void testTextModeration() {
        Green.ModerateTextResult result = green.moderateText("hello world", 123);
        Assertions.assertTrue(result.isValid());

        result = green.moderateText("", 123);
        Assertions.assertTrue(result.isValid());

        String longPrompt = "hello world".repeat(100);

        result = green.moderateText(longPrompt, 123);
        Assertions.assertTrue(result.isValid());

        result = green.moderateText("pengliyuan", 123);
        Assertions.assertFalse(result.isValid());
    }
}
