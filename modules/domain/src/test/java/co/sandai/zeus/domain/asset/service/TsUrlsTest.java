package co.sandai.zeus.domain.asset.service;

import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * 专门用于测试TS文件URL列表的功能测试类
 */
public class TsUrlsTest {

    /**
     * 测试TS文件URL列表是否可用
     */
    @Test
    void testTsUrlsAreValid() throws IOException {
        // 使用TS URL列表进行验证
        List<String> tsUrls = getTsFileUrls();
        System.out.println("测试使用以下TS URL列表:");

        for (String url : tsUrls) {
            System.out.println("  - " + url);
            // 验证URL格式是否有效
            URI uri = URI.create(url);
            URL parsedUrl = uri.toURL();
            assertTrue(parsedUrl.getProtocol().startsWith("http"), "URL应以http或https开头");
        }

        // 创建并写入输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        String message = "验证TS文件URL列表成功";
        outputStream.write(message.getBytes());

        // 验证输出流包含数据
        assertTrue(outputStream.size() > 0, "输出流应包含数据");

        // 打印成功信息
        System.out.println("URL列表验证成功!");
    }

    /**
     * 获取TS文件URL列表
     */
    private List<String> getTsFileUrls() {
        List<String> urls = new ArrayList<>();

        // 用户提供的TS文件URL列表
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/cdc46187-40f6-4665-b8bc-8b8d61309e99/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/1510f713-2fe2-4f6b-acf8-ffc727a0d9b0/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/21455e29-501f-44da-ad93-30e2dc172072/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/9b59e1a7-14b7-4922-9773-191f13f18669/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/33a52225-210b-43c0-881b-b2dc3cbfed1a/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/98666608-3c46-4862-b89f-4cfba859c565/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/468011cb-54b5-4f18-88c7-81d44b874667/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/85d30e6b-bdec-45c8-9fea-a6a55999bb6f/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/f8ba2629-e859-4eaa-b26c-ea2eef3f683e/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/e3181b48-c6b0-4281-bd1d-1e866e2246fb/202503.ts");

        return urls;
    }
}
