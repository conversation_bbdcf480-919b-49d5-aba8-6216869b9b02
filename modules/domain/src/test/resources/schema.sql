create table if not exists user_credit
(
    id            bigint auto_increment
        primary key,
    user_id       bigint                             not null ,
    credit_type   varchar(16)                         not null,
    amount        bigint    default 0                          not null ,
    expire_time   timestamp default CURRENT_TIMESTAMP not null,
    request_id    varchar(32)                         null ,
    temp_credit   tinyint   default 0                 not null,
    deleted       tinyint   default 0                 not null ,
    create_time   datetime  default CURRENT_TIMESTAMP not null ,
    modified_time datetime  default CURRENT_TIMESTAMP not null
);

create table if not exists user_credit_transaction
(
    id              bigint auto_increment
        primary key,
    user_id         bigint                             not null,
    credit_type     varchar(16)                        not null,
    direction       varchar(16)                        not null,
    amount          int                                not null,
    reason_type     varchar(32)                        not null,
    reason_sub_type varchar(32)                        not null,
    trace_id        varchar(128)                       not null,
    deleted         tinyint  default 0                 not null ,
    create_time     datetime default CURRENT_TIMESTAMP not null ,
    modified_time   datetime default CURRENT_TIMESTAMP not null
);

create table if not exists user_plan
(
    id                  bigint                              not null
        primary key,
    plan_code           varchar(64)                         not null,
    user_id             bigint                              not null,
    expire_time         datetime                            not null,
    next_renew_time     datetime                            null,
    create_time         datetime  default CURRENT_TIMESTAMP not null,
    modified_time       datetime  default CURRENT_TIMESTAMP not null,
    constraint uq_user_id
        unique (user_id)
);

create table if not exists asset
(
    id                    bigint                              not null primary key,
    oss_path             varchar(1024)  default ''           not null,
    oss_source           varchar(64)    default ''           not null,
    oss_bucket           varchar(64)    default ''           not null,
    media_type           varchar(64)    default ''           not null,
    source               varchar(64)                         not null,
    width                int           default 0             not null,
    height               int           default 0             not null,
    poster_asset_id      bigint        default 0            not null,
    user_id              bigint        default 0            not null,
    task_id              bigint        default 0            not null,
    index                int           default 0            not null,
    task_chunk_id        bigint                            null,
    create_time          datetime     default CURRENT_TIMESTAMP not null,
    update_time          datetime     default CURRENT_TIMESTAMP not null,
    is_moderation_passed tinyint      default 1            not null,
    urn                  varchar(255)  default ''           not null
);