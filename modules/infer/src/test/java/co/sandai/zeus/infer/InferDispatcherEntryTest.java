package co.sandai.zeus.infer;

import co.sandai.zeus.infer.dto.event.InferEvent;
import co.sandai.zeus.infer.dto.event.JobStatusUpdated;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.Test;

class InferDispatcherEntryTest {

    @Test
    void processInferEvent_ShouldParseJobStatusUpdatedEvent() throws Exception {
        // Arrange
        String jsonMessage =
                """
                        {
                            "notification_type": "JOB_STATUS_UPDATED",
                            "notification_id": 329,
                            "payload": {
                                "job": {
                                    "prompt": "Man rides bike along beach, enjoying the sunset",
                                    "model_version": "1268720",
                                    "status": "succeeded",
                                    "project_name": "default",
                                    "queue": "high",
                                    "progress": 100,
                                    "pipeline_id": 60,
                                    "priority": 0,
                                    "deadline": null,
                                    "pipeline_position": 0,
                                    "streaming_key": "acd2ec2f-9df5-4f48-9bf9-0aa69d4eae98",
                                    "started_at": "2025-01-07T03:56:44.295252Z",
                                    "id": 329,
                                    "type": "I2V",
                                    "inputs": [
                                        {
                                            "name": "image",
                                            "urn": "artifact://090beb46-d794-4a7d-acb4-083558450d53",
                                            "url": "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/090beb46-d794-4a7d-acb4-083558450d53/202501.jpeg?OSSAccessKeyId=LTAI5tHTjmHLrqrjGGfezHGU&Expires=2051346315&Signature=%2Bh3yxbqrcPWdGTOhQy99ataMtCY%3D"
                                        }
                                    ],
                                    "completed_at": "2025-01-07T03:58:50.919224Z",
                                    "request_id": null,
                                    "options": {
                                        "tSchedulerFunc": "",
                                        "tSchedulerFuncArgs": "",
                                        "seed": 8076907,
                                        "aspectRatio": "16:9",
                                        "specialTokens": [

                                        ],
                                        "vaeModel": "",
                                        "duration": 3
                                    },
                                    "retrieved_at": null,
                                    "model": "",
                                    "outputs": [
                                        {
                                            "name": "segment_0.ts",
                                            "urn": "artifact://f145c1da-c2f5-41da-9c58-be33d225d4f2",
                                            "meta": {
                                                "duration": 0.966667,
                                                "type": "segment"
                                            },
                                            "url": "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/f145c1da-c2f5-41da-9c58-be33d225d4f2/202501.ts?OSSAccessKeyId=LTAI5tHTjmHLrqrjGGfezHGU&Expires=2051582282&Signature=jVPPZdROGn4Y%2Fo8kHWio8GAZclk%3D"
                                        },
                                        {
                                            "name": "thumbnail.jpg",
                                            "urn": "artifact://4e7402ad-9759-4714-854c-08b4bce02f5a",
                                            "meta": {
                                                "type": "thumbnail"
                                            },
                                            "url": "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/4e7402ad-9759-4714-854c-08b4bce02f5a/202501.jpg?OSSAccessKeyId=LTAI5tHTjmHLrqrjGGfezHGU&Expires=2051582283&Signature=Os9C7k3BAV7l6f2J1vIJlhR2wpY%3D"
                                        },
                                        {
                                            "name": "segment_1.ts",
                                            "urn": "artifact://6970436a-ad75-4b8a-a890-27ee877b3e82",
                                            "meta": {
                                                "duration": 1.066667,
                                                "type": "segment"
                                            },
                                            "url": "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/6970436a-ad75-4b8a-a890-27ee877b3e82/202501.ts?OSSAccessKeyId=LTAI5tHTjmHLrqrjGGfezHGU&Expires=2051582304&Signature=p7meJeZ0lpSoxUfkg6ktG10zlZY%3D"
                                        },
                                        {
                                            "name": "segment_2.ts",
                                            "urn": "artifact://cf04b7af-2c9c-4126-a3d9-24ec460bbcc6",
                                            "meta": {
                                                "duration": 1.066667,
                                                "type": "segment"
                                            },
                                            "url": "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/cf04b7af-2c9c-4126-a3d9-24ec460bbcc6/202501.ts?OSSAccessKeyId=LTAI5tHTjmHLrqrjGGfezHGU&Expires=2051582321&Signature=CNPR5y5SPzKU4F9nls8hKUtv1eY%3D"
                                        },
                                        {
                                            "name": "output.mp4",
                                            "urn": "artifact://c3f9ce6a-6f78-4c77-b35a-8ad410037961",
                                            "meta": {
                                                "type": "output"
                                            },
                                            "url": "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/c3f9ce6a-6f78-4c77-b35a-8ad410037961/202501.mp4?OSSAccessKeyId=LTAI5tHTjmHLrqrjGGfezHGU&Expires=2051582323&Signature=%2FIXIHXFLgq12f7Kkz2DNDHl7YeE%3D"
                                        },
                                        {
                                            "name": "output-with-audio.mp4",
                                            "urn": "artifact://7d9ef228-a253-407e-94fc-f9fb4113a2ac",
                                            "meta": {
                                                "type": "output-with-audio"
                                            },
                                            "url": "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/7d9ef228-a253-407e-94fc-f9fb4113a2ac/202501.mp4?OSSAccessKeyId=LTAI5tHTjmHLrqrjGGfezHGU&Expires=2051582324&Signature=Fv%2FMGVwpai%2Bt9rYxnTiLvOeZXFE%3D"
                                        }
                                    ],
                                    "created_at": "2025-01-07T03:56:43.072441Z",
                                    "group": "default",
                                    "state": {
                                        "job_duration": 126.90443682670593,
                                        "receptionist_task_id": "329-e85",
                                        "quant_type": "none"
                                    },
                                    "updated_at": "2025-01-07T03:56:43.072460Z"
                                },
                                "pipeline_id": 60,
                                "pipeline_step_index": 0,
                                "pipeline_request_id": "631135439000005"
                            },
                            "timestamp": "2025-01-07T03:58:51.059690"
                        }
            """;

        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        InferEvent event = mapper.readValue(jsonMessage, InferEvent.class);

        JobStatusUpdated jobStatusUpdated = event.parsePayload(JobStatusUpdated.class);

        System.out.println(jobStatusUpdated);
        System.out.println(jobStatusUpdated.getPipelineRequestId());
        System.out.println(jobStatusUpdated.getPipelineStepIndex());
    }
}
