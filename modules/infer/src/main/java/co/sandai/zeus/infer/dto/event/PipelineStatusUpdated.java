package co.sandai.zeus.infer.dto.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PipelineStatusUpdated {
    @JsonProperty("job_id")
    private Long jobId;

    private String group;
    private Output output;

    @JsonProperty("output_index")
    private Integer outputIndex;

    @JsonProperty("pipeline_id")
    private Long pipelineId;

    @JsonProperty("pipeline_step_index")
    private Integer pipelineStepIndex;

    private String timestamp;

    @JsonProperty("pipeline_request_id")
    private Long pipelineRequestId;

    @JsonProperty("request_id")
    private Long requestId;

    @Data
    public static class Output {
        private String name;
        private String urn;
        private Meta meta;
        private String url;

        @JsonProperty("oss_bucket")
        private String ossBucket;

        @JsonProperty("oss_object")
        private String ossObject;

        @JsonProperty("oss_endpoint")
        private String ossEndpoint;

        // getters 和 setters...
    }

    @Data
    public static class Meta {
        private Double duration;
        private String type;

        // getters 和 setters...
    }
}
