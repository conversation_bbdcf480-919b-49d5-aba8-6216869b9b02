package co.sandai.zeus.infer.service;

import co.sandai.zeus.common.helper.FilePathHelper;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.asset.service.AssetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class BailianService {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AssetService assetService;

    public String generateImage(String prompt) {
        // TODO: 先用 silicon flow 的 API，看情况要不要用百炼的
        return "";
    }

    public Asset generateImageAndSaveAsAsset(String prompt, long userId, long orgId) {
        String url = generateImage(prompt);
        String ext = FilePathHelper.getFileExtName(url);
        return assetService.addAssetByWebUrl(url, userId, orgId, ext, AssetSource.Generate);
    }
}
