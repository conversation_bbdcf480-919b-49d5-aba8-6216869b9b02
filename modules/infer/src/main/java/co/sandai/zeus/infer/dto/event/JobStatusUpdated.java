package co.sandai.zeus.infer.dto.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class JobStatusUpdated {
    // @JsonProperty("notification_type")
    // private String notificationType;
    // @JsonProperty("notification_id")
    // private Long notificationId;
    // private Payload payload;
    // private String timestamp;

    // @Data
    // public static class Payload {
    //     private Job job;
    // }

    private Job job;

    @JsonProperty("pipeline_request_id")
    private Long pipelineRequestId;

    @JsonProperty("pipeline_id")
    private Long pipelineId;

    @JsonProperty("pipeline_step_index")
    private Integer pipelineStepIndex;

    @Data
    public static class Job {
        private String prompt;

        @JsonProperty("model_version")
        private String modelVersion;

        private String status;

        @JsonProperty("project_name")
        private String projectName;

        private String queue;
        private Integer progress;

        //        @JsonProperty("pipeline_id")
        //        private Long pipelineId;

        private Integer priority;
        private String deadline;

        @JsonProperty("pipeline_position")
        private Integer pipelinePosition;

        @JsonProperty("streaming_key")
        private String streamingKey;

        @JsonProperty("started_at")
        private String startedAt;

        private Long id;
        private String type;
        private List<Input> inputs;

        @JsonProperty("completed_at")
        private String completedAt;

        @JsonProperty("request_id")
        private Long requestId;

        private Options options;

        @JsonProperty("retrieved_at")
        private String retrievedAt;

        private String model;
        private List<Output> outputs;

        @JsonProperty("created_at")
        private String createdAt;

        private String group;
        private State state;

        @JsonProperty("updated_at")
        private String updatedAt;

        private List<Step> steps;

        @Data
        public static class Step {
            float duration;
            String prompt;

            @JsonProperty("enhanced_prompt")
            String enhancedPrompt;
        }
    }

    @Data
    public static class Input {
        private String name;
        private String urn;
        private String url;
    }

    @Data
    public static class Output {
        private String name;
        private String urn;
        private Meta meta;
        private String url;
    }

    @Data
    public static class Meta {
        private Double duration;
        private String type;
    }

    @Data
    public static class Options {
        @JsonProperty("tSchedulerFunc")
        private String tSchedulerFunc;

        @JsonProperty("tSchedulerFuncArgs")
        private String tSchedulerFuncArgs;

        private Long seed;
        private String aspectRatio;
        private List<String> specialTokens;
        private String vaeModel;
        private Integer duration;
    }

    @Data
    public static class State {
        @JsonProperty("job_duration")
        private Double jobDuration;

        @JsonProperty("receptionist_task_id")
        private String receptionistTaskId;

        @JsonProperty("quant_type")
        private String quantType;
    }
}
