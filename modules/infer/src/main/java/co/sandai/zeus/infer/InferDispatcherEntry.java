package co.sandai.zeus.infer;

import static co.sandai.zeus.domain.asset.dao.AssetSource.ExtractVideoPoster;

import co.sandai.zeus.common.log.trace.TraceContext;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetMapper;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.task.dao.PipelineTask;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskChunkDO;
import co.sandai.zeus.domain.task.dao.TaskChunkStatus;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.dao.mapper.TaskChunkMapper;
import co.sandai.zeus.domain.task.dao.mapper.TaskMapper;
import co.sandai.zeus.domain.task.service.PipelineService;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.infer.dto.InferGroupRegistryItem;
import co.sandai.zeus.infer.dto.event.InferEvent;
import co.sandai.zeus.infer.dto.event.JobOutputsUpdated;
import co.sandai.zeus.infer.dto.event.JobStatusUpdated;
import co.sandai.zeus.infer.proto.task.TaskInferResultOuterClass;
import co.sandai.zeus.infer.service.InferDispatcherService;
import co.sandai.zeus.infer.utils.HostnameUtil;
import co.sandai.zeus.infer.worker.InferResultCollector;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.infer.ArtifactClient;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.time.Duration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.SmartLifecycle;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.connection.stream.Consumer;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.ReadOffset;
import org.springframework.data.redis.connection.stream.StreamOffset;
import org.springframework.data.redis.connection.stream.StreamRecords;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.stream.StreamMessageListenerContainer;
import org.springframework.data.redis.stream.StreamMessageListenerContainer.StreamMessageListenerContainerOptions;
import org.springframework.data.redis.stream.Subscription;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
@Slf4j
public class InferDispatcherEntry implements SmartLifecycle {
    @Value("${zeus.infer.dispatcher.enabled:false}")
    private boolean dispatcherEnabled;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private InferDispatcherService inferDispatcherService;

    @Autowired
    private JedisConnectionFactory jedisConnectionFactory;

    @Autowired
    private AssetService assetService;

    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private AssetMapper assetMapper;

    @Autowired
    private IDGenerator idGenerator;

    @Value("${zeus.env}")
    private String env;

    private StreamMessageListenerContainer<String, MapRecord<String, Object, TaskInferResultOuterClass.TaskInferResult>>
            streamContainer = null;

    private InferResultCollector collector = null;

    private final Map<String, Subscription> subscriptions = new HashMap<>();

    @Autowired
    private TaskChunkMapper taskChunkMapper;

    @Autowired
    private ArtifactClient artifactClient;

    @Autowired
    private RestTemplate restTemplate;

    @Scheduled(fixedRate = 5000)
    public void inferServerRegistryMonitor() {
        if (!dispatcherEnabled) {
            return;
        }
        List<InferGroupRegistryItem> items = inferDispatcherService.getRegisteredInferGroups();
        Set<String> groupNameSet = new HashSet<>();
        for (InferGroupRegistryItem item : items) {
            groupNameSet.add(item.getGroupName());
            if (subscriptions.containsKey(item.getGroupName())) {
                Subscription subscription = subscriptions.get(item.getGroupName());
                if (subscription.isActive()) {
                    continue;
                }
                log.warn(
                        "subscription is not alive, workId: {}, groupName: {}",
                        idGenerator.getIdGenWorkerId(),
                        item.getGroupName());
                subscriptions.remove(item.getGroupName());
            }
            // TODO: 通过 xpending 处理超时的消息
            String streamKey = item.getGroupOutputStreamName();
            String consumerName = String.format("zeus-infer-result-collector-%d", idGenerator.getIdGenWorkerId());
            Consumer consumer = item.createConsumer(redisTemplate, consumerName);
            Subscription sub = streamContainer.receive(
                    consumer, StreamOffset.create(streamKey, ReadOffset.lastConsumed()), message -> {
                        this.collector = InferResultCollector.builder()
                                .message(message)
                                .streamName(streamKey)
                                .streamGroupName(consumer.getGroup())
                                .taskService(taskService)
                                .redisTemplate(redisTemplate)
                                .build();
                        this.collector.start();
                    });

            subscriptions.put(item.getGroupName(), sub);
            log.info(
                    "add stream subscription for server group: {}, stream key: {}",
                    item.getGroupName(),
                    item.getGroupOutputStreamName());
        }

        Iterator<Map.Entry<String, Subscription>> iterator =
                subscriptions.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Subscription> entry = iterator.next();
            String groupName = entry.getKey();
            if (groupNameSet.contains(groupName)) {
                continue;
            }
            Subscription sub = subscriptions.get(groupName);
            streamContainer.remove(sub);
            iterator.remove();
            log.info("remove stream subscription for server group: {}", groupName);
        }

        if (!streamContainer.isRunning()) {
            log.info("starting stream listener container...");
        }
    }

    @Override
    public void start() {

        if (!dispatcherEnabled) {
            log.info("infer dispatcher disabled");
            return;
        }
        setupStreamListener();
        log.info("starting infer dispatcher...");
        // 新版推理

        // 初始化推理结果 stream 监听容器 start
        StreamMessageListenerContainer.StreamMessageListenerContainerOptions<
                        String, MapRecord<String, Object, TaskInferResultOuterClass.TaskInferResult>>
                options = StreamMessageListenerContainer.StreamMessageListenerContainerOptions.builder()
                        .pollTimeout(Duration.ofSeconds(10))
                        .keySerializer(new StringRedisSerializer())
                        .hashKeySerializer(new StringRedisSerializer())
                        .hashValueSerializer(
                                new RedisProtobufSerializer<>(TaskInferResultOuterClass.TaskInferResult.class))
                        .build();
        streamContainer = StreamMessageListenerContainer.create(jedisConnectionFactory, options);
        streamContainer.start();

        // 初始化推理结果 stream 监听容器 end, 后续以删除了
        log.info("infer dispatcher started.");
    }

    @Override
    public void stop() {
        log.info("stopping infer dispatcher...");
        streamContainer.stop();
        log.info("stopped infer dispatcher.");
    }

    @Override
    public boolean isRunning() {
        boolean isCollectorRunning = Objects.nonNull(collector) && collector.isAlive();
        boolean isStreamContainerRunning = Objects.nonNull(streamContainer) && streamContainer.isRunning();
        boolean running = isStreamContainerRunning || isCollectorRunning;
        log.info("Infer dispatcher running: {}", running);
        return running;
    }

    private void setupStreamListener() {
        log.info("Setting up stream listener...");

        String streamKey = "zeus-infer-events";
        String groupName;
        if (!HostnameUtil.isRunningInK8s()) {
            // 本地运行时，使用 hostname 作为 group name，方便调试，并且从头开始消费
            groupName = String.format("zeus-infer-events-group-local-%s", HostnameUtil.getHostname());
        } else {
            groupName = "zeus-infer-events-group";
        }
        String consumerName = String.format("zeus-worker-%s", HostnameUtil.getHostname());

        log.info(
                "Configuring stream listener with key: {}, group: {}, consumer: {}",
                streamKey,
                groupName,
                consumerName);

        boolean isNewGroup = false;
        // 确保 stream 和 consumer group 存在
        try {
            redisTemplate.opsForStream().createGroup(streamKey, groupName);
            isNewGroup = true;
            log.info("Created new consumer group: {} for stream: {}", groupName, streamKey);
        } catch (Exception e) {
            if (e.getMessage() != null && e.getMessage().contains("BUSYGROUP")) {
                log.info("Consumer group already exists: {} for stream: {}", groupName, streamKey);
            } else if (e.getMessage() != null && e.getMessage().contains("ERR no such key")) {
                // Stream doesn't exist, create it with an empty record and then create the
                // group
                redisTemplate
                        .opsForStream()
                        .add(StreamRecords.newRecord().in(streamKey).ofObject(new HashMap<String, String>() {
                            {
                                put("init", "true");
                            }
                        }));
                redisTemplate.opsForStream().createGroup(streamKey, groupName);
                isNewGroup = true;
                log.info("Created new stream: {} and consumer group: {}", streamKey, groupName);
            } else {
                log.error("Error creating consumer group", e);
                throw e;
            }
        }

        // 配置容器选项
        StreamMessageListenerContainerOptions<String, MapRecord<String, Object, String>> containerOptions =
                StreamMessageListenerContainerOptions.builder()
                        .pollTimeout(Duration.ofMillis(100))
                        .serializer(new StringRedisSerializer())
                        .hashKeySerializer(new StringRedisSerializer())
                        // .hashValueSerializer(createJsonSerializer(InferEvent.class))
                        .hashValueSerializer((new StringRedisSerializer()))
                        .build();

        // 创建监听容器
        StreamMessageListenerContainer<String, MapRecord<String, Object, String>> container =
                StreamMessageListenerContainer.create(jedisConnectionFactory, containerOptions);

        log.info("Stream listener container created");

        ObjectMapper mapper = createObjectMapper();

        // 根据是否是新创建的组来决定使用哪个 ReadOffset
        // ReadOffset readOffset = isNewGroup ? ReadOffset.lastConsumed() :
        // ReadOffset.latest();
        container.receive(
                Consumer.from(groupName, consumerName),
                StreamOffset.create(streamKey, ReadOffset.lastConsumed()),
                message -> {
                    TraceContext.switchTraceId(); // 处理每个任务单独使用一个trace_id
                    try {

                        String jsonMessage = message.getValue().get("data").toString();
                        InferEvent event = mapper.readValue(jsonMessage, InferEvent.class);
                        processInferEvent(event);
                        // Acknowledge the message after successful processing
                        redisTemplate.opsForStream().acknowledge(streamKey, groupName, message.getId());
                        redisTemplate.opsForStream().delete(streamKey, message.getId());
                        log.info("Message {} processed and acknowledged", message.getId());

                    } catch (Exception e) {
                        log.error("Error processing message: {}", e.getMessage(), e);
                    }
                });

        // TODO 需要启动一个线程能够监控或者处理pending消息

        // // 处理可能的 pending 消息
        // try {
        // PendingMessages pending = redisTemplate.opsForStream()
        // .pending(streamKey, groupName, Range.unbounded(), 100);
        // if (pending != null && !pending.isEmpty()) {
        // log.info("Found {} pending messages", pending.size());
        // // for (PendingMessage pm : pending) {
        // // redisTemplate.opsForStream()
        // // .claim(streamKey, groupName, consumerName, Duration.ofMinutes(1),
        // pm.getIdAsString());
        // // }
        // }
        // } catch (Exception e) {
        // log.warn("Error checking pending messages: {}", e.getMessage());
        // }

        // 启动容器
        container.start();
        log.info("Stream listener started successfully");
    }

    public void processInferEvent(InferEvent event) {
        log.info("Message received: {}", event);
        switch (event.getNotificationType()) {
            case "JOB_OUTPUTS_UPDATED": // 这个是最终状态
                JobOutputsUpdated payload = event.parsePayload(JobOutputsUpdated.class);
                handleJobOutputUpdateEvent(payload);
                break;
            case "JOB_STATUS_UPDATED": {
                JobStatusUpdated jobStatusUpdated = event.parsePayload(JobStatusUpdated.class);
                handleJobStatusUpdatedEvent(jobStatusUpdated);
                break;
            }

            default:
                // log.warn("Unknown notification type: {}", event.getNotificationType());
        }
    }

    private void handleJobOutputUpdateEvent(JobOutputsUpdated event) {
        JobOutputsUpdated.Output output = event.getOutput();
        long taskId = event.getRequestId();
        Task task = taskService.getTaskById(taskId);
        if (task == null) {
            log.warn("handle job output update event, task not found for request id: {}", taskId);
            return;
        }
        if (task.getStatus() == TaskStatus.Canceled) {
            log.info("Task {} is already canceled, skipping status update", taskId);
            return;
        }

        List<TaskChunkDO> taskChunks = taskChunkMapper.getByTaskId(taskId);
        Long stepId = null;
        JobOutputsUpdated.Meta meta = output.getMeta();
        if (taskChunks != null && !taskChunks.isEmpty() && meta.getStepIdx() != null) {
            TaskChunkDO steps = taskChunks.get(meta.getStepIdx());
            stepId = steps.getId();
        }
        Asset asset;
        // Get duration from meta, defaulting to 0 if null
        float duration = meta.getDuration() != null ? meta.getDuration().floatValue() : 0f;
        int width = meta.getWidth() != null ? meta.getWidth() : 0;
        int height = meta.getHeight() != null ? meta.getHeight() : 0;
        boolean watermarked = meta.getWatermarked() != null ? meta.getWatermarked() : false;

        switch (meta.getType()) {
            case "input_video_segment": // 用于处理从 MP4 转换为 TS 的输入文件
                {
                    String sourceMp4Urn = meta.getSourceUrn();
                    // 使用新的专用方法处理从MP4转换的TS文件，传递水印信息
                    asset = assetService.addConvertedTsFromMp4(
                            output.getOssBucket(),
                            output.getOssObject(),
                            output.getOssEndpoint(),
                            output.getUrn(),
                            task.getUserId(),
                            task.getId(),
                            width,
                            height,
                            sourceMp4Urn, // 传递源MP4的URN
                            duration,
                            watermarked); // 传递水印状态
                }
                break;

            case "segment": // task和step两种级别
                assetService.addInferOutput(
                        output.getOssBucket(),
                        output.getOssObject(),
                        output.getOssEndpoint(),
                        output.getUrn(),
                        task.getUserId(),
                        task.getOrgId(),
                        taskId,
                        stepId,
                        meta.getChunkIdx(),
                        width,
                        height,
                        "ts",
                        AssetSource.Generate,
                        duration,
                        watermarked); // 传递水印状态
                taskMapper.updateTaskInferChunkResult(taskId, meta.getChunkIdx() + 1);
                if (stepId != null && (meta.getChunkIdx() + 1) == meta.getChunkNum()) {
                    taskChunkMapper.updateStatus(stepId, TaskChunkStatus.Success);
                }
                break;
            case "output-with-audio", "output": // 仅有task级别
                if (stepId != null) {
                    break;
                }
                asset = assetService.addInferOutput(
                        output.getOssBucket(),
                        output.getOssObject(),
                        output.getOssEndpoint(),
                        output.getUrn(),
                        task.getUserId(),
                        task.getOrgId(),
                        taskId,
                        null,
                        0,
                        width,
                        height,
                        "mp4",
                        AssetSource.Generate,
                        duration,
                        watermarked); // 传递水印状态
                // output-with-audio 在 output
                // 生成且可能失败。如果有output-with-audio，则使用output-with-audio，没有则使用output
                String type = meta.getType();
                if ((type.equals("output") && task.getResultVideoId() == 0) || type.equals("output-with-audio")) {
                    log.info(
                            "Updating task result video id for task {}, type: {}, asset id: {}",
                            taskId,
                            type,
                            asset.getId());
                    taskMapper.updateTaskResultVideoId(taskId, asset.getId());
                    // 提及结果审核
                    assetService.moderateVideoAsset(asset);
                } else {
                    log.debug(
                            "Skip updating task result video id for task {}, type: {}, existing result video id: {}",
                            taskId,
                            type,
                            task.getResultVideoId());
                }

                if (task.getResultVideoPosterId() != 0) {
                    assetMapper.updateAssetPosterById(asset.getId(), task.getResultVideoPosterId());
                }

                break;
            case "thumbnail": // TODO task和step两种级别都有？
                asset = assetService.addInferOutput(
                        output.getOssBucket(),
                        output.getOssObject(),
                        output.getOssEndpoint(),
                        output.getUrn(),
                        task.getUserId(),
                        task.getOrgId(),
                        taskId,
                        null,
                        0,
                        width,
                        height,
                        "jpeg",
                        ExtractVideoPoster,
                        0,
                        watermarked); // 传递水印状态
                taskMapper.updateTaskResultInfo(
                        taskId,
                        asset.getId(),
                        task.getWidth(),
                        task.getHeight(),
                        event.getModelVersion(),
                        event.getSeed());
                // task.setResultVideoPosterId(asset.getId());
                break;

            default:
                log.warn("Unknown output type: {}", meta.getType());
        }
    }

    private void handleJobStatusUpdatedEvent(JobStatusUpdated event) {
        JobStatusUpdated.Job job = event.getJob();
        long taskId = job.getRequestId();
        Task task = taskService.getTaskById(taskId);
        if (task == null) {
            log.warn("Task not found for request id: {}", taskId);
            return;
        }
        if (task.getStatus() == TaskStatus.Canceled) {
            log.info("Task {} is already canceled, skipping status update", taskId);
            return;
        }

        TaskChunkStatus newChunkStatus;
        TaskStatus newTaskStatus;
        switch (event.getJob().getStatus().toLowerCase()) {
            case "pending":
                // pending 默认状态不需要任何处理
                return;

            case "running":
                newChunkStatus = TaskChunkStatus.Running;
                newTaskStatus = TaskStatus.Running;

                if (event.getJob().getSteps() != null
                        && !event.getJob().getSteps().isEmpty()) {
                    List<JobStatusUpdated.Job.Step> steps = event.getJob().getSteps();
                    if (steps.size() == 1) {
                        taskMapper.updateEnhancedPrompt(taskId, steps.getFirst().getEnhancedPrompt());
                    } else {
                        List<TaskChunkDO> taskChunks = taskChunkMapper.getByTaskId(taskId);
                        if (taskChunks != null && !taskChunks.isEmpty()) {
                            for (int i = 0; i < steps.size(); i++) {
                                TaskChunkDO chunk = taskChunks.get(i);
                                JobStatusUpdated.Job.Step step = steps.get(i);
                                if (StringUtils.isEmpty(step.getEnhancedPrompt())) {
                                    log.info("Enhanced prompt is empty for step {}", i);
                                    continue;
                                }
                                log.info(
                                        "Updating chunk {} enhanced_prompt to {} for task {}",
                                        chunk.getId(),
                                        step.getEnhancedPrompt(),
                                        taskId);

                                taskChunkMapper.updateEnhancedPrompt(chunk.getId(), step.getEnhancedPrompt());
                            }
                        }
                    }
                }

                break;

            case "succeeded":
                newChunkStatus = TaskChunkStatus.Success;
                newTaskStatus = TaskStatus.Success;
                break;

            case "failed", "expired":
                newChunkStatus = TaskChunkStatus.Fail;
                newTaskStatus = TaskStatus.Fail;
                break;

            case "cancelled":
                newChunkStatus = TaskChunkStatus.Canceled;
                newTaskStatus = TaskStatus.Canceled;
                break;

            default:
                log.warn("Unknown job status: {} for task {}", event.getJob().getStatus(), taskId);
                return;
        }

        // 获取对应的 task chunk
        List<TaskChunkDO> taskChunks = taskChunkMapper.getByTaskId(taskId);
        if (taskChunks != null || !taskChunks.isEmpty()) {
            // Update all task chunks
            for (TaskChunkDO chunk : taskChunks) {
                log.info("Updating chunk {} status to {} for task {}", chunk.getId(), newChunkStatus, taskId);
                taskChunkMapper.updateStatus(chunk.getId(), newChunkStatus);
            }
        }

        // Update task status
        taskService.updateTaskStatus(taskId, newTaskStatus);

        // Process next task in pipeline or mark tasks as failed
        if (newTaskStatus == TaskStatus.Success || newTaskStatus == TaskStatus.Fail) {
            Task currentTask = taskService.getTaskById(taskId);

            // First check if this task is part of a pipeline
            PipelineTask pipelineTask = pipelineService.getPipelineTaskByTaskId(taskId);
            if (pipelineTask != null) {
                try {
                    // This will process next task or mark subsequent tasks as failed based on
                    // current task status
                    // TODO 使用异步提交
                    pipelineService.processNextTaskInPipeline(currentTask, pipelineTask);
                    log.info("Processed pipeline task: {} with status: {}", taskId, newTaskStatus);
                } catch (Exception e) {
                    log.error("Failed to process pipeline tasks for task {} with status {}", taskId, newTaskStatus, e);
                }
            } else {
                log.debug("Task {} is not part of a pipeline, skipping pipeline processing", taskId);
            }
        }
        log.info("Updated status to {} for task {} and {} for all its chunks", newTaskStatus, taskId, newChunkStatus);
    }

    private ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return mapper;
    }
}
