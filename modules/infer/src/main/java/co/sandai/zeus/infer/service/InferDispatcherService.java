package co.sandai.zeus.infer.service;

import co.sandai.zeus.infer.dto.InferGroupRegistryItem;
import co.sandai.zeus.infer.proto.task.TaskInferResultOuterClass;
import com.google.protobuf.ByteString;
import java.time.Duration;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import redis.clients.jedis.Jedis;

@Slf4j
@Service
public class InferDispatcherService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RestTemplate restTemplate;

    public List<InferGroupRegistryItem> getRegisteredInferGroups() {
        String prefix = "INFER_GROUP_REGISTRY";
        List<InferGroupRegistryItem> results = new ArrayList<>();
        Set<String> keys = redisTemplate.keys(prefix + ".*");
        if (keys == null) {
            return results;
        }

        for (String key : keys) {
            String serverCount = (String) redisTemplate.opsForValue().get(key);
            if (serverCount == null) {
                continue;
            }

            InferGroupRegistryItem item = InferGroupRegistryItem.builder()
                    .groupName(key.substring(prefix.length() + 1))
                    .serverCount(Integer.parseInt(serverCount))
                    .build();
            results.add(item);
        }
        return results;
    }

    public List<String> getInferGroupInputChannels(String groupName) {
        return redisTemplate.execute(
                connection -> {
                    Jedis conn = (Jedis) connection.getNativeConnection();
                    return conn.pubsubChannels(groupName + ".server?.input");
                },
                true);
    }

    public InferGroupRegistryItem getAvailableInferGroup() {
        return this.getAvailableInferGroup(true);
    }

    public InferGroupRegistryItem getAvailableInferGroup(boolean wait) {
        // TODO: delay & wait 改成配置
        long delayTime = Duration.ofSeconds(1).toMillis();
        long waitTimeout = Duration.ofSeconds(30).toMillis();
        long startTime = System.currentTimeMillis();

        InferGroupRegistryItem targetItem = null;

        //noinspection LoopConditionNotUpdatedInsideLoop
        do {
            List<InferGroupRegistryItem> registryItems = getRegisteredInferGroups();
            if (registryItems.isEmpty()) {
                break;
            }

            for (InferGroupRegistryItem item : registryItems) {
                List<String> inputChannels = getInferGroupInputChannels(item.getGroupName());
                if (inputChannels.isEmpty()) {
                    continue;
                }
                if (item.isAvailable(inputChannels)) {
                    targetItem = item;
                }
            }

            if (targetItem == null) {
                return null;
            }

            Boolean ok = redisTemplate
                    .opsForValue()
                    .setIfAbsent(targetItem.getGroupLockName(), "busy", Duration.ofSeconds(10));
            if (Boolean.TRUE.equals(ok)) {
                log.info("get lock for group: {}", targetItem.getGroupName());
                return targetItem;
            }

            long currentTime = System.currentTimeMillis();
            if (currentTime - startTime > waitTimeout) {
                log.info("get lock for group: {} timeout, wait timeout: {}ms", targetItem.getGroupName(), waitTimeout);
                break;
            }
            try {
                log.info("get lock for group: {} failed, wait {}ms to retry", targetItem.getGroupName(), delayTime);
                Thread.sleep(delayTime);
            } catch (InterruptedException e) {
                break;
            }
        } while (wait);
        return targetItem;
    }

    public void mockReportInferResult(long taskId, int idx) {
        // String m3u8File =
        // "https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/tears-of-steel-audio_eng=128002-video_eng=2200000.m3u8";
        // String testTsFile =
        // "https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/tears-of-steel-audio_eng=128002-video_eng=2200000-1.ts";
        String testTsFile =
                "https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/tears-of-steel-audio_eng=128002-video_eng=2200000-"
                        + (idx + 4) + ".ts";
        log.info("download url: {}", testTsFile);
        byte[] tsBytes = restTemplate.getForObject(testTsFile, byte[].class);

        if (tsBytes == null) {
            tsBytes = new byte[0];
        }

        byte[] finalTsBytes = tsBytes;
        TaskInferResultOuterClass.TaskInferResult result = TaskInferResultOuterClass.TaskInferResult.newBuilder()
                .setTaskId(taskId)
                .setChunk(ByteString.copyFrom(finalTsBytes))
                .setChunkIndex(idx)
                .build();

        Map<byte[], byte[]> m = new HashMap<>();
        m.put("data".getBytes(), result.toByteArray());

        redisTemplate.execute(connection -> connection.xAdd("group0.output".getBytes(), m), true);
    }
}
