package co.sandai.zeus.infer.dto.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class JobOutputsUpdated {
    @JsonProperty("job_id")
    private Long jobId;

    private String group;
    private Output output;

    @JsonProperty("model_version")
    private String modelVersion;

    @JsonProperty("seed")
    private Long seed;

    private String timestamp;

    @JsonProperty("request_id")
    private Long requestId;

    @Data
    public static class Output {
        private String name;
        private String urn;
        private Meta meta;
        private String url;

        @JsonProperty("oss_bucket")
        private String ossBucket;

        @JsonProperty("oss_object")
        private String ossObject;

        @JsonProperty("oss_endpoint")
        private String ossEndpoint;
    }

    @Data
    public static class Meta {
        private Double duration;
        private String type;

        @JsonProperty("step_idx")
        private Integer stepIdx;

        @JsonProperty("chunk_num")
        private Integer chunkNum;

        @JsonProperty("chunk_idx")
        private Integer chunkIdx;

        private Integer width;

        private Integer height;

        @JsonProperty("source_urn")
        private String sourceUrn;

        private Boolean watermarked;
    }
}
