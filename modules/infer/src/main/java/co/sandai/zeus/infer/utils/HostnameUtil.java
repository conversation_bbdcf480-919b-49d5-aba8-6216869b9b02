package co.sandai.zeus.infer.utils;

import java.net.InetAddress;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HostnameUtil {

    /**
     * Get the hostname of the current machine.
     * Priority:
     * 1. K8s pod name (HOSTNAME env variable)
     * 2. Local hostname
     * 3. Local IP address
     * 4. Fallback to default name with timestamp
     *
     * @return hostname string
     */
    public static String getHostname() {
        // Try K8s pod name
        String podName = System.getenv("HOSTNAME");
        if (podName != null && !podName.isEmpty()) {
            return podName;
        }

        try {
            // Try local hostname
            String hostname = InetAddress.getLocalHost().getHostName();
            if (hostname != null && !hostname.isEmpty()) {
                return hostname;
            }

            // Try IP address
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            if (hostAddress != null && !hostAddress.isEmpty()) {
                return hostAddress;
            }
        } catch (Exception e) {
            log.warn("Failed to get hostname or IP address", e);
        }

        // Fallback
        return String.format("unknown-host-%d", System.currentTimeMillis());
    }

    /**
     * Check if the application is running inside a Kubernetes container
     * by checking the existence of specific environment variables commonly
     * present in K8s environments.
     *
     * @return true if running in K8s container, false otherwise
     */
    public static boolean isRunningInK8s() {
        String podName = System.getenv("HOSTNAME");
        String k8sPort = System.getenv("KUBERNETES_PORT");
        String k8sService = System.getenv("KUBERNETES_SERVICE_HOST");

        return (podName != null && !podName.isEmpty()) && (k8sPort != null || k8sService != null);
    }
}
