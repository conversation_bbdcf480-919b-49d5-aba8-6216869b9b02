package co.sandai.zeus.infer.dto.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Data;

@Data
public class InferEvent {
    @JsonProperty("notification_type")
    private String notificationType;

    @JsonProperty("notification_id")
    private Long notificationId;

    @JsonProperty("payload")
    private JsonNode payload;

    @JsonProperty("timestamp")
    private String timestamp;

    // getters and setters

    public <T> T parsePayload(Class<T> clazz) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //        InferEvent event = mapper.readValue(jsonMessage, InferEvent.class);

        //        ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(payload, clazz);
    }
}
