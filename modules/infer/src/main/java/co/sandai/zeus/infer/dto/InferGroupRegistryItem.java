package co.sandai.zeus.infer.dto;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.IntStream;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.Consumer;
import org.springframework.data.redis.connection.stream.ReadOffset;
import org.springframework.data.redis.connection.stream.StreamInfo;
import org.springframework.data.redis.core.RedisTemplate;

@Getter
@Setter
@Builder
@Slf4j
public class InferGroupRegistryItem {
    private String groupName;
    private int serverCount;

    public String getGroupLockName() {
        return String.format("%s.lock", groupName);
    }

    public String getGroupOutputStreamName() {
        return String.format("%s.output", groupName);
    }

    public String getInferGroupInputChannelName(int serverIndex) {
        return String.format("%s.server%d.input", groupName, serverIndex);
    }

    public List<String> expectedInputChannels() {
        return IntStream.range(0, this.getServerCount())
                .mapToObj(this::getInferGroupInputChannelName)
                .toList();
    }

    public boolean isAvailable(List<String> realInputChannels) {
        return realInputChannels.size() == this.getServerCount()
                && new HashSet<>(realInputChannels).containsAll(expectedInputChannels());
    }

    public Consumer createConsumer(RedisTemplate<String, Object> redisTemplate, String consumerName) {
        String streamName = getGroupOutputStreamName();
        Set<String> keys = redisTemplate.keys(streamName);
        boolean groupExisted = false;
        if (keys != null && !keys.isEmpty()) {
            StreamInfo.XInfoGroups groups = redisTemplate.opsForStream().groups(getGroupOutputStreamName());
            groupExisted = groups.stream().anyMatch(g -> g.groupName().equals(getGroupName()));
        }
        if (!groupExisted) {
            redisTemplate.opsForStream().createGroup(streamName, ReadOffset.latest(), getGroupName());
            log.info("Created consumer group: {}, add consumer: {}", getGroupName(), consumerName);
        }
        return Consumer.from(getGroupName(), consumerName);
    }

    @Override
    public String toString() {
        return "InferGroupRegistryItem{" + "groupName='" + groupName + '\'' + ", serverCount=" + serverCount + '}';
    }
}
