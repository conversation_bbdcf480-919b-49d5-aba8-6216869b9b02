package co.sandai.zeus.infer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class PipelineView {
    private Integer id;
    private String group;
    private String prompt;

    @JsonProperty("project_name")
    private String projectName;

    private String type;

    @JsonProperty("request_id")
    private String requestId;

    private String model;

    @JsonProperty("model_version")
    private String modelVersion;

    private String queue;
    private Integer priority;

    @JsonProperty("streaming_key")
    private String streamingKey;

    private List<ArtifactInputReal> inputs;
    private Options options;
    private List<ArtifactInputReal> outputs;
    private Map<String, Object> state;
    private String status;
    private Float progress;
    private Instant deadline;

    @JsonProperty("started_at")
    private Instant startedAt;

    @JsonProperty("completed_at")
    private Instant completedAt;

    @JsonProperty("retrieved_at")
    private Instant retrievedAt;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    private List<StepView> steps;

    @Data
    public static class StepView {
        private String prompt;
        private Integer duration;

        @JsonProperty("job_id")
        private Integer jobId;

        private String status;
        private Float progress;
        private List<ArtifactInputReal> outputs;
    }

    @Data
    public static class ArtifactInputReal {
        private String name;
        private String urn;
        private String url;
        private Map<String, Object> meta;
    }

    @Data
    @lombok.EqualsAndHashCode(callSuper = true)
    public static class RichArtifactInputReal extends ArtifactInputReal {
        @JsonProperty("oss_bucket")
        private String ossBucket;

        @JsonProperty("oss_object")
        private String ossObject;

        @JsonProperty("oss_endpoint")
        private String ossEndpoint;
    }

    @Data
    public static class Options {
        @JsonProperty("tSchedulerFunc")
        private String tSchedulerFunc;

        @JsonProperty("tSchedulerFuncArgs")
        private String tSchedulerFuncArgs;

        private Integer seed;
        private String aspectRatio;
        private List<String> specialTokens;
        private String vaeModel;
        private Integer duration;
    }

    public enum PipelineStatus {
        PENDING("pending"),
        RUNNING("running"),
        PAUSED("paused"),
        SUCCEEDED("succeeded"),
        FAILED("failed"),
        CANCELLED("cancelled");

        private final String value;

        PipelineStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static PipelineStatus fromValue(String value) {
            for (PipelineStatus status : PipelineStatus.values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status: " + value);
        }
    }
}
