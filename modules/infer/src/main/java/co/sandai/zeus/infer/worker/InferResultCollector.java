package co.sandai.zeus.infer.worker;

import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.infer.proto.task.TaskInferResultOuterClass;
import java.time.Duration;
import java.util.Objects;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.core.RedisTemplate;

@Slf4j
@Builder
public class InferResultCollector extends Thread {

    private String streamName;
    private String streamGroupName;
    private TaskService taskService;
    private MapRecord<String, Object, TaskInferResultOuterClass.TaskInferResult> message;
    private RedisTemplate<String, Object> redisTemplate;

    public void run() {
        try {
            TaskInferResultOuterClass.TaskInferResult result =
                    message.getValue().get("data");

            if (Objects.isNull(result)) {
                throw new RuntimeException("TaskInferResult is null, invalid message format");
            }

            long taskId = result.getTaskId();
            TaskInferResultOuterClass.TaskInferStatus status = result.getStatus();

            if (status == TaskInferResultOuterClass.TaskInferStatus.Failed) {
                taskService.updateTaskStatus(taskId, TaskStatus.Fail);
                log.info("receive infer result, task id: {}, task status: failed", taskId);
                return;
            }

            if (status == TaskInferResultOuterClass.TaskInferStatus.Canceled) {
                taskService.updateTaskStatus(taskId, TaskStatus.Canceled);
                log.info("receive infer result, task id: {}, task status: canceled", taskId);
                return;
            }

            long currentTime = System.currentTimeMillis();
            long completeTime = result.getChunkCompleteTime();
            Duration latency = Duration.ofMillis(completeTime - currentTime);

            long index = result.getChunkIndex();
            long seed = result.getSeed();
            int width = result.getWidth();
            int height = result.getHeight();
            long chunkCount = result.getTotalChunk();
            byte[] chunk = result.getChunk().toByteArray();
            String modelVersion = result.getModelVersion();

            log.info(
                    "receive infer result, task id: {}, chunk index: {}, width: {}, height: {}, model: {}, latency: {}",
                    taskId,
                    index,
                    width,
                    height,
                    modelVersion,
                    latency);

            //                         只是接受，但不再处理
            //                                    taskService.collectInferResult(taskId, index, chunkCount, chunk,
            // width, height,
            //             modelVersion,
            //                         seed);
        } catch (Exception e) {
            log.error(e.getMessage(), e);

        } finally {
            redisTemplate.opsForStream().acknowledge(streamGroupName, message);
            redisTemplate.opsForStream().delete(streamName, message.getId());
        }
    }
}
