syntax = "proto3";

package co.sandai.zeus.infer.proto.task;

// https://j0yswlgboxz.feishu.cn/wiki/Rxi1wFCQriqwVrk3PjKcSiKkn8c
message TaskInferInput {
  int64 taskId = 1;
  string prompt = 2;
  string type = 3; // t2v, i2v, extend-duration
  bytes image = 4; // 图片二进制数据，当 type 为 i2v 时提供
  bytes video = 5; // 视频二进制数据，当 type 为 extend-duration 时提供
  int32 chunkCount = 6;
  string tSchedulerFunc = 7;
  string tSchedulerFuncArgs = 8;
  int64 seed = 9;
  string aspectRatio = 10;
  repeated string specialTokens = 11;
  string vaeModel = 12;
  int64 duration = 13;
  string model = 14;
  int64 nSampleSteps = 15;
  string extra = 16;
  string resolution=17;
}