syntax = "proto3";

package co.sandai.zeus.infer.proto.task;

enum TaskInferStatus {
  Running = 0;
  Failed = 1;
  Success = 2;
  Canceled = 3;
}

message TaskInferResult {
  int64 taskId = 1;
  int64 chunkIndex = 2;
  bytes chunk = 3;
  string modelVersion = 4;
  int64 chunkCompleteTime = 5;
  int32 width = 6;
  int32 height = 7;
  int64 seed = 8;
  int64 totalChunk = 9;
  TaskInferStatus status = 10;
}