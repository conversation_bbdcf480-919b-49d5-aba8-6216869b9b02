plugins {
    id "com.google.protobuf" version "0.9.4"
}


tasks.named('test') {
    useJUnitPlatform()
}


sourceSets {
    main {
        java {
            srcDirs 'src/main/java', 'src/generated/main/java'
        }
    }
}

protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:4.28.2'
    }
}

dependencies {
    implementation project(':modules:domain')
    implementation project(':modules:common')
    implementation project(':modules:infra')

//    implementation 'com.alibaba:dashscope-sdk-java:2.16.9'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'com.google.protobuf:protobuf-java:4.28.2'
    implementation 'software.amazon.awssdk:s3:2.28.21'
    implementation 'redis.clients:jedis:5.2.0'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    implementation 'org.apache.commons:commons-lang3:3.17.0'
}