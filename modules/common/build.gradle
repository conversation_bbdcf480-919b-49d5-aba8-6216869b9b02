plugins {
    id 'java'
}


tasks.named('test') {
    useJUnitPlatform()
}

configurations.all {
    exclude group: 'ch.qos.logback'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    exclude group: 'log4j', module: 'log4j' // 排除 Log4j 1.x
    exclude group: 'commons-logging', module: 'commons-logging'
}

dependencies {

    implementation 'org.apache.logging.log4j:log4j-core:2.23.1'
    implementation 'org.apache.logging.log4j:log4j-api:2.23.1'
    // Removed log4j-slf4j-impl to avoid conflicts
    implementation 'org.slf4j:slf4j-api'


    // 各种工具
    implementation 'com.alibaba:fastjson:1.2.79'
    implementation 'org.aspectj:aspectjrt:1.9.7'
    implementation 'org.apache.commons:commons-lang3:3.17.0'

    // OpenTelemetry API
    implementation 'io.opentelemetry:opentelemetry-api:1.33.0'
    // OpenTelemetry SDK
    implementation 'io.opentelemetry:opentelemetry-sdk:1.33.0'

    implementation "com.twelvemonkeys.imageio:imageio-jpeg:3.9.4" // JPEG 增强
    implementation "com.twelvemonkeys.imageio:imageio-tiff:3.9.4" // TIFF 支持
    implementation "com.twelvemonkeys.imageio:imageio-webp:3.12.0" // webp支持
    implementation "com.drewnoakes:metadata-extractor:2.18.0" // EXIF 元数据读取
    // TwelveMonkeys ImageIO already provides enhanced ImageIO support, no need for additional dependency

    // MP3 dependencies removed - now using FFmpeg exclusively for audio processing

}