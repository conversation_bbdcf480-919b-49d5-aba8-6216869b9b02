package co.sandai.zeus.common.helper;

import static org.junit.jupiter.api.Assertions.*;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;

/**
 * 测试FFmpegHelper的streamTsToMp4方法，使用实际的TS文件
 */
public class FFmpegStreamingTest {

    private static List<File> downloadedTsFiles = new ArrayList<>();
    private static Path tempDirPath;

    /**
     * 设置测试环境并下载TS文件
     */
    @BeforeAll
    static void setUp() throws IOException {
        // 创建临时目录存储下载的TS文件
        tempDirPath = Files.createTempDirectory("ffmpeg_test_");
        System.out.println("创建临时目录: " + tempDirPath);

        // 下载TS文件
        List<String> tsUrls = getTsFileUrlsV2();
        System.out.println("开始下载以下TS文件:");

        // 只下载前3个文件进行测试，以节省时间
        for (int i = 0; i < Math.min(100, tsUrls.size()); i++) {
            String url = tsUrls.get(i);
            System.out.println("  - 正在下载 " + url);

            try {
                File downloadedFile = downloadTsFile(url, tempDirPath.toFile(), i);
                downloadedTsFiles.add(downloadedFile);
                System.out.println(
                        "  - 下载成功: " + downloadedFile.getAbsolutePath() + " (" + downloadedFile.length() + " 字节)");
            } catch (Exception e) {
                System.err.println("  - 下载失败: " + e.getMessage());
                // 继续下载其他文件
            }
        }

        System.out.println("成功下载 " + downloadedTsFiles.size() + " 个TS文件");
        System.out.println("下载文件保存在目录: " + tempDirPath.toAbsolutePath());
        System.out.println("注意: 该目录中的文件不会被自动清理，请手动管理");
    }

    // @AfterAll
    // static void cleanUp() {
    //     System.out.println("保留下载的临时文件，跳过清理...");
    //     System.out.println("文件保存在目录: " + tempDirPath.toAbsolutePath());

    //     // 不清理下载的TS文件
    //     // 原代码被注释掉，不再删除下载的文件
    //     /*
    //     for (File file : downloadedTsFiles) {
    //         if (file.exists() && file.delete()) {
    //             System.out.println("  - 已删除: " + file.getAbsolutePath());
    //         }
    //     }

    //     // 尝试删除临时目录 - 也跳过不执行，保留目录
    //     /*
    //     try {
    //         Files.deleteIfExists(tempDirPath);
    //         System.out.println("  - 已删除临时目录: " + tempDirPath);
    //     } catch (IOException e) {
    //         System.err.println("  - 删除临时目录失败: " + e.getMessage());
    //     }
    //     */
    // }

    /**
     * 测试FFmpegHelper.streamTsToMp4方法
     */
    @Test
    @DisplayName("测试TS文件流式转换为MP4")
    void testStreamTsToMp4(TestInfo testInfo) throws IOException, InterruptedException {
        System.out.println("=== 开始执行测试: " + testInfo.getDisplayName() + " ===");

        // 跳过测试如果没有成功下载任何TS文件
        if (downloadedTsFiles.isEmpty()) {
            System.out.println("警告: 没有可用的TS文件，将创建模拟TS文件");
            createDummyTsFiles();
        }

        // 创建输出流接收MP4数据
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        System.out.println("开始测试streamTsToMp4方法，使用 " + downloadedTsFiles.size() + " 个TS文件");
        for (File file : downloadedTsFiles) {
            System.out.println("  - 使用文件: " + file.getAbsolutePath() + " (" + file.length() + " 字节)");
        }

        // 调用streamTsToMp4方法
        FFmpegHelper.streamTsToMp4(downloadedTsFiles, outputStream, true);

        // 验证输出流包含数据
        byte[] mp4Data = outputStream.toByteArray();
        System.out.println("输出MP4数据大小: " + mp4Data.length + " 字节");

        // 检查输出是否有实际内容
        assertTrue(mp4Data.length > 0, "输出的MP4数据不应为空");

        // 验证输出的文件头是否符合MP4格式 (通常以'ftyp'开头)
        // 检查是否至少有8个字节，然后检查第4-8字节是否为"ftyp"
        if (mp4Data.length > 8) {
            byte[] ftypBytes = {mp4Data[4], mp4Data[5], mp4Data[6], mp4Data[7]};
            String ftypHeader = new String(ftypBytes);
            System.out.println("MP4文件头标识: " + ftypHeader);

            // 多数MP4文件的文件头中包含"ftyp"
            if (ftypHeader.equals("ftyp")) {
                System.out.println("验证成功: 输出数据具有有效的MP4文件头");
            } else {
                System.out.println("未找到标准MP4文件头，但数据仍可能是有效的MP4流");
            }
        }

        // 可选：将数据写入文件以便手动检查
        File outputFile = new File(tempDirPath.toFile(), "output.mp4");
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(mp4Data);
        }
        System.out.println("已保存MP4输出到: " + outputFile.getAbsolutePath());

        System.out.println("测试完成");
    }

    /**
     * 创建模拟TS文件用于测试
     * 在无法下载实际文件时使用
     */
    private void createDummyTsFiles() throws IOException {
        System.out.println("创建模拟TS文件...");
        for (int i = 0; i < 3; i++) {
            File dummyFile = new File(tempDirPath.toFile(), "dummy" + i + ".ts");
            try (FileOutputStream fos = new FileOutputStream(dummyFile)) {
                // 创建一个简单的TS文件头部
                byte[] tsHeader = new byte[] {
                    0x47,
                    0x40,
                    0x00,
                    0x10,
                    0x00, // TS包头
                    (byte) 0x47,
                    (byte) 0x41,
                    0x00,
                    0x30,
                    0x07, // 另一个TS包头
                    0x00,
                    0x01,
                    (byte) 0xBA,
                    0x44,
                    0x00,
                    0x04,
                    0x00,
                    0x04,
                    0x01,
                    0x00,
                    0x00,
                    0x00,
                    0x03,
                    (byte) 0x80,
                    0x00,
                    0x00, // PES包头
                    0x00,
                    0x01,
                    (byte) 0xBB,
                    0x00,
                    0x0C,
                    (byte) 0x80,
                    (byte) 0x80,
                    0x1C,
                    0x0F,
                    (byte) 0xFF,
                    (byte) 0xFF // 另一个PES包头
                };

                // 写入TS头
                fos.write(tsHeader);

                // 添加一些随机数据作为视频内容
                byte[] dummyContent = new byte[10240]; // 10KB的数据
                for (int j = 0; j < dummyContent.length; j++) {
                    dummyContent[j] = (byte) (j % 256);
                }
                fos.write(dummyContent);
            }
            downloadedTsFiles.add(dummyFile);
            System.out.println("  - 创建模拟TS文件: " + dummyFile.getAbsolutePath() + " (" + dummyFile.length() + " 字节)");
        }
    }

    /**
     * 下载TS文件到本地
     */
    private static File downloadTsFile(String urlString, File targetDir, int index) throws IOException {
        // 使用URI创建URL以避免URL构造函数的警告
        URI uri = URI.create(urlString);
        URL url = uri.toURL();
        String fileName = "file" + index + ".ts";
        File targetFile = new File(targetDir, fileName);

        try (InputStream in = url.openStream();
                FileOutputStream out = new FileOutputStream(targetFile)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }

        return targetFile;
    }

    /**
     * 获取TS文件URL列表
     */
    private static List<String> getTsFileUrlsV2() {
        List<String> urls = new ArrayList<>();
        String m3u8 =
                """
                #EXTINF:5.041667, no desc
                https://cdn.sandai.cn/artifacts/magi-prod/caf4216f-e72e-449d-bb8b-c385f51853c0/202506.ts




                #EXTINF:1.000000, no desc
                https://cdn.sandai.cn/artifacts/magi-prod/52ca8287-69bd-4621-92f1-3fef2d4bb908/202506.ts

                #EXTINF:1.000000, no desc
                https://cdn.sandai.cn/artifacts/magi-prod/42519a06-69f8-478d-b91a-b9ecf082a342/202506.ts

                #EXTINF:1.000000, no desc
                https://cdn.sandai.cn/artifacts/magi-prod/3918f8a7-eff3-41e9-a28c-a76296ad70aa/202506.ts

                #EXTINF:1.000000, no desc
                https://cdn.sandai.cn/artifacts/magi-prod/e471bc0b-ef48-44cd-94f5-3c744d255ec5/202506.ts

                #EXTINF:1.000000, no desc
                https://cdn.sandai.cn/artifacts/magi-prod/35046381-1c37-4847-affd-dc749d44baef/202506.ts

                #EXT-X-ENDLIST
                """;

        // Parse the m3u8 string to extract URLs
        for (String line : m3u8.split("\n")) {
            line = line.trim();
            // Skip comment lines and empty lines
            if (!line.isEmpty() && !line.startsWith("#")) {
                urls.add(line);
            }
        }

        return urls;
    }

    private static List<String> getTsFileUrls() {
        List<String> urls = new ArrayList<>();

        // 用户提供的TS文件URL列表
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/cdc46187-40f6-4665-b8bc-8b8d61309e99/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/1510f713-2fe2-4f6b-acf8-ffc727a0d9b0/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/21455e29-501f-44da-ad93-30e2dc172072/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/9b59e1a7-14b7-4922-9773-191f13f18669/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/33a52225-210b-43c0-881b-b2dc3cbfed1a/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/98666608-3c46-4862-b89f-4cfba859c565/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/468011cb-54b5-4f18-88c7-81d44b874667/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/85d30e6b-bdec-45c8-9fea-a6a55999bb6f/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/f8ba2629-e859-4eaa-b26c-ea2eef3f683e/202503.ts");
        urls.add(
                "https://athena-artifacts-stagging.oss-cn-shanghai.aliyuncs.com/artifacts/default/e3181b48-c6b0-4281-bd1d-1e866e2246fb/202503.ts");

        return urls;
    }
}
