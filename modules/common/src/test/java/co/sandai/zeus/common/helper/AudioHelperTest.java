package co.sandai.zeus.common.helper;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import org.junit.jupiter.api.Test;

public class AudioHelperTest {

    /**
     * Test the normalizeAudioLoudness function with sample MP3 files
     * Downloads sample files from the provided URLs, processes them,
     * and saves both original and normalized versions to a temp directory
     */
    @Test
    public void testNormalizeAudioLoudnessMP3() throws IOException {
        String verifiedLanguagesJson =
                """
        { "verified_languages":
        [
        {
          "language": "bg",
          "model_id": "eleven_multilingual_v2",
          "accent": "standard",
          "locale": "bg-BG",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/615cb84e-bf01-48e7-a1f1-9c0f3f38217e.mp3"
        },
        {
          "language": "cs",
          "model_id": "eleven_multilingual_v2",
          "accent": "standard",
          "locale": "cs-CZ",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/6a78680e-aaf9-4089-a377-266af50c3393.mp3"
        },
        {
          "language": "it",
          "model_id": "eleven_multilingual_v2",
          "accent": "standard",
          "locale": "it-IT",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/57844029-9ed6-4389-b9bf-b32b2d997323.mp3"
        },
        {
          "language": "pt",
          "model_id": "eleven_multilingual_v2",
          "accent": "brazilian",
          "locale": "pt-BR",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/7073e067-328a-412f-baa9-dc1591df64ae.mp3"
        },
        {
          "language": "hi",
          "model_id": "eleven_multilingual_v2",
          "accent": "standard",
          "locale": "hi-IN",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/19473451-edfa-455b-9e83-e413d3e43e6d.mp3"
        },
        {
          "language": "es",
          "model_id": "eleven_multilingual_v2",
          "accent": "latin",
          "locale": null,
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/f9590bb9-1cc1-4789-b2ac-64e1a1cb48b8.mp3"
        },
        {
          "language": "fr",
          "model_id": "eleven_multilingual_v2",
          "accent": "standard",
          "locale": "fr-FR",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/430f69b3-e302-445f-bc6d-0d5f7f6cde15.mp3"
        },
        {
          "language": "da",
          "model_id": "eleven_multilingual_v2",
          "accent": "zealandic",
          "locale": "da-DK",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/34647257-3e6b-4233-8c2a-e38777ff9cd8.mp3"
        },
        {
          "language": "id",
          "model_id": "eleven_multilingual_v2",
          "accent": "standard",
          "locale": "id-ID",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/249edd36-72b5-469b-b942-22025816e714.mp3"
        },
        {
          "language": "uk",
          "model_id": "eleven_multilingual_v2",
          "accent": "standard",
          "locale": "uk-UA",
          "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/dbad4a9d-361a-4841-b16a-ca75c9976961.mp3"
        }
      ]
    }
        """;

        // Create temp directory for test files
        Path tempDir = Files.createTempDirectory("audio_normalizer_test");
        System.out.println("Saving test files to: " + tempDir);

        // Parse JSON to extract preview URLs
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(verifiedLanguagesJson);
        JsonNode verifiedLanguages = rootNode.get("verified_languages");

        List<String> results = new ArrayList<>();
        results.add("Audio Normalization Test Results:");
        results.add("Test files saved to: " + tempDir.toString());
        results.add("--------------------------------------------------");

        // Process each language entry
        for (JsonNode language : verifiedLanguages) {
            String languageCode = language.get("language").asText();
            String previewUrl = language.get("preview_url").asText();

            try {
                // Download the audio file
                System.out.println("Downloading audio for language: " + languageCode + " from URL: " + previewUrl);
                byte[] originalData = downloadFromUrl(previewUrl);

                // Save original file
                String originalFilename = languageCode + "_original.mp3";
                Path originalPath = Paths.get(tempDir.toString(), originalFilename);
                Files.write(originalPath, originalData);

                // Process with normalizer
                System.out.println("Normalizing audio for language: " + languageCode);
                byte[] normalizedData = AudioHelper.normalizeLoudness(originalData);

                // Save normalized file
                String normalizedFilename =
                        languageCode + "_normalized.mp3"; // Output stays MP3 with new implementation
                Path normalizedPath = Paths.get(tempDir.toString(), normalizedFilename);
                Files.write(normalizedPath, normalizedData);

                // Verify the result
                assertNotNull(normalizedData, "Normalized data should not be null");
                assertTrue(normalizedData.length > 0, "Normalized data should not be empty");

                results.add(String.format(
                        "Language: %s - Original: %s (%d bytes), Normalized: %s (%d bytes)",
                        languageCode,
                        originalFilename,
                        originalData.length,
                        normalizedFilename,
                        normalizedData.length));

            } catch (Exception e) {
                e.printStackTrace();
                assertTrue(false, "Exception should not be thrown");
            }
        }

        // Write summary file
        Path summaryPath = Paths.get(tempDir.toString(), "summary.txt");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(summaryPath.toFile()))) {
            for (String line : results) {
                writer.write(line);
                writer.newLine();
            }
        }

        System.out.println("Test complete. Results saved to: " + tempDir);
        System.out.println("Summary:\n" + String.join("\n", results));
    }

    /**
     * Test the normalizeAudioLoudness function with WAV files
     * Creates sample WAV files, processes them, and saves both original and normalized versions
     */
    @Test
    public void testNormalizeAudioLoudnessWAV() throws Exception {
        // Create temp directory for test files
        Path tempDir = Files.createTempDirectory("audio_normalizer_wav_test");
        System.out.println("Saving WAV test files to: " + tempDir);

        List<String> results = new ArrayList<>();
        results.add("Audio Normalization WAV Test Results:");
        results.add("Test files saved to: " + tempDir.toString());
        results.add("--------------------------------------------------");

        // Generate sample WAV data using Java Sound API
        byte[] sampleWavData = generateSampleWavFile("quiet", 0.1f); // Quiet sample
        byte[] loudWavData = generateSampleWavFile("loud", 0.8f); // Louder sample

        // Process both samples
        processAndSaveWavSample(tempDir, sampleWavData, "quiet", results);
        processAndSaveWavSample(tempDir, loudWavData, "loud", results);

        // Write summary file
        Path summaryPath = Paths.get(tempDir.toString(), "summary_wav.txt");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(summaryPath.toFile()))) {
            for (String line : results) {
                writer.write(line);
                writer.newLine();
            }
        }

        System.out.println("WAV Test complete. Results saved to: " + tempDir);
        System.out.println("WAV Summary:\n" + String.join("\n", results));
    }

    /**
     * Process and save a WAV sample
     */
    private void processAndSaveWavSample(Path tempDir, byte[] wavData, String name, List<String> results)
            throws IOException {
        // Save original file
        String originalFilename = name + "_original.wav";
        Path originalPath = Paths.get(tempDir.toString(), originalFilename);
        Files.write(originalPath, wavData);

        // Process with normalizer
        System.out.println("Normalizing WAV audio: " + name);
        byte[] normalizedData = AudioHelper.normalizeLoudness(wavData);

        // Save normalized file
        String normalizedFilename = name + "_normalized.wav";
        Path normalizedPath = Paths.get(tempDir.toString(), normalizedFilename);
        Files.write(normalizedPath, normalizedData);

        // Verify the result
        assertNotNull(normalizedData, "Normalized data should not be null");
        assertTrue(normalizedData.length > 0, "Normalized data should not be empty");

        results.add(String.format(
                "Sample: %s - Original: %s (%d bytes), Normalized: %s (%d bytes)",
                name, originalFilename, wavData.length, normalizedFilename, normalizedData.length));
    }

    /**
     * Generate a sample WAV file with specified amplitude
     */
    private byte[] generateSampleWavFile(String name, float amplitude) throws Exception {
        // Create a sine wave with the given amplitude
        float sampleRate = 44100.0f;
        int sampleSizeInBits = 16;
        int channels = 2;
        boolean signed = true;
        boolean bigEndian = false;

        AudioFormat format = new AudioFormat(sampleRate, sampleSizeInBits, channels, signed, bigEndian);

        // Create a 3-second sample
        int durationInSeconds = 3;
        int frameSize = format.getFrameSize();
        int bufferSize = (int) (sampleRate * frameSize * durationInSeconds);
        byte[] buffer = new byte[bufferSize];

        // Generate a 440Hz sine wave
        double frequency = 440.0;
        double period = sampleRate / frequency;
        double maxValue = Math.pow(2, sampleSizeInBits - 1) - 1;

        for (int i = 0; i < bufferSize; i += frameSize) {
            double angle = 2.0 * Math.PI * (i / frameSize) / period;
            double sinValue = Math.sin(angle) * amplitude;

            // Scale to the bit depth and apply amplitude
            int intValue = (int) (sinValue * maxValue);

            // Write the sample value to both channels
            for (int channel = 0; channel < channels; channel++) {
                int channelOffset = i + channel * (sampleSizeInBits / 8);
                if (channelOffset + 1 < buffer.length) {
                    // Write the sample in little-endian format
                    buffer[channelOffset] = (byte) (intValue & 0xFF);
                    buffer[channelOffset + 1] = (byte) ((intValue >> 8) & 0xFF);
                }
            }
        }

        // Convert buffer to WAV format
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        AudioInputStream ais =
                new AudioInputStream(new ByteArrayInputStream(buffer), format, buffer.length / format.getFrameSize());
        AudioSystem.write(ais, AudioFileFormat.Type.WAVE, baos);

        return baos.toByteArray();
    }

    /**
     * Download audio data from URL
     */
    private byte[] downloadFromUrl(String urlString) throws IOException {
        URL url = URI.create(urlString).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.connect();

        if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new IOException("HTTP error code: " + connection.getResponseCode());
        }

        return connection.getInputStream().readAllBytes();
    }
}
