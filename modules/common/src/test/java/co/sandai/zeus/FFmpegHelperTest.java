package co.sandai.zeus;

import co.sandai.zeus.common.helper.FFmpegHelper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

@SpringBootTest(classes = {ResourceLoader.class})
public class FFmpegHelperTest {

    @Autowired
    private ResourceLoader resourceLoader;

    private static final Logger log = LoggerFactory.getLogger(FFmpegHelperTest.class);

    @Test
    public void testMergeTs() {
        Resource video = resourceLoader.getResource("classpath:/videos/chunk.ts");
        Assertions.assertDoesNotThrow(() -> {
            byte[] tsData = video.getInputStream().readAllBytes();
            FFmpegHelper.mergeTsToMp4(new byte[][] {tsData, tsData});
        });
    }

    @Test
    public void testExtractKeyFrame() {
        try {
            Resource video = resourceLoader.getResource("classpath:/videos/kiss.mp4");
            FFmpegHelper.ExtractFrameResult result = FFmpegHelper.extractKeyFrame(video.getInputStream());

            Assertions.assertNotNull(result);
            Assertions.assertEquals(384, result.getWidth());
            Assertions.assertEquals(224, result.getHeight());
            Assertions.assertTrue(result.getFrameData().length > 0);

            FFmpegHelper.MediaSize vSize = FFmpegHelper.getMediaSize(new ByteArrayInputStream(result.getFrameData()));
            Assertions.assertEquals(384, vSize.getWidth());
            Assertions.assertEquals(224, vSize.getHeight());

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testSliceVideo() {
        Assertions.assertDoesNotThrow(() -> {
            Resource video = resourceLoader.getResource("classpath:/videos/kiss.mp4");
            long start = 0;
            long end = 533333;
            //            long end = 1000000;
            FFmpegHelper.sliceVideo(video.getInputStream().readAllBytes(), start, end);
        });
    }

    @Test
    public void testGetMediaSize() {
        try {
            Resource image = resourceLoader.getResource("classpath:/images/trip.jpg");
            FFmpegHelper.MediaSize size = FFmpegHelper.getMediaSize(image.getInputStream());
            Assertions.assertEquals(1080, size.getWidth());
            Assertions.assertEquals(810, size.getHeight());
            log.info("image size extracted successfully, width: {}, height: {}", size.getWidth(), size.getHeight());

            Resource video = resourceLoader.getResource("classpath:/videos/kiss.mp4");
            FFmpegHelper.MediaSize vSize = FFmpegHelper.getMediaSize(video.getInputStream());
            Assertions.assertEquals(384, vSize.getWidth());
            Assertions.assertEquals(224, vSize.getHeight());
            log.info("video size extracted successfully, width: {}, height: {}", vSize.getWidth(), vSize.getHeight());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testCropImage() {
        try {
            // jpg
            Resource image = resourceLoader.getResource("classpath:/images/trip.jpg");
            byte[] out = FFmpegHelper.cropImage(image.getInputStream(), 800, 600, 200, 200);
            FFmpegHelper.MediaSize size = FFmpegHelper.getMediaSize(new ByteArrayInputStream(out));
            Assertions.assertEquals(200, size.getWidth());
            Assertions.assertEquals(200, size.getHeight());

            // png
            Resource png = resourceLoader.getResource("classpath:/images/alpaca.png");
            byte[] pngOut = FFmpegHelper.cropImage(png.getInputStream(), 1000, 300, 300, 300);
            FFmpegHelper.MediaSize pngOutSize = FFmpegHelper.getMediaSize(new ByteArrayInputStream(pngOut));
            Assertions.assertEquals(300, pngOutSize.getWidth());
            Assertions.assertEquals(300, pngOutSize.getHeight());

            // webp
            Resource webp = resourceLoader.getResource("classpath:/images/yellow_rose.webp");
            byte[] webpOut = FFmpegHelper.cropImage(webp.getInputStream(), 100, 200, 100, 100);
            FFmpegHelper.MediaSize webpOutSize = FFmpegHelper.getMediaSize(new ByteArrayInputStream(webpOut));
            Assertions.assertEquals(100, webpOutSize.getWidth());
            Assertions.assertEquals(100, webpOutSize.getHeight());

            // Files.write(new File("check.jpg").toPath(), webpOut);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testCropVideo() {
        try {
            // jpg
            Resource video = resourceLoader.getResource("classpath:/videos/kiss.mp4");
            byte[] out = FFmpegHelper.cropVideo(video.getInputStream(), 0, 0, 200, 100);
            FFmpegHelper.MediaSize size = FFmpegHelper.getMediaSize(new ByteArrayInputStream(out));
            Assertions.assertEquals(200, size.getWidth());
            Assertions.assertEquals(100, size.getHeight());

            // Files.write(new File("check.mp4").toPath(), out);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
