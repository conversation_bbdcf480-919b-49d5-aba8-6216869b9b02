package co.sandai.zeus.common.helper;

import co.sandai.zeus.common.log.LogUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;
import java.util.stream.Collectors;
import javax.imageio.ImageIO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FFmpegHelper {
    public static byte[] mergeTsToMp4(byte[][] tsByteArrays) throws IOException, InterruptedException {
        long start = System.currentTimeMillis();
        // 创建临时目录用于存储 TS 文件
        File tempDir = Files.createTempDirectory("tsFiles").toFile();
        tempDir.deleteOnExit();
        // 创建临时 TS 文件
        List<File> tsFiles = new ArrayList<>();
        for (int i = 0; i < tsByteArrays.length; i++) {
            File tsFile = new File(tempDir, "file" + i + ".ts");
            try (FileOutputStream fos = new FileOutputStream(tsFile)) {
                fos.write(tsByteArrays[i]);
                tsFiles.add(tsFile);
            }
        }

        // 创建临时文件用于存储合并后的 MP4 文件
        File outputFile = File.createTempFile("output", ".mp4");
        outputFile.deleteOnExit();

        // 构建 ffmpeg 命令
        String input = "concat:" + tsFiles.stream().map(File::getAbsolutePath).collect(Collectors.joining("|"));
        String output = outputFile.getAbsolutePath();
        ProcessBuilder pb = new ProcessBuilder(
                "ffmpeg", "-y", "-i", input, "-c", "copy", "-pix_fmt", "yuv420p", "-movflags", "faststart", output);

        // 执行 ffmpeg 命令
        Process process = pb.start();
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("ffmpeg process failed with exit code " + exitCode);
        }

        // 读取合并后的 MP4 文件为字节数组
        byte[] result = Files.readAllBytes(outputFile.toPath());

        //noinspection ResultOfMethodCallIgnored
        tsFiles.forEach(File::delete);
        boolean inputDeleted = tempDir.delete();
        boolean outDeleted = outputFile.delete();
        log.info(
                "merge ts files into mp4, cost: {}ms, input temp dir deleted: {}, output temp file deleted: {}",
                System.currentTimeMillis() - start,
                inputDeleted,
                outDeleted);
        return result;
    }

    /**
     * Streams TS files directly to MP4 format and writes to the provided output stream
     * This avoids loading the entire output into memory
     *
     * @param tsFiles List of TS files to convert and stream
     * @param outputStream The output stream to write the MP4 content to
     * @throws IOException If an I/O error occurs during streaming
     * @throws InterruptedException If the process is interrupted
     */
    public static void streamTsToMp4(List<File> tsFiles, OutputStream outputStream)
            throws IOException, InterruptedException {
        // Use the default method (no watermark) for stability
        streamTsToMp4(tsFiles, outputStream, false);
    }

    /**
     * Streams TS files directly to MP4 format with optional watermark and writes to the provided output stream
     *
     * @param tsFiles List of TS files to convert and stream
     * @param outputStream The output stream to write the MP4 content to
     * @param enableWatermark Whether to add watermark to the output
     * @throws IOException If an I/O error occurs during streaming
     * @throws InterruptedException If the process is interrupted
     */
    public static void streamTsToMp4(List<File> tsFiles, OutputStream outputStream, boolean enableWatermark)
            throws IOException, InterruptedException {
        long start = System.currentTimeMillis();

        // Create a concat file with list of TS files (more reliable than concat protocol for many files)
        File concatFile = File.createTempFile("concat", ".txt");
        concatFile.deleteOnExit();

        try (FileOutputStream fos = new FileOutputStream(concatFile)) {
            for (File tsFile : tsFiles) {
                String line = "file '" + tsFile.getAbsolutePath() + "'\n";
                fos.write(line.getBytes(StandardCharsets.UTF_8));
            }
        }

        // Create command list for more flexibility
        List<String> command = new ArrayList<>();
        command.add("ffmpeg");
        command.add("-y");
        command.add("-f");
        command.add("concat");
        command.add("-safe");
        command.add("0");
        command.add("-i");
        command.add(concatFile.getAbsolutePath());

        // Add watermark if enabled
        if (enableWatermark) {
            addWatermarkToCommand(command, tsFiles);
        } else {
            // No watermark - direct stream copy
            command.add("-c");
            command.add("copy");
        }

        // Add output parameters
        command.add("-movflags");
        command.add("+empty_moov+default_base_moof+faststart+frag_keyframe");
        command.add("-f");
        command.add("mp4");
        command.add("-");

        // Build the process using our command list
        ProcessBuilder pb = new ProcessBuilder(command);

        log.info("Executing FFmpeg command: {}", String.join(" ", pb.command()));

        // Start ffmpeg process
        Process process = pb.start();

        // Create a thread to process the error stream to prevent buffer overflow
        Thread errorReader = new Thread(() -> {
            try (InputStream errorStream = process.getErrorStream()) {
                byte[] buffer = new byte[8192];
                while (errorStream.read(buffer) >= 0) {
                    // Just drain the stream, we don't need to do anything with the output
                }
            } catch (IOException e) {
                log.warn("Error reading ffmpeg error stream: {}", e.getMessage());
            }
        });
        errorReader.setDaemon(true);
        errorReader.start();

        // Stream the output directly to the provided output stream with backpressure control
        try (InputStream inputStream = process.getInputStream()) {
            // 使用较小的缓冲区，减少单次写入量
            byte[] buffer = new byte[4096];
            int bytesRead;
            boolean firstDataLogged = false;
            long startTime = System.currentTimeMillis();

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                try {
                    // 记录首次返回数据的时间
                    if (!firstDataLogged) {
                        long timeToFirstData = System.currentTimeMillis() - startTime;
                        log.info(
                                "First FFmpeg data received after {}ms, sending {} bytes to client",
                                timeToFirstData,
                                bytesRead);
                        firstDataLogged = true;
                    }

                    // 记录每个数据包大小
                    log.debug("FFmpeg data packet size: {} bytes", bytesRead);
                    // 写入数据到输出流
                    outputStream.write(buffer, 0, bytesRead);
                    // 立即刷新，确保数据被及时发送，避免缓冲区积压
                    outputStream.flush();

                    // 添加短暂暂停，允许下游处理数据，实现简单的背压控制
                    if (bytesRead == buffer.length) {
                        try {
                            // 小暂停，降低生产速率
                            Thread.sleep(1);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.warn("Streaming interrupted", e);
                            break;
                        }
                    }
                } catch (IOException e) {
                    // 客户端可能断开连接或出现网络问题
                    log.error("Error writing to output stream: {}", e.getMessage());
                    break;
                }
            }
        }

        try {
            // Wait for process to complete
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                log.error("ffmpeg streaming process failed with exit code {}", exitCode);
                // fallback minimal MP4 header when streaming fails
                byte[] header = new byte[] {0, 0, 0, 8, 'f', 't', 'y', 'p'};
                outputStream.write(header);
                outputStream.flush();
                return;
            }
        } catch (InterruptedException e) {
            // Handle interruption properly
            Thread.currentThread().interrupt(); // Preserve interrupt status
            log.warn("FFmpeg process interrupted", e);

            // Force terminate the ffmpeg process to avoid orphaned processes
            process.destroyForcibly();

            // Write minimal MP4 header to avoid client-side parsing errors
            try {
                byte[] header = new byte[] {0, 0, 0, 8, 'f', 't', 'y', 'p'};
                outputStream.write(header);
                outputStream.flush();
            } catch (IOException ioe) {
                log.error("Failed to write fallback header after interruption", ioe);
            }
            return;
        }

        // Clean up concat file
        boolean concatDeleted = concatFile.delete();

        log.info(
                "Streamed TS files to MP4, cost: {}ms, concat file deleted: {}",
                System.currentTimeMillis() - start,
                concatDeleted);
    }

    /**
     * Adds watermark parameters to an FFmpeg command list
     *
     * @param command The FFmpeg command list to modify
     * @param tsFiles List of TS files used to calculate video dimensions
     */
    private static void addWatermarkToCommand(List<String> command, List<File> tsFiles) {
        try {
            // Load default watermark image to get dimensions (needed for scaling calculations)
            BufferedImage watermarkImg = WatermarkConfig.getDefaultWatermarkImage();

            log.info("Using default watermark image");
            int origWidth = watermarkImg.getWidth();
            int origHeight = watermarkImg.getHeight();

            log.info("Loaded watermark image | original size: {}x{}", origWidth, origHeight);

            float marginRatio = WatermarkConfig.marginRatio;
            int minMargin = WatermarkConfig.minMargin;
            int minWidth = WatermarkConfig.minWidth;
            int maxWidth = WatermarkConfig.maxWidth;
            float opacity = WatermarkConfig.opacity;

            // Get dimensions from first TS file
            File firstTsFile = tsFiles.get(0);
            int estimatedWidth;
            int estimatedHeight;

            try (FileInputStream fis = new FileInputStream(firstTsFile)) {
                MediaSize mediaSize = getMediaSize(fis);
                estimatedWidth = mediaSize.getWidth();
                estimatedHeight = mediaSize.getHeight();
                log.info("Detected video dimensions: {}x{}", estimatedWidth, estimatedHeight);
            }

            // Check if video is in portrait orientation
            boolean isPortrait = estimatedHeight > estimatedWidth;

            // Choose appropriate scale ratio based on orientation
            float effectiveScaleRatio = isPortrait ? WatermarkConfig.portraitScaleRatio : WatermarkConfig.scaleRatio;
            log.info(
                    "Video orientation: {}, using scale ratio: {}",
                    isPortrait ? "portrait" : "landscape",
                    effectiveScaleRatio);

            // Calculate target watermark width
            int targetWidth = (int) (estimatedWidth * effectiveScaleRatio);
            // Constrain to min/max limits
            targetWidth = Math.max(Math.min(targetWidth, maxWidth), minWidth);

            // Calculate target height preserving aspect ratio
            int targetHeight = (int) (origHeight * ((double) targetWidth / origWidth));

            // Calculate margin (matching Python implementation)
            int margin = Math.max((int) (estimatedWidth * marginRatio), minMargin);

            log.info(
                    "Calculated watermark dimensions - target size: {}x{}, margin: {}px",
                    targetWidth,
                    targetHeight,
                    margin);

            // Add watermark image input
            command.add("-i");
            command.add(WatermarkConfig.getDefaultWatermarkPath());

            // 获取水印位置参数以便微调
            String horizontalAlign = WatermarkConfig.horizontalAlign;
            String verticalAlign = WatermarkConfig.verticalAlign;
            int horizontalMargin = WatermarkConfig.horizontalMargin;
            int verticalMargin = WatermarkConfig.verticalMargin;

            // 如果未指定特定边距，则使用通用边距
            if (horizontalMargin <= 0) horizontalMargin = margin;
            if (verticalMargin <= 0) verticalMargin = margin;

            // 位置计算，并确保水平和垂直边距视觉上一致
            String xPosition, yPosition;

            // 水平位置计算
            switch (horizontalAlign) {
                case "left":
                    xPosition = Integer.toString(horizontalMargin);
                    break;
                case "right":
                    xPosition = "main_w-overlay_w-" + horizontalMargin;
                    break;
                case "center":
                    xPosition = "(main_w-overlay_w)/2";
                    break;
                default: // 默认右对齐
                    xPosition = "main_w-overlay_w-" + horizontalMargin;
            }

            // 垂直位置计算
            switch (verticalAlign) {
                case "top":
                    yPosition = Integer.toString(verticalMargin);
                    break;
                case "bottom":
                    yPosition = "main_h-overlay_h-" + verticalMargin;
                    break;
                case "center":
                    yPosition = "(main_h-overlay_h)/2";
                    break;
                default: // 默认底部对齐
                    yPosition = "main_h-overlay_h-" + verticalMargin;
            }

            // 合并位置
            String overlayPosition = xPosition + ":" + yPosition;

            log.info(
                    "Watermark position: {}, {} with margin: {}x{}, size: {}x{}",
                    horizontalAlign,
                    verticalAlign,
                    horizontalMargin,
                    verticalMargin,
                    targetWidth,
                    targetHeight);

            // Enhanced filter with scaling to match Python implementation
            StringBuilder filterComplex = new StringBuilder();

            // First scale the watermark to target dimensions
            filterComplex.append("[1:v]scale=" + targetWidth + ":" + targetHeight);

            // 确保水印使用rgba格式，有利于透明通道处理
            filterComplex.append(",format=rgba");

            // Add opacity control if needed (when opacity < 1.0)
            if (opacity < 1.0f) {
                filterComplex.append(",colorchannelmixer=a=" + opacity);
            }

            // Finish watermark preparation
            filterComplex.append("[wm];");

            // 将主视频转换为rgba，改善Alpha混合
            filterComplex.append("[0:v]format=rgba[bg];");

            // 使用标准overlay混合，但忽略Alpha为0的像素
            filterComplex.append("[bg][wm]overlay=" + overlayPosition + ":format=auto,format=yuv420p");

            command.add("-filter_complex");
            command.add(filterComplex.toString());

            log.info("Using enhanced watermark filter: {}", filterComplex);

            // Standard video codec parameters
            command.add("-c:v");
            command.add("libx264");
            command.add("-preset");
            command.add("medium"); // Good balance between speed and quality
            command.add("-crf");
            command.add("23");

            // Copy audio stream
            command.add("-c:a");
            command.add("copy");

            log.info("Watermark enabled with enhanced filter matching Python implementation");

        } catch (Exception e) {
            log.error("Error setting up watermark: {}", e.getMessage());
            // Fallback to direct stream copy
            command.add("-c");
            command.add("copy");
        }
    }

    /**
     * Configuration class for watermark settings
     */
    public static class WatermarkConfig {
        // Default values matching Python implementation
        public static float opacity = 1.0f; // Opacity (0.0f-1.0f)
        public static float scaleRatio =
                0.15f; // Watermark width relative to video width for landscape (matching Python scale_factor)
        public static float portraitScaleRatio = 0.25f; // Larger watermark scale for portrait videos
        public static float marginRatio = 0.03f; // Margin relative to video width (matching Python margin_ratio)
        public static int minMargin = 20; // Minimum margin in pixels as a fallback
        public static int minWidth = 20; // Minimum watermark width in pixels
        public static int maxWidth = 256; // Maximum watermark width in pixels

        // 高级位置控制参数（更灵活的定位）
        public static String horizontalAlign = "right"; // 水平对齐: "left", "center", "right"
        public static String verticalAlign = "bottom"; // 垂直对齐: "top", "center", "bottom"
        public static int horizontalMargin = 30; // 水平边距（像素）
        public static int verticalMargin = 30; // 垂直边距（像素）

        // 缓存设置
        public static boolean useCache = true; // Whether to cache watermark image
        private static BufferedImage cachedWatermarkImage = null;
        private static String cachedImagePath = null;
        private static final String DEFAULT_WATERMARK_RESOURCE = "/wm1.png";

        // Temporary file for extracted watermark when running from JAR
        private static File tempWatermarkFile = null;

        /**
         * Get the default watermark image path
         *
         * @return Path to the default watermark file
         */
        public static String getDefaultWatermarkPath() {
            try {
                // First check if we already have a valid temp file
                if (tempWatermarkFile != null && tempWatermarkFile.exists()) {
                    log.debug("Using existing temp watermark file: {}", tempWatermarkFile.getAbsolutePath());
                    return tempWatermarkFile.getAbsolutePath();
                }

                URL resource = WatermarkConfig.class.getResource(DEFAULT_WATERMARK_RESOURCE);
                if (resource == null) {
                    log.warn("Default watermark resource not found: {}", DEFAULT_WATERMARK_RESOURCE);
                    return null;
                }

                // If the URL protocol is "file", we can use it directly
                if ("file".equals(resource.getProtocol())) {
                    log.debug("Using file system watermark: {}", resource.getPath());
                    return resource.getPath();
                }

                // For other protocols (especially jar), extract to temp file
                log.info("Extracting watermark from {} to temporary file", resource);
                tempWatermarkFile = File.createTempFile("watermark_", ".png");
                tempWatermarkFile.deleteOnExit(); // Clean up on JVM exit

                try (InputStream in = resource.openStream();
                        FileOutputStream out = new FileOutputStream(tempWatermarkFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }

                log.info("Extracted watermark to: {}", tempWatermarkFile.getAbsolutePath());
                return tempWatermarkFile.getAbsolutePath();
            } catch (Exception e) {
                log.error("Error getting default watermark path: {}", e.getMessage(), e);
                return null;
            }
        }

        /**
         * Get the default watermark image
         *
         * @return BufferedImage of the default watermark
         */
        public static synchronized BufferedImage getDefaultWatermarkImage() {
            if (useCache && cachedWatermarkImage != null) {
                log.debug("Using cached default watermark image");
                return cachedWatermarkImage;
            }

            try {
                URL resource = WatermarkConfig.class.getResource(DEFAULT_WATERMARK_RESOURCE);
                if (resource == null) {
                    log.warn("Default watermark resource not found: {}", DEFAULT_WATERMARK_RESOURCE);
                    return null;
                }

                BufferedImage image = ImageIO.read(resource);
                if (image == null) {
                    log.warn("Failed to read default watermark image");
                    return null;
                }

                // Update cache if caching is enabled
                if (useCache) {
                    cachedWatermarkImage = image;
                    cachedImagePath = resource.getPath();
                    log.debug("Cached default watermark image");
                }

                return image;
            } catch (Exception e) {
                log.error("Error loading default watermark image: {}", e.getMessage());
                return null;
            }
        }

        /**
         * Get watermark image from a specific path, using cache if enabled
         *
         * @param watermarkPath Path to the watermark image
         * @return BufferedImage of the watermark
         */
        public static synchronized BufferedImage getWatermarkImage(String watermarkPath) {
            if (useCache
                    && cachedWatermarkImage != null
                    && cachedImagePath != null
                    && cachedImagePath.equals(watermarkPath)) {
                log.info("Using cached watermark image from: {}", cachedImagePath);
                return cachedWatermarkImage;
            }

            try {
                File watermarkFile = new File(watermarkPath);
                if (!watermarkFile.exists() || !watermarkFile.canRead()) {
                    log.warn("Watermark file not found or not readable: {}", watermarkPath);
                    return null;
                }

                BufferedImage image = ImageIO.read(watermarkFile);
                if (image == null) {
                    log.warn("Failed to read watermark image: {}", watermarkPath);
                    return null;
                }

                // Update cache if caching is enabled
                if (useCache) {
                    cachedWatermarkImage = image;
                    cachedImagePath = watermarkPath;
                    log.info("Cached watermark image: {}", watermarkPath);
                }

                return image;
            } catch (Exception e) {
                log.error("Error loading watermark image: {}", e.getMessage());
                return null;
            }
        }
    }

    @Getter
    @Setter
    public static class ExtractFrameResult {
        private int width;
        private int height;
        private byte[] frameData;
        private String ext = "jpg";
    }

    public static ExtractFrameResult extractKeyFrame(InputStream inputStream) {
        // 默认提取首帧
        return extractFrame(inputStream, ExtractModeEnum.KEY_FRAME);
    }

    /**
     * 支持提取尾帧
     * @param inputStream
     * @param extractMode
     * @return
     */
    public static ExtractFrameResult extractFrame(InputStream inputStream, ExtractModeEnum extractMode) {
        File tempInput = null;
        File tempOutput = null;
        try {
            tempInput = File.createTempFile("input-", ".media");
            tempOutput = File.createTempFile("output-", ".jpg");
            Files.write(tempInput.toPath(), inputStream.readAllBytes());

            ExtractFrameResult result = new ExtractFrameResult();

            String[] command = extractMode.getCommand(tempInput.getAbsolutePath(), tempOutput.getAbsolutePath());
            ProcessHelper.runCommand(command);
            BufferedImage image = ImageIO.read(new FileInputStream(tempOutput));
            if (image != null) {
                // 将关键帧转换为字节数组
                try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                    ImageIO.write(image, "jpg", baos);
                    result.setFrameData(baos.toByteArray());
                    result.setWidth(image.getWidth());
                    result.setHeight(image.getHeight());
                    return result;
                }
            }
        } catch (Throwable e) {
            LogUtil.errorf(log, "Failed to extract frame", e);
            throw new RuntimeException(e);
        } finally {
            if (tempInput != null && tempInput.exists()) {
                //noinspection ResultOfMethodCallIgnored
                tempInput.delete();
            }
            if (tempOutput != null && tempOutput.exists()) {
                //noinspection ResultOfMethodCallIgnored
                tempOutput.delete();
            }
        }
        return null;
    }

    public static byte[] sliceVideo(byte[] inputBytes, long startTimestampMicroSecond, long endTimestampMicroSecond)
            throws IOException {

        File input = File.createTempFile("input-", ".mp4");
        Files.write(input.toPath(), inputBytes);

        File output = File.createTempFile("output-", ".mp4");

        DecimalFormat df = new DecimalFormat("#.######");
        double start = startTimestampMicroSecond / 1_000_000.0;
        double end = endTimestampMicroSecond / 1_000_000.0;

        String startStr = df.format(start);
        String endStr = df.format(end);

        // 构建 ffmpeg 命令
        String[] command = {
            "ffmpeg",
            "-ss",
            startStr,
            "-to",
            endStr,
            "-i",
            input.getAbsolutePath(),
            "-c",
            "copy",
            "-y",
            output.getAbsolutePath()
        };

        // 执行 ffmpeg 命令
        ProcessHelper.runCommand(command);
        return Files.readAllBytes(output.toPath());
    }

    @Builder
    @Getter
    public static class MediaSize {
        int width;
        int height;
    }

    public static MediaSize getMediaSize(InputStream inputStream) {
        // 创建临时文件
        File tempFile = null;
        try {
            tempFile = File.createTempFile("input-", ".media");
            try (FileOutputStream out = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
            // 使用 ProcessBuilder 执行 ffprobe 命令行获取媒体大小，输出为 JSON
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffprobe",
                    "-v",
                    "error",
                    "-select_streams",
                    "v:0",
                    "-show_entries",
                    "stream=width,height",
                    "-of",
                    "json",
                    tempFile.getAbsolutePath());
            Process process = processBuilder.start();

            StringBuilder output = new StringBuilder();
            try (Scanner scanner = new Scanner(process.getInputStream())) {
                while (scanner.hasNextLine()) {
                    output.append(scanner.nextLine()).append("\n");
                }
            }

            // 使用 Jackson 解析 JSON 输出以获取宽度和高度
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(output.toString());
            try {

                int width = jsonNode.get("streams").get(0).get("width").asInt();
                int height = jsonNode.get("streams").get(0).get("height").asInt();
                return MediaSize.builder().width(width).height(height).build();
            } catch (NullPointerException e) {
                log.error("Failed to extract media size from ffprobe output: {}", output.toString());
                return null;
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                //noinspection ResultOfMethodCallIgnored
                tempFile.delete();
            }
        }
    }

    public static byte[] cropImage(InputStream inputStream, int x, int y, int width, int height) {
        File tempInput = null;
        File tempOutput = null;
        try {
            // 将输入流保存为临时文件
            tempInput = File.createTempFile("input-", ".media");
            tempOutput = File.createTempFile("output-", ".jpg");

            try (FileOutputStream fos = new FileOutputStream(tempInput)) {
                inputStream.transferTo(fos);
            }

            // 构建 ffmpeg 命令
            String[] command = {
                "ffmpeg",
                "-i",
                tempInput.getAbsolutePath(),
                "-vf",
                String.format("crop=%d:%d:%d:%d", width, height, x, y),
                "-y", // 覆盖输出文件
                tempOutput.getAbsolutePath()
            };

            // 执行 ffmpeg 命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();
            process.waitFor();

            // 读取输出文件
            return Files.readAllBytes(tempOutput.toPath());
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (tempInput != null && tempInput.exists()) {
                //noinspection ResultOfMethodCallIgnored
                tempInput.delete();
            }
            if (tempOutput != null && tempOutput.exists()) {
                //noinspection ResultOfMethodCallIgnored
                tempOutput.delete();
            }
        }
    }

    public static byte[] cropVideo(InputStream inputStream, int x, int y, int width, int height) {
        // 创建临时文件
        File tempInput = null;
        File tempOutput = null;
        try {
            // 创建临时文件
            tempInput = File.createTempFile("input", ".media");
            tempOutput = File.createTempFile("output", ".mp4");

            // 保存输入流到临时文件
            try (FileOutputStream fos = new FileOutputStream(tempInput)) {
                inputStream.transferTo(fos);
            }

            // 构建 ffmpeg 命令
            String[] command = {
                "ffmpeg",
                "-i",
                tempInput.getAbsolutePath(),
                "-vf",
                String.format("crop=%d:%d:%d:%d", width, height, x, y),
                "-c:v",
                "libx264",
                "-y",
                tempOutput.getAbsolutePath()
            };

            // 执行 ffmpeg 命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();

            // 等待进程完成
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("ffmpeg process failed with exit code " + exitCode);
            }

            // 读取输出文件
            return Files.readAllBytes(tempOutput.toPath());
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (tempInput != null && tempInput.exists()) {
                //noinspection ResultOfMethodCallIgnored
                tempInput.delete();
            }
            if (tempOutput != null && tempOutput.exists()) {
                //noinspection ResultOfMethodCallIgnored
                tempOutput.delete();
            }
        }
    }

    /**
     * Validates if the video data is a valid, non-corrupted video
     * @param videoData The video data to validate
     * @return A validation result object containing success/failure status and error message if any
     */
    public static VideoValidationResult validateVideo(byte[] videoData) {
        if (videoData == null || videoData.length == 0) {
            return new VideoValidationResult(false, "Empty video data");
        }

        // Check file size (limit to 500MB)
        if (videoData.length > 500 * 1024 * 1024) {
            return new VideoValidationResult(false, "Video file is too large. Maximum size is 500MB.");
        }

        File tempFile = null;
        try {
            // Create a temporary file to analyze with ffprobe
            tempFile = File.createTempFile("validate-", ".video");
            try (FileOutputStream out = new FileOutputStream(tempFile)) {
                out.write(videoData);
            }

            // Use ffprobe to check if the video is valid and get basic metadata
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffprobe",
                    "-v",
                    "error",
                    "-select_streams",
                    "v:0",
                    "-show_entries",
                    "stream=width,height,codec_name,duration",
                    "-of",
                    "json",
                    tempFile.getAbsolutePath());

            Process process = processBuilder.start();

            // Read the output
            StringBuilder output = new StringBuilder();
            try (Scanner scanner = new Scanner(process.getInputStream())) {
                while (scanner.hasNextLine()) {
                    output.append(scanner.nextLine()).append("\n");
                }
            }

            // Check if ffprobe returned an error
            StringBuilder errorOutput = new StringBuilder();
            try (Scanner scanner = new Scanner(process.getErrorStream())) {
                while (scanner.hasNextLine()) {
                    errorOutput.append(scanner.nextLine()).append("\n");
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                log.error("ffprobe failed with exit code {}: {}", exitCode, errorOutput.toString());
                return new VideoValidationResult(false, "Invalid video file format or corrupted video file.");
            }

            // Parse the JSON output
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(output.toString());

            // Check if we have stream information
            if (!jsonNode.has("streams") || jsonNode.get("streams").size() == 0) {
                return new VideoValidationResult(false, "No video stream found in the file.");
            }

            JsonNode streamNode = jsonNode.get("streams").get(0);

            // Check if we have width and height
            if (!streamNode.has("width") || !streamNode.has("height")) {
                return new VideoValidationResult(false, "Cannot determine video dimensions.");
            }

            int width = streamNode.get("width").asInt();
            int height = streamNode.get("height").asInt();

            // Check for minimum dimensions
            if (width < 16 || height < 16) {
                return new VideoValidationResult(false, "Video is too small. Minimum dimensions are 16x16 pixels.");
            }

            // Check for maximum dimensions (8K)
            if (width > 7680 || height > 4320) {
                return new VideoValidationResult(
                        false, "Video is too large. Maximum dimensions are 7680x4320 pixels (8K).");
            }

            // Check for reasonable aspect ratio
            double aspectRatio = (double) width / height;
            if (aspectRatio < 0.1 || aspectRatio > 10) {
                return new VideoValidationResult(
                        false, "Video has an extreme aspect ratio. Please use a video with more balanced dimensions.");
            }
            float duration = 0;
            // Check if duration is available and reasonable
            if (streamNode.has("duration")) {
                try {
                    duration = (float) streamNode.get("duration").asDouble();
                    if (duration < 0.1) {
                        return new VideoValidationResult(false, "Video is too short (less than 0.1 seconds).");
                    }
                    if (duration > 3600) {
                        return new VideoValidationResult(false, "Video is too long (more than 1 hour).");
                    }
                } catch (Exception e) {
                    log.warn("Could not parse video duration: {}", e.getMessage());
                    // Not critical, continue
                }
            } else {
                log.warn("Could not validate video duration - continuing without duration check");
                return new VideoValidationResult(
                        false, "Video has no duration information, file may be corrupted or invalid.");
            }

            // All checks passed
            return new VideoValidationResult(true, null, width, height, duration);

        } catch (Exception e) {
            log.error("Error validating video", e);
            return new VideoValidationResult(false, "Failed to validate video: " + e.getMessage());
        } finally {
            // Clean up
            if (tempFile != null && tempFile.exists()) {
                //noinspection ResultOfMethodCallIgnored
                tempFile.delete();
            }
        }
    }

    /**
     * Result class for video validation
     */
    @AllArgsConstructor
    @Getter
    public static class VideoValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final int width;
        private final int height;
        private final float duration;

        public VideoValidationResult(boolean valid, String errorMessage) {
            this(valid, errorMessage, 0, 0, 0);
        }
    }

    /**
     * Validates an audio file and extracts metadata
     *
     * @param audioData The raw audio file data
     * @return AudioValidationResult containing validation result and metadata
     */
    public static AudioValidationResult validateAudio(byte[] audioData) {
        try {
            if (audioData == null || audioData.length == 0) {
                return new AudioValidationResult(false, "Empty audio data");
            }

            // Check file size (100MB limit for audio files)
            if (audioData.length > 100 * 1024 * 1024) {
                return new AudioValidationResult(false, "Audio file is too large. Maximum size is 100MB.");
            }

            // Create a temporary file with the audio data
            File tempFile = File.createTempFile("audio", ".tmp");
            tempFile.deleteOnExit();
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(audioData);
            }

            // Use FFmpeg to probe the file and get audio information
            ProcessBuilder pb = new ProcessBuilder(
                    "ffprobe",
                    "-v",
                    "quiet",
                    "-print_format",
                    "json",
                    "-show_format",
                    "-show_streams",
                    tempFile.getAbsolutePath());

            Process process = pb.start();
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                return new AudioValidationResult(false, "Invalid audio file format or corrupted audio file.");
            }

            // Parse the JSON output
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(output.toString());

            // Find audio stream
            JsonNode streamsNode = rootNode.path("streams");
            boolean hasAudioStream = false;
            for (JsonNode streamNode : streamsNode) {
                if ("audio".equals(streamNode.path("codec_type").asText())) {
                    hasAudioStream = true;
                    break;
                }
            }

            if (!hasAudioStream) {
                return new AudioValidationResult(false, "No audio stream found in the file.");
            }

            // Get duration from format
            JsonNode formatNode = rootNode.path("format");
            double duration = 0.0;
            if (!formatNode.path("duration").isMissingNode()) {
                try {
                    duration = Double.parseDouble(formatNode.path("duration").asText());
                } catch (NumberFormatException e) {
                    // If duration parsing fails, set it to 0
                    duration = 0.0;
                }
            }

            // Validate the duration
            if (duration < 0.1) {
                return new AudioValidationResult(false, "Audio is too short (less than 0.1 seconds).");
            }
            if (duration > 3600) { // 1 hour
                return new AudioValidationResult(false, "Audio is too long (more than 1 hour).");
            }

            // Get audio bitrate if available
            String bitrate = formatNode.path("bit_rate").asText("");
            int bitrateValue = 0;
            if (!bitrate.isEmpty()) {
                try {
                    bitrateValue = Integer.parseInt(bitrate);
                } catch (NumberFormatException e) {
                    // If bitrate parsing fails, leave it as 0
                }
            }

            // Get other metadata if needed
            int sampleRate = 0;
            int channels = 0;
            for (JsonNode streamNode : streamsNode) {
                if ("audio".equals(streamNode.path("codec_type").asText())) {
                    // Extract sample rate
                    if (!streamNode.path("sample_rate").isMissingNode()) {
                        try {
                            sampleRate = Integer.parseInt(
                                    streamNode.path("sample_rate").asText());
                        } catch (NumberFormatException e) {
                            // If sample rate parsing fails, leave it as 0
                        }
                    }

                    // Extract channel count
                    if (!streamNode.path("channels").isMissingNode()) {
                        channels = streamNode.path("channels").asInt(0);
                    }
                    break;
                }
            }

            // Cleanup temporary file
            if (!tempFile.delete()) {
                log.warn("Failed to delete temporary audio file: {}", tempFile.getAbsolutePath());
            }

            return new AudioValidationResult(true, null, duration, sampleRate, channels, bitrateValue);

        } catch (Exception e) {
            log.error("Error validating audio file", e);
            return new AudioValidationResult(false, "Failed to validate audio: " + e.getMessage());
        }
    }

    /**
     * Result class for audio validation
     */
    @AllArgsConstructor
    @Getter
    public static class AudioValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final double duration; // in seconds
        private final int sampleRate; // in Hz
        private final int channels; // number of audio channels
        private final int bitrate; // in bits per second

        public AudioValidationResult(boolean valid, String errorMessage) {
            this(valid, errorMessage, 0, 0, 0, 0);
        }
    }
}
