package co.sandai.zeus.common.helper;

import java.nio.charset.StandardCharsets;
import java.util.List;

public class M3U8Helper {
    /**
     * 生成M3U8文件，每个片段使用其对应的持续时间
     *
     * @param durations 每个片段的持续时间列表
     * @param chunkUrls 片段URL列表
     * @param defaultDuration 当持续时间为0或负数时使用的默认持续时间
     * @param end 是否是最终列表
     * @return M3U8文件的字节数组
     */
    public static byte[] generateM3U8File(
            List<Float> durations, List<String> chunkUrls, float defaultDuration, boolean end) {
        StringBuilder stringBuilder = new StringBuilder();

        // 计算目标持续时间（使用最大持续时间值）
        float maxDuration = defaultDuration;
        for (Float duration : durations) {
            if (duration != null && duration > 0) {
                maxDuration = Math.max(maxDuration, duration);
            }
        }
        long targetDuration = (long) Math.ceil(maxDuration);

        // 写入M3U8文件头
        stringBuilder.append("#EXTM3U\n");
        stringBuilder.append("#EXT-X-VERSION:3\n");
        stringBuilder.append(String.format("#EXT-X-TARGETDURATION:%d\n", targetDuration));
        stringBuilder.append("\n");

        // 写入每个视频片段，使用对应的持续时间
        for (int i = 0; i < durations.size(); i++) {
            Float duration = durations.get(i);
            String url = chunkUrls.get(i);

            // 使用持续时间，如果为0或null则使用默认值
            float segmentDuration = (duration != null && duration > 0) ? duration : defaultDuration;

            stringBuilder.append(String.format("#EXTINF:%f, no desc\n", segmentDuration));
            stringBuilder.append(url).append("\n\n");
        }

        if (end) {
            // 写入结束标签
            stringBuilder.append("#EXT-X-ENDLIST\n");
        }

        // 返回字节数组
        return stringBuilder.toString().getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 生成M3U8文件，所有片段使用相同的持续时间
     *
     * @param chunks 片段URL列表
     * @param chunkDuration 每个片段的持续时间
     * @param targetDuration 目标持续时间
     * @param end 是否是最终列表
     * @return M3U8文件的字节数组
     */
    public static byte[] generateM3U8File(List<String> chunks, double chunkDuration, long targetDuration, boolean end) {
        StringBuilder stringBuilder = new StringBuilder();

        // 写入 M3U8 文件头
        stringBuilder.append("#EXTM3U\n");
        stringBuilder.append("#EXT-X-VERSION:3\n");
        stringBuilder.append(String.format("#EXT-X-TARGETDURATION:%d\n", targetDuration));
        stringBuilder.append("\n");

        // 写入每个视频片段
        for (String segment : chunks) {
            stringBuilder.append(String.format("#EXTINF:%f, no desc\n", chunkDuration)); // 每个片段的持续时间（秒）
            stringBuilder.append(segment).append("\n\n");
        }

        if (end) {
            // 写入结束标签
            stringBuilder.append("#EXT-X-ENDLIST\n");
        }

        // 返回字节数组
        return stringBuilder.toString().getBytes(StandardCharsets.UTF_8);
    }
}
