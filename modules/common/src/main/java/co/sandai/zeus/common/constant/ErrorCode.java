package co.sandai.zeus.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ErrorCode {
    // 成功。 1000x
    Success("10000", true, true),
    Redirect("10001", true, true),

    // common。 0000x
    NotLogin("00001"),
    NotPermitted("00002"),
    InvalidParameters("00003", true, true),
    UnSupportedOperation("00004"),
    NotFound("00005"),
    InternalError("00006", false, false),
    ThirdPartyError("00007"),
    DbError("00008", false, false),
    ExceedRateLimits("00009", false, false),
    InvalidRequest("00010", false, false),

    // user. 0010x
    UserProfileUpdateFailed("00100", false, false),
    EmailNotVerified("00101"),
    CaptchaVerifyFailed("00102"),

    // asset. 0020x
    AssetExtNotSupported("00200", true, true),
    AssetIndexRequiredForGenerate("00201"),
    AssetInvalidContent("00202"),
    DuplicateFavorite("00203"),
    AssetTooLarge("00204", true, true),

    // task. 0030x
    TaskNotFound("00300"),

    // credit. 0040x
    InsufficientCredit("00400", true, true),
    NoAvailableCredit("00401", true, true),
    UpgradeRequired("00402", true, true),

    // project. 0060x
    ProjectNotFound("00600"),
    ProjectInvalidStatus("00601"),
    ProjectOperationNotAllowed("00602"),

    // voice. 0070x
    VoiceProviderNotFound("00700"),
    VoiceSpeechToTextFailed("00701"),
    VoiceLanguageDetectionFailed("00702"),

    // external api errors
    ExternalApiError("00800", false, false),
    ;

    private final String code;

    /**
     * 表示业务是否成功。 指的是业务是否按照预期执行
     */
    private boolean success = false;

    /**
     * 表示系统是否成功。 指的是系统是否按照预期执行
     */
    private boolean passed = true;

    ErrorCode(String code) {
        this.code = code;
    }

    ErrorCode(String code, boolean success, boolean passed) {
        this.code = code;
        this.success = success;
        this.passed = passed;
    }

    @JsonValue
    public String getValue() {
        return code;
    }
}
