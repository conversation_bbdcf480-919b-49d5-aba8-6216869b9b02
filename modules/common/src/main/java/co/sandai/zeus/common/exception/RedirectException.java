package co.sandai.zeus.common.exception;

import co.sandai.zeus.common.constant.ErrorCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
public class RedirectException extends ZeusServiceException {

    private String redirectUrl;

    public RedirectException(ErrorCode code, String redirectUrl) {
        super(HttpStatus.SEE_OTHER, code);
        this.redirectUrl = redirectUrl;
    }
}
