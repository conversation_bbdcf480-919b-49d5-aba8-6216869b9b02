package co.sandai.zeus.common.helper;

import co.sandai.zeus.common.utils.TimeUtil;
import java.util.Random;

public class SimpleIdGenerator {
    // 随机数生成器（线程安全）
    private static final Random RANDOM = new Random();

    // 每毫秒的最大随机数范围（根据需求调整）
    private static final int MAX_RANDOM = 10000;

    /**
     * 生成唯一 ID。
     * ID 格式为：当前时间戳（毫秒） + 随机数
     * @return 生成的唯一 ID
     */
    public static synchronized String generateId() {
        long timestamp = System.currentTimeMillis();
        int random = RANDOM.nextInt(MAX_RANDOM);
        return timestamp + String.format("DD%04d", random);
    }

    /**
     * 从生成的 ID 中解析时间戳。
     * @param id 唯一 ID
     * @return 时间戳（毫秒）
     */
    public static long parseTimestamp(String id) {
        String[] dds = id.split("DD");
        return Long.parseLong(dds[0]);
    }

    public static String parseDate(String id) {
        return TimeUtil.formatMillis(parseTimestamp(id));
    }

    public static void main(String[] args) {
        String id = generateId();
        System.out.println("生成的 ID：" + id);
        System.out.println("解析的时间戳：" + parseTimestamp(id));
        System.out.println("解析的日期：" + parseDate(id));
    }
}
