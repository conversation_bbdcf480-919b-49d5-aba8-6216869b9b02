package co.sandai.zeus.common.web;

import co.sandai.zeus.common.log.trace.TraceContext;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.springframework.stereotype.Component;

@Component
public class TraceIdFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // Initialization logic if needed
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        httpServletResponse.setHeader("traceId", TraceContext.getTraceId());
        chain.doFilter(request, response);
        TraceContext.clear();
    }

    @Override
    public void destroy() {
        // Cleanup logic if needed
    }
}
