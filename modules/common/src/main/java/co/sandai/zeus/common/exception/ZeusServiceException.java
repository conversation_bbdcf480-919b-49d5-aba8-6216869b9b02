package co.sandai.zeus.common.exception;

import static co.sandai.zeus.common.constant.ErrorCode.NotFound;

import co.sandai.zeus.common.constant.ErrorCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
public class ZeusServiceException extends RuntimeException {

    public ErrorCode code;

    public HttpStatus httpStatus = HttpStatus.BAD_REQUEST;

    public ZeusServiceException(ErrorCode code, String message) {
        super(message);
        this.code = code;
    }

    public ZeusServiceException(ErrorCode code) {
        super("");
        this.code = code;
    }

    public ZeusServiceException(ErrorCode code, Throwable cause) {
        super(cause);
        this.code = code;
    }

    public ZeusServiceException(HttpStatus httpStatus, ErrorCode code) {
        super();
        this.code = code;
        this.httpStatus = httpStatus;
    }

    public ZeusServiceException(HttpStatus httpStatus, ErrorCode code, String message) {
        super(message);
        this.code = code;
        this.httpStatus = httpStatus;
    }

    public ZeusServiceException(HttpStatus httpStatus, ErrorCode code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.httpStatus = httpStatus;
    }

    public ZeusServiceException(HttpStatus httpStatus, ErrorCode errorCode, Throwable cause) {
        super(cause);
        this.code = errorCode;
        this.httpStatus = httpStatus;
    }

    public ZeusServiceException httpStatus(HttpStatus httpStatus) {
        this.httpStatus = httpStatus;
        return this;
    }

    public ZeusServiceException code(ErrorCode code) {
        this.code = code;
        return this;
    }

    public static ZeusServiceException notFound(ErrorCode code, String message) {
        return new ZeusServiceException(HttpStatus.NOT_FOUND, code, message);
    }

    public static ZeusServiceException notFound(String message) {
        return new ZeusServiceException(HttpStatus.NOT_FOUND, NotFound, message);
    }

    public static ZeusServiceException forbidden(String message) {
        return new ZeusServiceException(HttpStatus.FORBIDDEN, ErrorCode.NotPermitted, message);
    }

    public static ZeusServiceException internalError(String message) {
        return new ZeusServiceException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorCode.InternalError, message);
    }

    public static ZeusServiceException internalError(String message, ErrorCode code) {
        return new ZeusServiceException(HttpStatus.INTERNAL_SERVER_ERROR, code, message);
    }

    public static ZeusServiceException internalError(String message, Throwable t) {
        return new ZeusServiceException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorCode.InternalError, message, t);
    }

    public static ZeusServiceException internalError(String message, ErrorCode errorCode, Throwable t) {
        return new ZeusServiceException(HttpStatus.INTERNAL_SERVER_ERROR, errorCode, message, t);
    }

    public static ZeusServiceException badRequest(String message) {
        return new ZeusServiceException(HttpStatus.BAD_REQUEST, ErrorCode.InvalidParameters, message);
    }

    public static ZeusServiceException badRequest(Throwable cause) {
        return new ZeusServiceException(HttpStatus.BAD_REQUEST, ErrorCode.InvalidParameters, cause);
    }

    public static ZeusServiceException badRequest(String message, Throwable t) {
        return new ZeusServiceException(HttpStatus.BAD_REQUEST, ErrorCode.InvalidParameters, message, t);
    }

    public static ZeusServiceException tooManyRequests(int rpm) {
        String message = String.format("Too many requests, please try again later, max RPM is: %d", rpm);
        return new ZeusServiceException(HttpStatus.TOO_MANY_REQUESTS, ErrorCode.ExceedRateLimits, message);
    }

    public static ZeusServiceException upgradeRequired() {
        return new ZeusServiceException(
                HttpStatus.BAD_REQUEST, ErrorCode.UpgradeRequired, "This operation requires an upgraded plan.");
    }
}
