package co.sandai.zeus.common.helper;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExtractModeEnum {
    KEY_FRAME(((intputPath, outputPath) -> new String[] {
        "ffmpeg",
        "-i",
        intputPath,
        "-vf",
        "select=eq(pict_type\\,I)",
        "-frames:v",
        "1",
        "-f",
        "image2",
        "-y",
        outputPath,
    })),
    TAIL_FRAME((intputPath, outputPath) -> {
        return new String[] {
            "ffmpeg",
            "-sseof",
            // 提取倒数0.1s的帧
            "-0.1",
            "-i",
            intputPath,
            "-frames:v",
            "1",
            "-c:v",
            "mjpeg",
            "-strict",
            "unofficial",
            "-q:v",
            "2",
            "-update",
            "1",
            "-f",
            "image2",
            "-y",
            outputPath
        };
    }),
    ;

    private CommandSupplier supplier;

    private interface CommandSupplier {
        String[] getCommand(String intputPath, String outputPath);
    }

    public String[] getCommand(String intputPath, String outputPath) {
        return supplier.getCommand(intputPath, outputPath);
    }
}
