package co.sandai.zeus.common.utils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class PriceUtil {

    private static final Map<String, Integer> currencySubunits = new HashMap<>();

    static {
        currencySubunits.put("USD", 100);
        currencySubunits.put("EUR", 100);
        currencySubunits.put("JPY", 1);
        currencySubunits.put("KWD", 1000);
        // Add other currencies as needed
    }

    public static String format(long priceInCents, String currency) {
        int subunit = currencySubunits.getOrDefault(currency, 100);
        BigDecimal price = BigDecimal.valueOf(priceInCents).divide(BigDecimal.valueOf(subunit));
        return price.toString();
    }
}
