package co.sandai.zeus.common.log.digest;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.principal.PrincipalContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Aspect
@Component
@Slf4j
public class LogAspect {

    private static final Logger digestLogger = LoggerFactory.getLogger("DigestLogger");
    private static final Logger clientDigestLogger = LoggerFactory.getLogger("ClientDigestLogger");
    private static final Logger errorLogger = LoggerFactory.getLogger("ErrorLogger");

    @Resource
    private PrincipalContext principalContext;

    private static boolean isServletRelated(Object value) {
        if (value == null) return false;
        String className = value.getClass().getName();
        return value instanceof jakarta.servlet.ServletRequest
                || value instanceof jakarta.servlet.ServletResponse
                || className.contains("jakarta.servlet")
                || className.contains("javax.servlet")
                || className.contains("org.springframework.web.context.request.async.StandardServletAsyncWebRequest")
                || className.contains("org.springframework.security.web")
                || className.contains("org.springframework.session.web")
                || className.contains("org.springframework.web.filter")
                || className.contains("org.apache.catalina");
    }

    private static final PropertyFilter filter = (object, name, value) -> {
        // 过滤 MultipartFile 的某些字段
        if (value instanceof MultipartFile) {
            return false;
        }
        return !isServletRelated(value);
    };

    @Pointcut("@annotation(printlog)")
    public void logPointCut(PrintLog printlog) {}

    /**
     * print digest log and handle exception
     */
    @Around("logPointCut(printlog)")
    public Object logInputOutput(ProceedingJoinPoint joinPoint, PrintLog printlog) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result;
        try {
            result = joinPoint.proceed();
            long elapsedTime = System.currentTimeMillis() - startTime;

            DigestInfo info = getDigestInfo(joinPoint, elapsedTime, result, null);

            doLog(info, null, printlog);
        } catch (ZeusServiceException zeusEx) {
            long elapsedTime = System.currentTimeMillis() - startTime;

            DigestInfo info = getDigestInfo(joinPoint, elapsedTime, null, zeusEx);
            doLog(info, zeusEx, printlog);
            // 继续向上抛出，让 ServiceExceptionHandler 处理一下
            throw zeusEx;
        } catch (Throwable t) {
            long elapsedTime = System.currentTimeMillis() - startTime;

            DigestInfo info = getDigestInfo(joinPoint, elapsedTime, null, t);
            doLog(info, t, printlog);
            // 继续向上抛出
            throw t;
        }
        return result;
    }

    private DigestInfo getDigestInfo(ProceedingJoinPoint joinPoint, long elapsedTime, Object result, Throwable o) {
        DigestInfo info = new DigestInfo();
        if (o == null) {
            info.setSuccess(true);
            info.setPassed(true);
        } else if (o instanceof ZeusServiceException ex) {
            ErrorCode code = ex.getCode();
            info.setSuccess(code != null && code.isSuccess());
            info.setPassed(code != null && code.isPassed());
            if (code != null) {
                info.setResultCode(code.getCode());
            }
        } else {
            info.setSuccess(false);
            info.setPassed(false);
        }
        info.setElapsedTime(elapsedTime);
        info.setFrom("");
        info.setPressureTest(false);
        info.getContextExtraInfo().put("principalId", principalContext.getPrincipalId());
        info.getContextExtraInfo().put("principalType", principalContext.getPrincipalType());
        info.setLogger(joinPoint.getSignature().getDeclaringTypeName());
        info.setMethod(joinPoint.getSignature().getName());

        info.setArgs(getArgsMap(joinPoint));

        if (o == null) {
            info.setResult(JSON.toJSONString(result, filter));
        } else {
            // 如果抛了异常，把方法的结果返回一下
            info.setResult(o.getMessage());
        }
        info.setBizExtraInfo(null);
        return info;
    }

    private Map<String, Object> getArgsMap(ProceedingJoinPoint joinPoint) {
        Map<String, Object> argsMap = new LinkedHashMap<>();
        String[] parameterNames =
                ((org.aspectj.lang.reflect.CodeSignature) joinPoint.getSignature()).getParameterNames();
        Object[] args = joinPoint.getArgs();
        for (int i = 0; i < parameterNames.length; i++) {
            Object value = args[i];
            if (value instanceof HttpServletRequest) {
                // 特殊处理一下，直接序列化会抛异常。 有需要打印的参数，放到attribute的param里面
                value = ((HttpServletRequest) value).getAttribute("param");
            }
            if (isServletRelated(value)) {
                value = value.getClass().getSimpleName();
            }
            argsMap.put(parameterNames[i], value);
        }
        return argsMap;
    }

    @Data
    public static class DigestInfo {
        private boolean success;
        private boolean passed;
        private long elapsedTime;
        /**
         * ErrorCode
         */
        private String resultCode;
        /**
         * 来源端
         */
        private String from;

        private boolean pressureTest = false;

        private Map<String, Object> contextExtraInfo = new LinkedHashMap<>();

        /**
         * 类名
         */
        private String logger;
        /**
         * 方法名
         */
        private String method;

        /**
         * 入参
         */
        private Map<String, Object> args;

        /**
         * 返回
         */
        private String result;

        private Map<String, Object> bizExtraInfo = new LinkedHashMap<>();
    }

    private static void doLog(DigestInfo info, Throwable t, PrintLog printlog) {
        try {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(info.elapsedTime);
            stringBuilder.append("`|`").append(convert(info.success));
            stringBuilder.append("`|`").append(convert(info.passed));
            stringBuilder.append("`|`").append(convert(info.resultCode));
            stringBuilder.append("`|`").append(convert(info.from));
            stringBuilder.append("`|`").append(convert(info.pressureTest));
            stringBuilder.append("`|`").append(convert(info.contextExtraInfo));
            stringBuilder.append("`|`").append(convert(info.logger));
            stringBuilder.append("`|`").append(convert(info.method));
            stringBuilder.append("`|`").append("request=").append(convert(info.args));
            stringBuilder.append("`|`").append("result=").append(convert(info.result));
            stringBuilder.append("`|`").append(convert(info.bizExtraInfo));
            Logger logger;
            if (printlog.fileType() == PrintLog.TypeEnum.CLIENT_DIGEST) {
                logger = clientDigestLogger;
            } else {
                logger = digestLogger;
            }
            if (t != null && (!info.success || !info.passed)) {
                logger.info(stringBuilder.toString(), t);
            } else {
                logger.info(stringBuilder.toString());
            }

            if (t != null && (!info.success || !info.passed)) {
                // 真的错误的情况下才打印错误日志
                if (t instanceof ZeusServiceException zeusEx) {
                    ErrorCode code = zeusEx.getCode();
                    LogUtil.errorf(
                            errorLogger,
                            "defined exception, httpCode={0}, errorCode={1}",
                            zeusEx,
                            zeusEx.getHttpStatus(),
                            code);
                } else {
                    errorLogger.error(t.getMessage(), t);
                }
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
    }

    private static char convert(boolean success) {
        return success ? 'Y' : 'N';
    }

    private static String convert(String from) {
        return StringUtils.isBlank(from) ? "-" : from;
    }

    private static Object ensureStringKeyMap(Object obj) {
        if (obj instanceof Map<?, ?> map) {
            Map<String, Object> result = new LinkedHashMap<>();
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                String key = String.valueOf(entry.getKey());
                result.put(key, ensureStringKeyMap(entry.getValue()));
            }
            return result;
        } else if (obj instanceof Iterable<?>) {
            List<Object> list = new ArrayList<>();
            for (Object item : (Iterable<?>) obj) {
                list.add(ensureStringKeyMap(item));
            }
            return list;
        }
        return obj;
    }

    private static String convert(Map<String, Object> map) {
        if (map == null) return "{}";
        Object safeMap = ensureStringKeyMap(map);
        return JSON.toJSONString(safeMap, filter);
    }
}
