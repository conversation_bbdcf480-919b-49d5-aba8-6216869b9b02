package co.sandai.zeus.common.utils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 规范： 所有时间处理都使用本类，不直接在外部处理，以避免：
 *  1. 时区问题
 *  2. 时钟测试问题
 */
public class TimeUtil {

    /**
     * 获取当前UTC时间
     *
     * @return
     */
    public static LocalDateTime utcTime() {
        return LocalDateTime.now(ZoneOffset.UTC);
    }

    /**
     * 获取当前utc时间的TimeStamp类型值
     */
    public static Timestamp utcTimeTimeStamp() {
        return Timestamp.valueOf(utcTime());
    }

    public static LocalDateTime addMonths(LocalDateTime time, int months) {
        return time.plusMonths(months);
    }

    public static LocalDateTime addYears(LocalDateTime time, int years) {
        return time.plusMonths(years);
    }

    public static Timestamp addYears(Timestamp time, int years) {
        LocalDateTime localDateTime = time.toLocalDateTime().plusYears(years);
        return Timestamp.valueOf(localDateTime);
    }

    public static Timestamp addMonths(Timestamp time, int months) {
        LocalDateTime localDateTime = time.toLocalDateTime().plusMonths(months);
        return Timestamp.valueOf(localDateTime);
    }

    // 按照YYYY-MM-DD HH:MM:SS格式化时间
    public static String format(LocalDateTime time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return time.format(formatter);
    }

    // 按照YYYY-MM-DD HH:MM:SS格式化时间
    public static String format(Timestamp time) {
        return format(time.toLocalDateTime());
    }

    // to epoch
    public static long toEpochMilli(LocalDateTime time) {
        return time.toInstant(ZoneOffset.UTC).toEpochMilli();
    }

    public static LocalDateTime from(long timeStampInSeconds) {
        return LocalDateTime.ofEpochSecond(timeStampInSeconds, 0, ZoneOffset.UTC);
    }

    public static Timestamp toTimeStamp(LocalDateTime time) {
        return Timestamp.valueOf(time);
    }

    public static boolean isSameMinute(LocalDateTime time1, LocalDateTime time2) {
        return Math.abs(time1.toEpochSecond(ZoneOffset.UTC) - time2.toEpochSecond(ZoneOffset.UTC)) < 60;
    }

    public static boolean isSameMinute(java.sql.Timestamp time1, java.sql.Timestamp time2) {
        return Math.abs(time1.toInstant().getEpochSecond() - time2.toInstant().getEpochSecond()) < 60;
    }

    public static Timestamp getNextMonthStartTimestamp(LocalDateTime at) {
        return Timestamp.valueOf(
                at.plusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0));
    }

    public static boolean isExpire(Timestamp expireTime) {
        return expireTime.toInstant().getEpochSecond() < utcTime().toEpochSecond(ZoneOffset.UTC);
    }

    /**
     * 返回yyyy-MM-dd HH:mm:ss格式的时间
     * @param timeStamp
     * @return
     */
    public static String formatSeconds(Long timeStamp) {
        return format(from(timeStamp));
    }

    /**
     * 返回yyyy-MM-dd HH:mm:ss,SSS格式的时间
     * @param timeStamp
     * @return
     */
    public static String formatMillis(Long timeStamp) {
        LocalDateTime localDateTime =
                LocalDateTime.ofEpochSecond(timeStamp / 1000, (int) ((timeStamp % 1000) * 1000000), ZoneOffset.UTC);
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss,SSS"));
    }

    /**
     * 判断是否过期
     * @param date
     * @param expireTime 单位ms
     * @return
     */
    public static boolean isExpire(Date date, long expireTime) {
        if (date == null) {
            return true;
        }
        long currentTime = System.currentTimeMillis();
        long inputTime = date.getTime();
        return currentTime - inputTime > expireTime;
    }
}
