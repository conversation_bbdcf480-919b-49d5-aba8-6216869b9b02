package co.sandai.zeus.common.log.digest;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface PrintLog {

    TypeEnum fileType() default TypeEnum.DIGEST;

    public static enum TypeEnum {
        DIGEST,
        CLIENT_DIGEST,
        ;
    }
}
