package co.sandai.zeus.common.log.trace;

import co.sandai.zeus.common.helper.SimpleIdGenerator;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanContext;
import io.opentelemetry.api.trace.TraceId;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class TraceContext {
    private static final ThreadLocal<String> traceIdHolder = new ThreadLocal<>();

    public static String getTraceId() {
        String traceId = traceIdHolder.get();
        if (traceId == null) {
            traceId = generateTraceId();
            traceIdHolder.set(traceId);
        }
        return traceId;
    }

    public static void setTraceId(String traceId) {
        traceIdHolder.set(traceId);
    }

    //    //todo panhong 2024/12/18 接口切面、定时任务、webhook里需要在接口返回前、打印日志后清理traceId
    public static void clear() {
        traceIdHolder.remove();
    }

    private static String generateTraceId() {
        String traceId = Optional.ofNullable(Span.current())
                .map(Span::getSpanContext)
                .map(SpanContext::getTraceId)
                .orElse(TraceId.getInvalid());
        if (traceId.equals(TraceId.getInvalid())) {
            return SimpleIdGenerator.generateId();
        } else {
            return traceId;
        }
    }

    public static void switchTraceId() {
        traceIdHolder.set(generateTraceId());
    }
}
