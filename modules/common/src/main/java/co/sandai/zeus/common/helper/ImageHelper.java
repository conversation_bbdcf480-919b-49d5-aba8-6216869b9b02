package co.sandai.zeus.common.helper;

import com.drew.imaging.ImageMetadataReader;
import com.drew.metadata.Metadata;
import com.drew.metadata.exif.ExifIFD0Directory;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import javax.imageio.ImageIO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ImageHelper {
    public static boolean needRotate(byte[] imageData) {
        int orientation = getExifOrientation(imageData);
        log.info("EXIF orientation: {}", orientation);
        return orientation != 1;
    }

    /**
     * Rotate image according to EXIF orientation information
     * @param imageData Original image data
     * @return Rotated image data, returns original image if rotation is not needed
     */
    public static byte[] tryFixImageOrientation(byte[] imageData) {
        try {
            int orientation = getExifOrientation(imageData);
            if (orientation == 1) {
                return imageData;
            }

            log.info("Image rotation needed, orientation value: {}", orientation);

            int angle = 0;
            switch (orientation) {
                case 3: // 180 degrees
                    angle = 180;
                    break;
                case 6: // 顺时针旋转90度
                    angle = 90;
                    break;
                case 8: // 逆时针旋转90度
                    angle = 270;
                    break;
                default:
                    return imageData; // 不支持的旋转类型，返回原始数据
            }

            // 加载图片
            ByteArrayInputStream inputStream = new ByteArrayInputStream(imageData);
            BufferedImage originalImage = ImageIO.read(inputStream);
            if (originalImage == null) {
                log.warn("Failed to load image data");
                return imageData;
            }

            // 旋转图片
            log.info("Starting to rotate image, angle: {} degrees", angle);
            BufferedImage rotatedImage = Rotate(originalImage, angle);

            // 转换回字节数组
            java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream();
            ImageIO.write(rotatedImage, "jpg", outputStream);

            // 释放资源
            if (rotatedImage.getGraphics() != null) {
                rotatedImage.getGraphics().dispose();
            }
            if (originalImage.getGraphics() != null) {
                originalImage.getGraphics().dispose();
            }

            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("Error occurred while rotating image", e);
            return imageData; // 出错时返回原始数据
        }
    }

    /**
     * 获取图片的EXIF旋转值
     * @param imageData 图片字节数组
     * @return EXIF orientation值，1表示无旋转
     */
    public static int getExifOrientation(byte[] imageData) {
        // 读取原始图片和元数据
        ByteArrayInputStream inputStream = new ByteArrayInputStream(imageData);
        int orientation = 1; // 默认为1，表示无旋转

        try {
            Metadata metadata = ImageMetadataReader.readMetadata(inputStream);
            ExifIFD0Directory exifDir = metadata.getFirstDirectoryOfType(ExifIFD0Directory.class);
            if (exifDir != null && exifDir.containsTag(ExifIFD0Directory.TAG_ORIENTATION)) {
                orientation = exifDir.getInt(ExifIFD0Directory.TAG_ORIENTATION);
            }
        } catch (Exception e) {
            log.warn("Failed to read EXIF orientation: {}", e.getMessage());
        }

        return orientation;
    }

    public static BufferedImage Rotate(Image src, int angel) {
        int srcWidth = src.getWidth(null);
        int srcHeight = src.getHeight(null);
        // 计算旋转后图片的尺寸
        Rectangle rect_des = CalcRotatedSize(new Rectangle(new Dimension(srcWidth, srcHeight)), angel);
        BufferedImage res = null;
        res = new BufferedImage(rect_des.width, rect_des.height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = res.createGraphics();
        // 进行转换
        g2.translate((rect_des.width - srcWidth) / 2, (rect_des.height - srcHeight) / 2);
        g2.rotate(Math.toRadians(angel), srcWidth / 2, srcHeight / 2);

        g2.drawImage(src, null, null);
        return res;
    }

    /**
     * 计算旋转后的图片
     *
     * @param src   被旋转的图片
     * @param angel 旋转角度
     * @return 旋转后的图片
     */
    public static Rectangle CalcRotatedSize(Rectangle src, int angel) {
        // 如果旋转的角度大于90度做相应的转换
        if (angel >= 90) {
            if (angel / 90 % 2 == 1) {
                int temp = src.height;
                src.height = src.width;
                src.width = temp;
            }
            angel = angel % 90;
        }

        double r = Math.sqrt(src.height * src.height + src.width * src.width) / 2;
        double len = 2 * Math.sin(Math.toRadians(angel) / 2) * r;
        double angelAlpha = (Math.PI - Math.toRadians(angel)) / 2;
        double angelDeltaWidth = Math.atan((double) src.height / src.width);
        double angelDeltaHeight = Math.atan((double) src.width / src.height);

        int lenDeltaWidth = (int) (len * Math.cos(Math.PI - angelAlpha - angelDeltaWidth));
        int lenDeltaHeight = (int) (len * Math.cos(Math.PI - angelAlpha - angelDeltaHeight));
        int desWidth = src.width + lenDeltaWidth * 2;
        int desHeight = src.height + lenDeltaHeight * 2;
        return new Rectangle(new Dimension(desWidth, desHeight));
    }

    /**
     * Validates if the image data is a valid, non-corrupted image
     * @param imageData The image data to validate
     * @return A validation result object containing success/failure status and error message if any
     */
    public static ImageValidationResult validateImage(byte[] imageData) {
        if (imageData == null || imageData.length == 0) {
            return new ImageValidationResult(false, "Empty image data");
        }

        try {
            // Try to decode the image to verify it's not corrupted
            ByteArrayInputStream inputStream = new ByteArrayInputStream(imageData);
            BufferedImage image = ImageIO.read(inputStream);

            if (image == null) {
                return new ImageValidationResult(
                        false, "Cannot decode image. The file appears to be corrupted or in an unsupported format.");
            }

            // Check image dimensions
            int width = image.getWidth();
            int height = image.getHeight();

            // Check for minimum dimensions
            if (width < 16 || height < 16) {
                return new ImageValidationResult(
                        false, "Image is too small. Minimum dimensions are 16x16 pixels.", width, height);
            }

            // Check for maximum dimensions
            if (width > 10000 || height > 10000) {
                return new ImageValidationResult(
                        false, "Image is too large. Maximum dimensions are 10000x10000 pixels.", width, height);
            }

            // Check for reasonable aspect ratio (e.g., not 1x10000)
            double aspectRatio = (double) width / height;
            if (aspectRatio < 0.01 || aspectRatio > 100) {
                return new ImageValidationResult(
                        false,
                        "Image has an extreme aspect ratio. Please use an image with more balanced dimensions.",
                        width,
                        height);
            }

            return new ImageValidationResult(true, null, width, height);

        } catch (Exception e) {
            log.error("Error validating image", e);
            return new ImageValidationResult(false, "Failed to validate image: " + e.getMessage());
        }
    }

    /**
     * Result class for image validation
     */
    @AllArgsConstructor
    @Getter
    public static class ImageValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final int width;
        private final int height;

        public ImageValidationResult(boolean valid, String errorMessage) {
            this(valid, errorMessage, 0, 0);
        }
    }

    public static void main(String[] args) {
        try {
            // 假设 imageBytes 是从文件或其他来源获取的字节数组
            byte[] imageBytes = java.nio.file.Files.readAllBytes(
                    java.nio.file.Paths.get("/Users/<USER>/Downloads/2025 Art.jpg"));
            //            byte[] imageBytes =
            //
            // java.nio.file.Files.readAllBytes(java.nio.file.Paths.get("/Users/<USER>/Downloads/凉宫1.webp"));
            boolean needRotate = needRotate(imageBytes);
            System.out.println(needRotate);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
