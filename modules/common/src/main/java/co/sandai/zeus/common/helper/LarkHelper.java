package co.sandai.zeus.common.helper;

import java.time.Duration;
import java.util.HashMap;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

public class LarkHelper {
    public static boolean sendLarkBotMessage(String webhook, String message) {
        HashMap<String, Object> content = new HashMap<>();
        content.put("text", message);

        HashMap<String, Object> data = new HashMap<>();
        data.put("msg_type", "text");
        data.put("content", content);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        RestTemplate restTemplate = new RestTemplateBuilder()
                .setReadTimeout(Duration.ofSeconds(60))
                .setConnectTimeout(Duration.ofSeconds(60))
                .build();

        HttpEntity<HashMap<String, Object>> req = new HttpEntity<>(data, headers);

        //noinspection rawtypes
        ResponseEntity<HashMap> res = restTemplate.postForEntity(webhook, req, HashMap.class);

        return res.getStatusCode().is2xxSuccessful();
    }
}
