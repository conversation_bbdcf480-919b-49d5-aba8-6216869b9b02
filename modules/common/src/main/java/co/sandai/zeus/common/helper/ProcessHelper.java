package co.sandai.zeus.common.helper;

import co.sandai.zeus.common.log.LogUtil;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ProcessHelper {
    public static void runCommand(String[] command) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();
            StringBuilder errorOutput = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    errorOutput.append(line).append("\n");
                }
            }
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("process failed with exit code " + exitCode + errorOutput);
            }
            LogUtil.debugf(
                    log,
                    "process finished, errorOutput={0}, command={1}",
                    errorOutput,
                    StringUtils.joinWith(" ", (Object[]) command));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
