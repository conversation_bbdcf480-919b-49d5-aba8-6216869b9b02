package co.sandai.zeus.common.helper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for normalizing audio loudness
 * Supports both MP3 and WAV formats using FFmpeg
 */
@Slf4j
public class AudioHelper {

    private static final float TARGET_LOUDNESS_DB = -16.0f; // Standard target loudness (LUFS approximation)
    private static final String TEMP_DIR_PREFIX = "audio_normalizer_";
    private static final int FFMPEG_PROCESS_TIMEOUT_SECONDS = 30;

    /**
     * Normalize the loudness of an audio file (supports both MP3 and WAV)
     *
     * @param audioData The raw audio data as byte array
     * @return Normalized audio data as byte array
     * @throws IOException If an I/O error occurs during audio processing
     */
    public static byte[] normalizeLoudness(byte[] audioData) throws IOException {
        if (audioData == null || audioData.length == 0) {
            log.warn("Empty audio data provided");
            return audioData;
        }

        try {
            log.debug("Using FFmpeg for audio normalization");
            return normalizeUsingFFmpeg(audioData);
        } catch (Exception e) {
            log.error("FFmpeg normalization failed: {}", e.getMessage());
            return audioData; // Return original data on failure
        }
    }

    /**
     * Check if FFmpeg is available on the system
     */
    private static boolean isFFmpegAvailable() {
        try {
            ProcessBuilder pb = new ProcessBuilder("ffmpeg", "-version");
            pb.redirectErrorStream(true);
            Process process = pb.start();
            boolean completed = process.waitFor(FFMPEG_PROCESS_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            if (!completed) {
                process.destroyForcibly();
                log.warn("FFmpeg version check timed out");
                return false;
            }

            int exitCode = process.exitValue();
            if (exitCode == 0) {
                log.debug("FFmpeg is available");
                return true;
            } else {
                log.warn("FFmpeg check returned non-zero exit code: {}", exitCode);
                return false;
            }
        } catch (IOException | InterruptedException e) {
            log.warn("FFmpeg not available: {}", e.getMessage());
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            return false;
        }
    }

    /**
     * Normalize audio using FFmpeg
     */
    private static byte[] normalizeUsingFFmpeg(byte[] audioData) throws IOException {
        Path tempDir = null;
        try {
            // Create temporary directory for processing
            tempDir = Files.createTempDirectory(TEMP_DIR_PREFIX);
            Path inputFile = tempDir.resolve("input_audio");
            Path outputFile = tempDir.resolve("output_audio");

            // Write input data to temporary file
            Files.write(inputFile, audioData);

            // Detect audio format
            String format = detectAudioFormat(inputFile);
            log.debug("Detected audio format: {}", format);

            // Build FFmpeg command for loudness normalization with compression
            ProcessBuilder pb;

            if ("mp3".equals(format)) {
                // MP3 format - add bitrate control
                pb = new ProcessBuilder(
                        "ffmpeg",
                        "-i",
                        inputFile.toString(),
                        "-af",
                        "loudnorm=I=" + TARGET_LOUDNESS_DB + ":LRA=11:TP=-1.5",
                        "-codec:a",
                        "libmp3lame",
                        "-b:a",
                        "128k", // Set output bitrate to 128kbps (adjust as needed)
                        "-f",
                        format,
                        "-y", // Overwrite output file
                        outputFile.toString());
            } else if ("wav".equals(format)) {
                // WAV format - use compression
                pb = new ProcessBuilder(
                        "ffmpeg",
                        "-i",
                        inputFile.toString(),
                        "-af",
                        "loudnorm=I=" + TARGET_LOUDNESS_DB + ":LRA=11:TP=-1.5",
                        "-codec:a",
                        "pcm_s16le", // Use 16-bit PCM
                        "-ar",
                        "44100", // Set sample rate
                        "-f",
                        format,
                        "-y", // Overwrite output file
                        outputFile.toString());
            } else {
                // Generic format
                pb = new ProcessBuilder(
                        "ffmpeg",
                        "-i",
                        inputFile.toString(),
                        "-af",
                        "loudnorm=I=" + TARGET_LOUDNESS_DB + ":LRA=11:TP=-1.5",
                        "-f",
                        format,
                        "-y", // Overwrite output file
                        outputFile.toString());
            }

            // Combine standard error with standard output for logging
            pb.redirectErrorStream(true);

            // Start the process
            Process process = pb.start();

            // Log FFmpeg output
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // Wait for process completion
            boolean completed = process.waitFor(FFMPEG_PROCESS_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (!completed) {
                process.destroyForcibly();
                log.error("FFmpeg process timed out after {} seconds", FFMPEG_PROCESS_TIMEOUT_SECONDS);
                return audioData; // Return original on timeout
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                log.error("FFmpeg processing failed (exit code: {}): {}", exitCode, output);
                return audioData; // Return original data on failure
            }

            // Read processed data
            return Files.readAllBytes(outputFile);
        } catch (InterruptedException e) {
            log.error("FFmpeg processing was interrupted", e);
            Thread.currentThread().interrupt();
            return audioData;
        } finally {
            // Clean up temporary files
            if (tempDir != null) {
                deleteDirectory(tempDir);
            }
        }
    }

    /**
     * Detect audio format using FFprobe
     */
    private static String detectAudioFormat(Path audioFile) throws IOException {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    "ffprobe",
                    "-v",
                    "error",
                    "-select_streams",
                    "a:0",
                    "-show_entries",
                    "stream=codec_name",
                    "-of",
                    "csv=p=0",
                    audioFile.toString());

            Process process = pb.start();

            // Read output
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }
            }

            boolean completed = process.waitFor(FFMPEG_PROCESS_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (!completed) {
                process.destroyForcibly();
                log.warn("FFprobe format detection timed out");
                return "mp3"; // Default to mp3 on timeout
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                log.warn("FFprobe format detection failed (exit code: {})", exitCode);
                return "mp3"; // Default to mp3 on error
            }

            // Map codec to container format
            String codec = output.toString().trim().toLowerCase();
            log.debug("Detected codec: {}", codec);

            switch (codec) {
                case "pcm_s16le":
                case "pcm_f32le":
                case "pcm_s24le":
                    return "wav";
                case "mp3":
                case "libmp3lame":
                    return "mp3";
                default:
                    // For other formats, add mappings as needed
                    log.debug("Unknown codec '{}', defaulting to mp3", codec);
                    return "mp3";
            }
        } catch (InterruptedException e) {
            log.warn("FFprobe format detection was interrupted");
            Thread.currentThread().interrupt();
            return "mp3"; // Default to mp3
        }
    }

    /**
     * Delete directory and all contents recursively
     */
    private static void deleteDirectory(Path directory) {
        try {
            if (Files.isDirectory(directory)) {
                try (Stream<Path> paths = Files.list(directory)) {
                    paths.forEach(path -> {
                        try {
                            if (Files.isDirectory(path)) {
                                deleteDirectory(path);
                            } else {
                                Files.delete(path);
                            }
                        } catch (IOException e) {
                            log.warn("Failed to delete file: {}", path, e);
                        }
                    });
                }
            }
            Files.delete(directory);
        } catch (IOException e) {
            log.warn("Failed to delete directory: {}", directory, e);
        }
    }

    // Java Sound API methods removed as we now exclusively use FFmpeg for audio normalization
}
