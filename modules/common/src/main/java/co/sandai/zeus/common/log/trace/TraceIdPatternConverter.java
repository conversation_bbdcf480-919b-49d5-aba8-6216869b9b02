package co.sandai.zeus.common.log.trace;

import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.pattern.ConverterKeys;
import org.apache.logging.log4j.core.pattern.LogEventPatternConverter;
import org.apache.logging.log4j.core.pattern.PatternConverter;

/**
 * 添加traceId到日志的pattern
 **/
@Plugin(name = "TraceIdPatternConverter", category = PatternConverter.CATEGORY)
@ConverterKeys({"traceId"})
public class TraceIdPatternConverter extends LogEventPatternConverter {
    private static final String KEY_NAME = "traceId";

    private static final TraceIdPatternConverter INSTANCE = new TraceIdPatternConverter();

    public static TraceIdPatternConverter newInstance(final String[] options) {
        return INSTANCE;
    }

    private TraceIdPatternConverter() {
        super(KEY_NAME, "style");
    }

    @Override
    public void format(LogEvent logEvent, StringBuilder toAppendTo) {
        String traceId = TraceContext.getTraceId();
        toAppendTo.append(traceId);
    }
}
