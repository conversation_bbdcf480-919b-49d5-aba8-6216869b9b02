package co.sandai.zeus.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

public class UrlUtil {

    /**
     * 将 JSON 转换为 HTTP GET 查询参数
     *
     * @param jsonString JSON 格式的字符串
     * @return 转换后的 HTTP GET 查询参数字符串
     * @throws Exception 转换失败时抛出异常
     */
    public static String convertJsonToHttpParams(String jsonString) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        // 解析 JSON 字符串为 Map
        Map<String, Object> paramMap = objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {});
        return convertMapToQueryParams(paramMap, null);
    }

    /**
     * 将 Map 转换为 URL 查询参数（支持递归处理）
     *
     * @param paramMap 参数 Map
     * @param parentKey 父级键（用于递归时的 key 组合）
     * @return 转换后的查询参数字符串
     * @throws UnsupportedEncodingException 编码失败时抛出异常
     */
    private static String convertMapToQueryParams(Object paramMap, String parentKey)
            throws UnsupportedEncodingException {
        StringBuilder result = new StringBuilder();

        if (paramMap instanceof Map) {
            // 处理 Map 类型
            Map<String, Object> map = (Map<String, Object>) paramMap;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = (parentKey == null) ? entry.getKey() : parentKey + "." + entry.getKey();
                if (result.length() > 0) result.append("&");
                result.append(convertMapToQueryParams(entry.getValue(), key));
            }
        } else if (paramMap instanceof List) {
            // 处理 List 类型
            List<Object> list = (List<Object>) paramMap;
            for (int i = 0; i < list.size(); i++) {
                String key = parentKey + "[" + i + "]";
                if (result.length() > 0) result.append("&");
                result.append(convertMapToQueryParams(list.get(i), key));
            }
        } else {
            // 处理普通键值对
            if (result.length() > 0) result.append("&");
            result.append(URLEncoder.encode(parentKey, "UTF-8"))
                    .append("=")
                    .append(URLEncoder.encode(String.valueOf(paramMap), "UTF-8"));
        }

        return result.toString();
    }

    public static void main(String[] args) {
        String jsonString = "{\n" + "  \"chunkList\": [\n"
                + "      {\n"
                + "        \"prompt\": \"Man stands on shore, looking out to sea.\",\n"
                + "        \"changeable\": false,\n"
                + "        \"duration\": 2\n"
                + "      },\n"
                + "      {\n"
                + "        \"prompt\": \"Man spots distant ship on the horizon.\",\n"
                + "        \"changeable\": true,\n"
                + "        \"duration\": 2\n"
                + "      },\n"
                + "      {\n"
                + "        \"prompt\": \"Man waves energetically at the ship.\",\n"
                + "        \"changeable\": true,\n"
                + "        \"duration\": 2\n"
                + "      }\n"
                + "    ]\n"
                + "}";

        try {
            String httpParams = convertJsonToHttpParams(jsonString);
            System.out.println("HTTP GET 参数格式: " + httpParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
