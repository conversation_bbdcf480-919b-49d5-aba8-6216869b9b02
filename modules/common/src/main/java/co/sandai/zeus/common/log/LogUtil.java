package co.sandai.zeus.common.log;

import java.text.MessageFormat;
import org.slf4j.Logger;

public class LogUtil {

    public static void error(Logger log, String message) {
        log.error(message);
    }

    public static void error(Logger log, String message, Throwable throwable) {
        log.error(message, throwable);
    }

    public static void errorf(Logger log, String format, Throwable throwable, Object... args) {
        String message = MessageFormat.format(format, args);
        log.error(message, throwable);
    }

    public static void errorf(Logger log, String format, Object... args) {
        String message = MessageFormat.format(format, args);
        log.error(message);
    }

    public static void warn(Logger log, String message) {
        log.warn(message);
    }

    public static void warn(Logger log, String message, Throwable throwable) {
        log.warn(message, throwable);
    }

    public static void warnf(Logger log, String format, Throwable throwable, Object... args) {
        String message = MessageFormat.format(format, args);
        log.warn(message, throwable);
    }

    public static void warnf(Logger log, String format, Object... args) {
        String message = MessageFormat.format(format, args);
        log.warn(message);
    }

    public static void info(Logger log, String message) {
        log.info(message);
    }

    public static void infof(Logger log, String format, Object... args) {
        String message = MessageFormat.format(format, args);
        log.info(message);
    }

    public static void debug(Logger log, String message) {
        log.debug(message);
    }

    public static void debug(Logger log, String message, Throwable throwable) {
        log.debug(message, throwable);
    }

    public static void debugf(Logger log, String format, Object... args) {
        String message = MessageFormat.format(format, args);
        log.debug(message, args);
    }

    public static void debugf(Logger log, String format, Throwable throwable, Object... args) {
        String message = MessageFormat.format(format, args);
        log.debug(message, throwable);
    }
}
