plugins {
    id 'java'
}


tasks.named('test') {
    useJUnitPlatform()
}

configurations.all {
    exclude group: 'ch.qos.logback'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    exclude group: 'log4j', module: 'log4j' // 排除 Log4j 1.x
    exclude group: 'commons-logging', module: 'commons-logging'
}

dependencies {
    implementation project(':modules:common')
    implementation project(':modules:config')

    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.redisson:redisson-spring-boot-starter:3.40.0'

    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'
    implementation 'com.github.yitter:yitter-idgenerator:1.0.6'
    implementation 'software.amazon.awssdk:s3:2.28.21'
    implementation 'redis.clients:jedis:5.2.0'
    implementation 'com.aliyun:dm20151123:1.2.2'

    implementation 'com.stripe:stripe-java:28.0.0'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'com.aliyun:green20220302:2.19.1'

    implementation 'org.apache.httpcomponents:httpclient:4.5.14'
    implementation 'org.apache.httpcomponents:httpcore:4.4.16'
    implementation 'org.apache.commons:commons-lang3:3.17.0'
    implementation 'org.reflections:reflections:0.10.2'
    implementation 'com.alibaba:fastjson:1.2.79'
    implementation 'com.coze:coze-api:0.1.6'

    implementation 'com.aliyun:captcha20230305:1.1.2'
    implementation 'com.aliyun:tea-openapi:0.3.6'
    implementation 'com.aliyun:tea:1.3.1'

    // Google Cloud Translation API
    implementation 'com.google.cloud:google-cloud-translate:2.32.0'
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.20.0'

}

