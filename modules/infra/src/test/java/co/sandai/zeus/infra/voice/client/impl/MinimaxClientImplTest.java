package co.sandai.zeus.infra.voice.client.impl;

import static org.junit.jupiter.api.Assertions.*;

import co.sandai.zeus.infra.voice.config.MinimaxConfig;
import co.sandai.zeus.infra.voice.dto.MinimaxVoiceDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.client.RestTemplate;

/**
 * Unit tests for MinimaxClientImpl
 */
public class MinimaxClientImplTest {

    @Mock
    private MinimaxConfig minimaxConfig;

    @Mock
    private RestTemplate restTemplate;

    private ObjectMapper objectMapper;

    private MinimaxClientImpl minimaxClient;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        objectMapper = new ObjectMapper();
        minimaxClient = new MinimaxClientImpl(minimaxConfig, restTemplate, objectMapper);
    }

    @Test
    public void testGetVoices_WithValidJson_ShouldReturnVoices() throws Exception {
        // Arrange
        String testJson = "{"
                + "    \"data\": {"
                + "        \"voice_list\": ["
                + "            {"
                + "                \"voice_id\": \"287975284219999\","
                + "                \"parent_voice_id\": \"209533299589189\","
                + "                \"voice_name\": \"Mature Partner\","
                + "                \"tag_list\": ["
                + "                    \"English\","
                + "                    \"Male\","
                + "                    \"Middle Age\""
                + "                ],"
                + "                \"sample_audio\": \"https://example.com/sample1.mp3\","
                + "                \"description\": \"A mature male voice\""
                + "            },"
                + "            {"
                + "                \"voice_id\": \"287973542097022\","
                + "                \"parent_voice_id\": \"226902087905344\","
                + "                \"voice_name\": \"Chinese Voice\","
                + "                \"tag_list\": ["
                + "                    \"中文\","
                + "                    \"Female\""
                + "                ],"
                + "                \"sample_audio\": \"https://example.com/sample2.mp3\","
                + "                \"description\": \"A Chinese female voice\""
                + "            }"
                + "        ],"
                + "        \"total\": 2"
                + "    }"
                + "}";

        // Create a subclass that overrides the processVoicesFromInputStream method
        MinimaxClientImpl testClient = new MinimaxClientImpl(minimaxConfig, restTemplate, objectMapper) {
            @Override
            public List<MinimaxVoiceDTO> getVoices() {
                try {
                    InputStream inputStream = new ByteArrayInputStream(testJson.getBytes());
                    return processVoicesFromInputStream(inputStream);
                } catch (Exception e) {
                    throw new RuntimeException("Test failed", e);
                }
            }
        };

        // Act
        List<MinimaxVoiceDTO> voices = testClient.getVoices();

        // Assert
        assertNotNull(voices);
        assertEquals(2, voices.size());

        // Verify first voice
        MinimaxVoiceDTO voice1 = voices.get(0);
        assertEquals("287975284219999", voice1.getVoiceId());
        assertEquals("Mature Partner", voice1.getName());
        assertEquals("A mature male voice", voice1.getDescription());
        assertEquals("en", voice1.getLanguage());
        assertEquals("https://example.com/sample1.mp3", voice1.getPreviewUrl());
        assertTrue(voice1.isCloned());

        // Verify second voice
        MinimaxVoiceDTO voice2 = voices.get(1);
        assertEquals("287973542097022", voice2.getVoiceId());
        assertEquals("Chinese Voice", voice2.getName());
        assertEquals("A Chinese female voice", voice2.getDescription());
        assertEquals("zh", voice2.getLanguage()); // Should be zh due to "中文" tag
        assertEquals("https://example.com/sample2.mp3", voice2.getPreviewUrl());
        assertTrue(voice2.isCloned());
    }
}
