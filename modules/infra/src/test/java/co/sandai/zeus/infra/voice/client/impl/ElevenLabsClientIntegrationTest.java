package co.sandai.zeus.infra.voice.client.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import co.sandai.zeus.infra.voice.config.ElevenLabsConfig;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Properties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

/**
 * Integration tests for ElevenLabsClient that call the actual API.
 * These tests will use the API key configured in application.properties if available.
 */
public class ElevenLabsClientIntegrationTest {

    private ElevenLabsClientImpl elevenLabsClient;
    private ElevenLabsConfig elevenLabsConfig;
    private String apiKey = null;

    @BeforeEach
    public void setUp() {
        // 尝试从配置文件中读取API密钥
        try {
            Properties props = new Properties();
            // 首先尝试读取application-test.properties（包含实际API密钥，被gitignore忽略）
            String testPropsPath =
                    "/Users/<USER>/jproj/zeus/modules/infra/src/test/resources/application-test.properties";
            File testPropsFile = new File(testPropsPath);
            if (testPropsFile.exists()) {
                props.load(new FileInputStream(testPropsPath));
                apiKey = props.getProperty("elevenlabs.api.key");
                System.out.println("从application-test.properties读取API密钥");
            } else {
                // 如果测试配置文件不存在，尝试读取普通配置文件
                String appPropsPath =
                        "/Users/<USER>/jproj/zeus/modules/infra/src/test/resources/application.properties";
                props.load(new FileInputStream(appPropsPath));
                apiKey = props.getProperty("elevenlabs.api.key");
                System.out.println("从application.properties读取API密钥");
            }
        } catch (Exception e) {
            System.out.println("无法从配置文件读取API密钥: " + e.getMessage());
        }

        // 创建配置
        elevenLabsConfig = Mockito.mock(ElevenLabsConfig.class);
        Mockito.when(elevenLabsConfig.getApiKey()).thenReturn(apiKey);
        Mockito.when(elevenLabsConfig.getBaseUrl()).thenReturn("https://api.elevenlabs.io/");

        // 创建客户端
        elevenLabsClient = new ElevenLabsClientImpl(elevenLabsConfig);
    }

    /**
     * Test speech-to-text with sample audio.
     * This test will be skipped if no API key is available.
     */
    @Test
    // @EnabledIf("isApiKeyAvailable")
    public void testSpeechToTextWithRealApi() throws Exception {
        // Create a simple audio file with test content
        // In a real test, you would have a real audio file
        byte[] sampleAudio = createSampleAudioFile();

        // Call the API
        String result = elevenLabsClient.speechToText(sampleAudio, null);

        // Verify that we got some text back
        assertNotNull(result);
        assertFalse(result.isEmpty());

        // 打印语音转文本结果
        System.out.println("\n==================== STT 结果 ====================");
        System.out.println(result);
        System.out.println("==================== STT 结果结束 ====================\n");
    }

    // MultipartFile speechToText test removed as part of refactoring to keep only byte[] interface

    /**
     * Helper method to get test audio data
     */
    private byte[] createSampleAudioFile() throws IOException {
        // Try to find a test audio file in resources directory
        Path audioPath = getSampleAudioPath();
        System.out.println("Looking for audio file at: " + audioPath.toString());

        if (Files.exists(audioPath)) {
            // Use the existing audio file if available
            System.out.println("Found audio file! File exists at: " + audioPath.toString());
            byte[] fileData = Files.readAllBytes(audioPath);
            System.out.println("Audio file size: " + fileData.length + " bytes");
            return fileData;
        } else {
            // If no file exists, create a simple file with test content
            // NOTE: This is just a placeholder and won't work as actual audio data
            // For real testing, add a real audio file to the test resources
            System.out.println("WARNING: Audio file not found at: " + audioPath.toString());
            System.out.println("WARNING: Using dummy audio data. Real testing requires a valid audio file.");

            // Create minimal dummy MP3 content (not a real MP3, just a placeholder)
            byte[] dummyContent = new byte[1024];
            for (int i = 0; i < dummyContent.length; i++) {
                dummyContent[i] = (byte) (i % 256);
            }

            // Ensure test directory exists
            Files.createDirectories(audioPath.getParent());

            // Write dummy content to the test file location
            Files.write(audioPath, dummyContent, StandardOpenOption.CREATE);

            return dummyContent;
        }
    }

    /**
     * Get path to a test audio file
     */
    private Path getSampleAudioPath() throws IOException {
        // Use the class loader to find resources
        URL resource = getClass().getClassLoader().getResource("audio/zh-test.mp3");
        if (resource == null) {
            throw new IOException("Could not find test audio file: audio/zh-test.mp3");
        }
        System.out.println("Resource URL: " + resource.toString());
        try {
            return Paths.get(resource.toURI());
        } catch (URISyntaxException e) {
            throw new IOException("Invalid resource URI: " + e.getMessage(), e);
        }
    }

    /**
     * 检查API密钥是否可用，用于条件性测试
     */
    boolean isApiKeyAvailable() {
        return apiKey != null && !apiKey.equals("your-api-key-here") && !apiKey.isEmpty();
    }
}
