package co.sandai.zeus.infra;

import co.sandai.zeus.common.utils.StringUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.green20220302.models.ImageModerationResponseBody;
import com.aliyun.green20220302.models.TextModerationResponseBody;
import com.aliyun.green20220302.models.VideoModerationResponseBody;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

@SpringBootTest(classes = {ResourceLoader.class, Green.class, GreenOpenApiSDK.class})
public class GreenTest {

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private Green green;

    @Autowired
    private GreenOpenApiSDK greenApi;

    @Test
    public void testParseVideoModerationCallbackRequest() throws IOException {
        Resource content = resourceLoader.getResource("classpath:/video-callback-content.json");
        String checksum = "5bd92f10945b048532971f42e5fd9a57903d8133d449b54c8b5adf4fc8c5bbde";
        String contentString = content.getContentAsString(Charset.defaultCharset());
        Green.ModerateVideoCallbackRequest request = Green.ModerateVideoCallbackRequest.builder()
                .checksum(checksum)
                .content(contentString)
                .build();

        Green green = new Green();
        Green.ModerateVideoResult result = green.parseVideoModerationCallbackRequest(request);
        Assertions.assertEquals(664124265371525L, result.dataId);
        Assertions.assertFalse(result.isValid);

        // validate
        String calcCheckSum = StringUtil.strSHA256Hex("1552113873580453" + "pyDJZHQIuH" + contentString);
        Assertions.assertEquals(checksum, calcCheckSum);
    }

    @Test
    public void getGreen() {
        Green.ModerateTextResult a = green.moderateText("hello world", 123456);
        System.out.println(JSON.toJSONString(a, true));

        Green.ModerateTextResult a2 = green.moderateText("xijinping", 123456);
        System.out.println(JSON.toJSONString(a2, true));

        Green.ModerateImageResult b = green.moderateImage(
                "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/69cc59c4-210e-4822-9888-f2f8e0c9139f/202504.jpg",
                123456);
        System.out.println(JSON.toJSONString(b, true));

        Green.ModerateImageResult b2 =
                green.moderateImage("https://sandai-dev.oss-cn-shanghai.aliyuncs.com/***************.jpg", 123456);
        System.out.println(JSON.toJSONString(b2, true));

        Green.SubmitVideoModerationTaskResult c = green.submitVideoModerationTask(
                "https://files.sandai.cn/artifacts/default/9f3add79-3126-4f68-b25f-a264e349a508/202504.mp4", 123456);
        System.out.println(JSON.toJSONString(c, true));
    }

    @Test
    public void testGA() {
        HashMap<String, String> args = new HashMap<>();
        args.put("content", "hello world");
        args.put("accountId", "123456");
        TextModerationResponseBody a = greenApi.moderationText("comment_multilingual_pro", args);
        System.out.println(a);

        HashMap<String, String> imageArgs = new HashMap<>();
        imageArgs.put(
                "imageUrl",
                "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/69cc59c4-210e-4822-9888-f2f8e0c9139f/202504.jpg");
        imageArgs.put("dateId", "123456");
        ImageModerationResponseBody b = greenApi.moderateImage("baselineCheck", imageArgs);
        System.out.println(b);

        HashMap<String, String> videoArgs = new HashMap<>();
        videoArgs.put(
                "url", "https://files.sandai.cn/artifacts/default/9f3add79-3126-4f68-b25f-a264e349a508/202504.mp4");
        videoArgs.put("dateId", "123456");
        videoArgs.put("callback", "example.com");
        videoArgs.put("seed", "asdf");
        VideoModerationResponseBody c = greenApi.moderateVideo("videoDetection", videoArgs);
        System.out.println(c);
    }
}
