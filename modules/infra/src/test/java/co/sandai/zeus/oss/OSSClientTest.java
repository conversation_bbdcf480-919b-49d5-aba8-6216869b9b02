package co.sandai.zeus.oss;

import co.sandai.zeus.TestApplication;
import co.sandai.zeus.infra.oss.OSSProviderSource;
import co.sandai.zeus.infra.oss.OssClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = TestApplication.class)
public class OSSClientTest {

    @Test
    public void testParse() {
        OssClient.OSSFileMeta res = OssClient.parseOSSUrl(
                "https://athena-artifacts-dev.oss-cn-shanghai.aliyuncs.com/artifacts/default/547fded5-f501-4a76-b0b0-f4c5a7b407f4/202412.jpg?id=1");
        Assertions.assertEquals("artifacts/default/547fded5-f501-4a76-b0b0-f4c5a7b407f4/202412.jpg", res.getPath());
        Assertions.assertEquals(OSSProviderSource.AliYun, res.getSource());
        Assertions.assertEquals("athena-artifacts-dev", res.getBucket());
    }
}
