package co.sandai.zeus.infra.voice.client.impl;

import static org.junit.jupiter.api.Assertions.*;

import co.sandai.zeus.infra.voice.config.GoogleTranslateConfig;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

public class GoogleTranslateClientImplTest {

    /**
     * Integration test that makes a real call to the Google Cloud Translation API.
     * This test uses credentials from application-test.properties.
     */
    @Test
    @Tag("integration")
    public void testRealGoogleApiCall() throws java.io.IOException {
        // Load application properties for testing
        org.springframework.core.io.ClassPathResource resource =
                new org.springframework.core.io.ClassPathResource("application-test.properties");
        java.util.Properties properties = new java.util.Properties();
        properties.load(resource.getInputStream());

        // Create a real config with the service account credentials from properties
        GoogleTranslateConfig realConfig = new GoogleTranslateConfig();
        String credentialsJson = properties.getProperty("google.translate.credentials-json");
        realConfig.setCredentialsJson(credentialsJson);
        realConfig.setProjectId("sa-gemini"); // Set your project ID

        // Create a real client instance
        GoogleTranslateClientImpl realClient = new GoogleTranslateClientImpl(realConfig);

        // Test with English text
        String englishText = "Hello, this is a test of the Google Translate API";
        String englishDetected = realClient.detectLanguage(englishText);
        assertNotNull(englishDetected);
        assertEquals("en", englishDetected);

        // Test with Chinese text
        String chineseText = "你好，这是谷歌翻译API的测试";
        String chineseDetected = realClient.detectLanguage(chineseText);
        assertNotNull(chineseDetected);
        assertTrue(chineseDetected.startsWith("zh"), "Should detect Chinese language code starting with 'zh'");

        // Test with Japanese text
        String japaneseText = "こんにちは、これはGoogleTranslate APIのテストです";
        String japaneseDetected = realClient.detectLanguage(japaneseText);
        assertNotNull(japaneseDetected);
        assertEquals("ja", japaneseDetected);
    }
}
