package co.sandai.zeus.infra.voice.client;

/**
 * Client interface for Google Cloud Translation API
 * This interface defines methods for interacting with the Google Cloud Translation API
 * without exposing domain layer concepts
 */
public interface GoogleTranslateClient {

    /**
     * Detect the language of a text string
     *
     * @param text The text to analyze for language detection
     * @return The detected language code (e.g., "en", "zh-CN", "ja")
     */
    String detectLanguage(String text);
}
