package co.sandai.zeus.infra;

import java.util.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

@Service
public class RateLimiter {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String SCRIPT =
            """
            local key = KEYS[1]
            local now = tonumber(ARGV[1])
            local window = tonumber(ARGV[2])
            local limit = tonumber(ARGV[3])
            redis.call('ZREMRANGEBYSCORE', key, 0, now - window)
            local count = redis.call('ZCARD', key)
            if count >= limit then
              return 0
            else
              redis.call('ZADD', key, now, now)
              redis.call('EXPIRE', key, math.ceil(window / 1000) + 1)
              return 1
            end
        """;

    private final DefaultRedisScript<Long> redisScript;

    public RateLimiter() {
        this.redisScript = new DefaultRedisScript<>();
        this.redisScript.setScriptText(SCRIPT);
        this.redisScript.setResultType(Long.class);
    }

    /**
     * @param key Redis 键（通常是 IP、用户 ID、接口名等）
     * @param limit 允许最大请求数
     * @param windowMillis 滑动窗口时间（毫秒）
     */
    public boolean isAllowed(String key, int limit, long windowMillis) {
        long now = System.currentTimeMillis();
        Long result = redisTemplate.execute(
                redisScript,
                Collections.singletonList("rate_limit:" + key),
                String.valueOf(now),
                String.valueOf(windowMillis),
                String.valueOf(limit));
        return result == 1L;
    }
}
