package co.sandai.zeus.infra.voice.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for Google Cloud Translation API
 */
@Configuration
@ConfigurationProperties(prefix = "google.translate")
@Data
public class GoogleTranslateConfig {

    /**
     * Project ID for Google Cloud
     */
    private String projectId;

    /**
     * API key for Google Cloud Translation API
     */
    private String apiKey;

    /**
     * Path to service account credentials JSON file (optional)
     */
    private String credentialsPath;

    /**
     * Service account credentials as JSON string (alternative to credentialsPath)
     */
    private String credentialsJson;
}
