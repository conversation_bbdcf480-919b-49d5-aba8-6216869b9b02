package co.sandai.zeus.infra.prompt;

import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.infra.prompt.dto.ChunkDTO;
import co.sandai.zeus.infra.prompt.dto.SplitChunkResultDTO;
import co.sandai.zeus.infra.prompt.dto.UpdateChunkPromptRequestDTO;
import co.sandai.zeus.infra.prompt.dto.UpdateChunkPromptResultDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

@Slf4j
public class CozeConverter {

    /**
     *
     * @param plotStr
     * 样例1： "Soldiers cautiously approach the alien, unsure of its intentions##3##\nThe alien raises its hand, signaling peace##2##\nSoldiers and alien slowly move closer to each other##3##\nThey share a heartfelt hug, breaking the tension##4##\nSoldiers smile, feeling a newfound connection with the alien##3##"
     * @return
     */
    public static List<ChunkDTO> fromStringToChunks(String plotStr) {
        if (StringUtils.isBlank(plotStr)) {
            return Collections.emptyList();
        }
        String[] clips = plotStr.split("\\n");
        ArrayList<ChunkDTO> result = new ArrayList<>();

        for (String clip : clips) {
            try {
                ChunkDTO chunkDTO = fromStringToChunk(clip);
                result.add(chunkDTO);
            } catch (Throwable t) {
                LogUtil.warnf(log, "Invalid plotStr: {0}", plotStr);
                continue;
            }
        }
        return result;
    }

    private static ChunkDTO fromStringToChunk(String clip) {
        ChunkDTO chunkDTO = new ChunkDTO()
                .setPrompt(clip.substring(0, clip.indexOf("##")))
                .setDuration(Integer.valueOf(clip.substring(clip.indexOf("##") + 2, clip.lastIndexOf("##"))));
        return chunkDTO;
    }

    /**
     * @param chunks
     * @return 样例1： "0. Giant panda steps into Heaven Temple.\n1. null\n2. Panda begins to dash happily.\n3. null\n4. Panda sprints by majestic pillars.\n5. null."
     *          样例2： "0. Giant panda enters Heaven Temple.\n1. null\n2. Panda starts running frantically, eyes wide with fear.\n3. null\n4. Panda runs past crumbling, haunted pillars.\n5. null."
     *          样例3： "0. Giant panda enters Heaven Temple.\n1. null\n2. Panda starts running joyfully.\n3. null\n4. Panda runs past ancient pillars.\n5. null"
     */
    public static String fromChangeableChunksToPlotStr(List<UpdateChunkPromptRequestDTO.Chunk> chunks) {
        StringBuilder sb = new StringBuilder();

        for (UpdateChunkPromptRequestDTO.Chunk chunk : chunks) {
            sb.append(chunk.getPrompt())
                    .append("##")
                    .append(chunk.getDuration())
                    .append("##")
                    .append("\n");
        }
        return sb.toString();
    }

    /**
     *
     * @param data 样例：
     *             {
     *   "nextplotaction": "The alien suddenly points towards the sky, revealing an incoming enemy spaceship##4##",
     *   "nextplothorror": "The alien's eyes suddenly turn red, emitting an eerie glow##5##",
     *   "nextplotjoy": "The alien reveals a holographic map of the universe##4##",
     *   "nextplotplain": "The alien speaks in a soft, melodic voice##4##"
     * }
     * @return
     */
    @NotNull
    static UpdateChunkPromptResultDTO fromRawDataToUpdateChunkPromptResultDTO(String data) {
        JSONObject jsonObject = JSONObject.parseObject(data);
        UpdateChunkPromptResultDTO result = new UpdateChunkPromptResultDTO();

        // 其他的都是候选情节
        List<List<ChunkDTO>> candidateChunksList = new ArrayList<>();
        List<ChunkDTO> chunkDTOS = new ArrayList<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            ChunkDTO chunkDTO = fromStringToChunk(String.valueOf(entry.getValue()));
            chunkDTOS.add(chunkDTO);
        }
        candidateChunksList.add(chunkDTOS);
        result.setCandidateChunksList(candidateChunksList);
        return result;
    }

    /**
     * 示例：
     * {
     *   "mainplot": "Soldiers cautiously approach the alien, unsure of its intentions##3##\nThe alien raises its hand, signaling peace##2##\nSoldiers and alien slowly move closer to each other##3##\nThey share a heartfelt hug, breaking the tension##4##\nSoldiers smile, feeling a newfound connection with the alien##3##"
     * }
     * @param data
     * @return
     */
    @NotNull
    static SplitChunkResultDTO fromRawDataToSplitChunkResultDTO(String data) {
        JSONObject jsonObject = JSONObject.parseObject(data);
        SplitChunkResultDTO result = new SplitChunkResultDTO();

        // 遍历所有返回的情节列表
        List<List<ChunkDTO>> candidateChunksList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            List<ChunkDTO> chunkDTOS = fromStringToChunks(String.valueOf(entry.getValue()));
            candidateChunksList.add(chunkDTOS);
        }
        result.setCandidateChunksList(candidateChunksList);
        return result;
    }

    public static List<String> fromArCaptionResponse(String data) {
        JSONObject jsonObject = JSONObject.parseObject(data);

        List<String> result = new ArrayList<>();
        String firstChunkCaption = jsonObject.getString("firstChunkCaption");
        result.add(firstChunkCaption);

        List<JSONObject> followingChunkCaptions =
                jsonObject.getObject("followingChunkCaptions", new TypeReference<List<JSONObject>>() {});
        for (JSONObject followingChunkCaption : followingChunkCaptions) {
            result.add(followingChunkCaption.getString("description"));
        }
        return result;
    }
}
