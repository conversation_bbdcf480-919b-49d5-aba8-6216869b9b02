package co.sandai.zeus.infra.prompt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class GenStoryRequest {

    /**
     * 生成候选story的数量
     */
    @JsonProperty("count")
    @Builder.Default
    private int count = 5;

    @JsonProperty("image_url")
    private String imageUrl;

    @JsonProperty("story_prompt")
    private String storyPrompt;
}
