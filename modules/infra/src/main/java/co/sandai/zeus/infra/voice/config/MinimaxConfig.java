package co.sandai.zeus.infra.voice.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Configuration for Minimax API
 */
@Getter
@Component
public class MinimaxConfig {
    @Value("${minimax.api.key:}")
    private String apiKey;

    @Value("${minimax.api.baseUrl:https://api.minimaxi.chat/}")
    private String baseUrl;

    @Value("${minimax.api.groupId:}")
    private String groupId;
}
