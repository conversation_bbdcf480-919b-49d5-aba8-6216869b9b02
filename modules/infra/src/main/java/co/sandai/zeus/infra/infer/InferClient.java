package co.sandai.zeus.infra.infer;

import co.sandai.zeus.infra.infer.dto.InferCommonResponse;
import co.sandai.zeus.infra.infer.dto.InferJob;
import co.sandai.zeus.infra.infer.dto.InferPipelineDTO;
import co.sandai.zeus.infra.web.log.LogBody;
import com.alibaba.fastjson.JSONObject;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Component
@Slf4j
public class InferClient {

    @Value("${athena.url}")
    private String athenaUrl;

    @Value("${athena.api.key}")
    private String athenaApiKey;

    @Autowired
    private RestTemplate restTemplate;

    @LogBody(uri = "public/api/v1/jobs/")
    public ResponseEntity<String> pushToInferJob(InferPipelineDTO requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-API-KEY", athenaApiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<InferPipelineDTO> requestEntity = new HttpEntity<>(requestBody, headers);
        return restTemplate.exchange(athenaUrl + "/public/api/v1/jobs/", HttpMethod.POST, requestEntity, String.class);
    }

    @LogBody(uri = "public/api/v1/jobs/by_request_id/*")
    public ResponseEntity<InferCommonResponse<JSONObject>> queryTaskDetailInfo(long taskId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-API-KEY", athenaApiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<InferPipelineDTO> requestEntity = new HttpEntity<>(headers);

        ParameterizedTypeReference<InferCommonResponse<JSONObject>> typeReference =
                new ParameterizedTypeReference<>() {};

        return restTemplate.exchange(
                athenaUrl + "/public/api/v1/jobs/by-request/" + taskId,
                HttpMethod.GET,
                requestEntity,
                typeReference,
                Collections.emptyMap());
    }

    @LogBody(uri = "public/api/v1/jobs/by_request_id/*")
    public InferJob getInferJob(long taskId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-API-KEY", athenaApiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<InferPipelineDTO> requestEntity = new HttpEntity<>(headers);

        ParameterizedTypeReference<InferCommonResponse<InferJob>> typeReference = new ParameterizedTypeReference<>() {};

        try {
            ResponseEntity<InferCommonResponse<InferJob>> response = restTemplate.exchange(
                    athenaUrl + "/public/api/v1/jobs/by-request/" + taskId,
                    HttpMethod.GET,
                    requestEntity,
                    typeReference,
                    Collections.emptyMap());

            // Extract the InferJob from the response
            if (response.getBody() != null && response.getBody().getData() != null) {
                return response.getBody().getData();
            }
            return null;
        } catch (RestClientException e) {
            log.error("Error querying task detail info for taskId: {}", taskId, e);
            // Return null in case of error
            return null;
        }
    }

    public void cancelJob(String jobId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-API-KEY", athenaApiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<InferPipelineDTO> requestEntity = new HttpEntity<>(headers);

        restTemplate.postForEntity(
                athenaUrl + "/public/api/v1/jobs/" + jobId + "/cancel", requestEntity, JSONObject.class);
    }
}
