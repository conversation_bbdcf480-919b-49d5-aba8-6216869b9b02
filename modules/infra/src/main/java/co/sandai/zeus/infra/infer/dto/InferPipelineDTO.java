package co.sandai.zeus.infra.infer.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
public class InferPipelineDTO {

    private List<InputDTO> inputs;
    private List<StepDTO> steps;
    private String type;
    private String queue;
    private String model;

    @JsonProperty("model_version")
    private String modelVersion;

    @JsonProperty("request_id")
    private String requestId;

    private OptionsDTO options;

    @Data
    public static class OptionsDTO {
        @JsonProperty("tSchedulerFunc")
        private String tSchedulerFunc;

        @JsonProperty("tSchedulerFuncArgs")
        private String tSchedulerFuncArgs;

        private long seed;
        private String aspectRatio;
        private List<String> specialTokens;
        private String vaeModel;
        private String resolution;
        private boolean promptEnhancement;
        private String promptEnhancementType;
        private Map<String, Integer> cropArea;
        private long cropTimeStart;
        private long cropTimeEnd;

        @JsonProperty("nSampleSteps")
        private int nSampleSteps;

        private boolean dryRun;
        private boolean enableWatermark;
        private boolean enableInputVideoToTs;
        private String watermarkType;
        private Map<String, Object> extra;

        /**
         * 存储未在类中明确定义的额外属性
         * 在JSON序列化时，这些属性会与其他属性平铺在同一级别
         */
        @JsonIgnore
        private Map<String, Object> dynamicProps = new HashMap<>();

        /**
         * 使额外属性在JSON中平铺显示
         */
        @JsonAnyGetter
        public Map<String, Object> getDynamicProps() {
            return dynamicProps;
        }

        /**
         * 捕获所有未定义的JSON属性
         */
        @JsonAnySetter
        public void addDynamicProp(String name, Object value) {
            dynamicProps.put(name, value);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class InputDTO {

        private String urn;
        /**
         * video or image
         */
        private String name;
    }

    @Data
    @Accessors(chain = true)
    public static class StepDTO {

        private Float duration;
        private String prompt;
        private String audioUrn; // 暂时只有一种可能就是audio
    }
}
