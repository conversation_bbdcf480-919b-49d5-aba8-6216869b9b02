package co.sandai.zeus.infra.prompt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SplitChunkRequest {

    /**
     * 切分chunk的数量
     */
    @JsonProperty("count")
    private int count;

    /**
     * 图片url
     */
    @JsonProperty("image_url")
    private String imageUrl;

    /**
     * story prompt
     */
    @JsonProperty("story")
    private String storyPrompt;
}
