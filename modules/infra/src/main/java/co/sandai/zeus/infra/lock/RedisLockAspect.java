package co.sandai.zeus.infra.lock;

import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Order(1)
@Component
public class RedisLockAspect {

    @Autowired
    private RedissonClient redissonClient;

    @Around("@annotation(redisLock)")
    public Object around(ProceedingJoinPoint joinPoint, RedisLock redisLock) throws Throwable {
        String key = parseKey(redisLock.key(), joinPoint); // 动态生成锁的 key
        RLock lock = redissonClient.getLock(key);

        boolean lockAcquired = false;

        try {
            // 尝试获取分布式锁
            lockAcquired = lock.tryLock(redisLock.waitTime(), redisLock.leaseTime(), TimeUnit.MILLISECONDS);
            if (!lockAcquired) {
                throw new RuntimeException("Could not acquire lock for key: " + key);
            }

            // 执行业务逻辑
            return joinPoint.proceed();
        } catch (Throwable e) {
            // 如果 joinPoint.proceed 抛出异常，直接向外抛出
            throw e;
        } finally {
            // 确保锁的释放
            if (lockAcquired) {
                try {
                    lock.unlock();
                } catch (IllegalMonitorStateException e) {
                    // 防止因锁状态不一致导致 unlock 异常
                    System.err.println("Unlock failed: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 动态解析注解中的 SpEL 表达式或生成 Key
     */
    private String parseKey(String key, ProceedingJoinPoint joinPoint) {
        // 简单实现，支持动态替换参数
        return MessageFormat.format(key, joinPoint.getArgs());
    }
}
