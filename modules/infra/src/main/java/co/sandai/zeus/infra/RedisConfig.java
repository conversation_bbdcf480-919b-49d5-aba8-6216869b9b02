package co.sandai.zeus.infra;

import co.sandai.zeus.config.SystemRedisConfig;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

@Slf4j
@Configuration
public class RedisConfig {
    @Autowired
    SystemRedisConfig redisConfig;

    @Bean
    public JedisConnectionFactory redisConnectionFactory() {
        String host = redisConfig.getHost();
        String redisPassword = redisConfig.getPassword();
        Integer redisDatabase = redisConfig.getDatabase();

        int maxIdle = redisConfig.getPoolMaxIdle();
        int minIdle = redisConfig.getPoolMinIdle();
        int connTimeout = redisConfig.getConnTimeout();
        int readTimeout = redisConfig.getReadTimeout();
        int port = redisConfig.getPort();

        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(host, port);
        if (redisPassword != null) {
            config.setPassword(redisPassword);
        }
        if (redisDatabase != null) {
            config.setDatabase(redisDatabase);
        }

        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);

        // 设置客户端配置，包括超时时间
        JedisClientConfiguration clientConfig = JedisClientConfiguration.builder()
                .connectTimeout(Duration.ofMillis(connTimeout)) // 设置连接超时时间
                .readTimeout(Duration.ofMillis(readTimeout)) // 设置读取超时时间
                .usePooling()
                .poolConfig(poolConfig)
                .build();

        log.info(
                "redis config, conn timeout: {}, read timeout: {}, minIdle: {}, maxIdle: {}",
                connTimeout,
                readTimeout,
                minIdle,
                maxIdle);

        return new JedisConnectionFactory(config, clientConfig);
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setConnectionFactory(connectionFactory);
        return template;
    }

    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        return container;
    }
}
