package co.sandai.zeus.infra.voice.client.impl;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.infra.voice.client.GoogleTranslateClient;
import co.sandai.zeus.infra.voice.config.GoogleTranslateConfig;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.translate.Detection;
import com.google.cloud.translate.Translate;
import com.google.cloud.translate.TranslateOptions;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Implementation of the GoogleTranslateClient interface
 * Handles communication with the Google Cloud Translation API
 */
@Component
@Slf4j
public class GoogleTranslateClientImpl implements GoogleTranslateClient {

    private final GoogleTranslateConfig config;
    private Translate translateService;

    public GoogleTranslateClientImpl(GoogleTranslateConfig config) {
        this.config = config;
        initializeTranslateService();
    }

    private void initializeTranslateService() {
        try {
            TranslateOptions.Builder optionsBuilder = TranslateOptions.newBuilder();

            // Set project ID if available
            if (StringUtils.hasText(config.getProjectId())) {
                optionsBuilder.setProjectId(config.getProjectId());
            }

            // Try to load credentials from JSON string if provided
            if (StringUtils.hasText(config.getCredentialsJson())) {
                try (ByteArrayInputStream credentialsStream =
                        new ByteArrayInputStream(config.getCredentialsJson().getBytes(StandardCharsets.UTF_8))) {
                    GoogleCredentials credentials = GoogleCredentials.fromStream(credentialsStream);
                    optionsBuilder.setCredentials(credentials);
                    log.info("Google Translate service credentials loaded from JSON string");
                }
            }
            // If no JSON string, try to load credentials from file if path is provided
            else if (StringUtils.hasText(config.getCredentialsPath())) {
                try (FileInputStream credentialsStream = new FileInputStream(config.getCredentialsPath())) {
                    GoogleCredentials credentials = GoogleCredentials.fromStream(credentialsStream);
                    optionsBuilder.setCredentials(credentials);
                    log.info("Google Translate service credentials loaded from file: {}", config.getCredentialsPath());
                }
            }

            // Set API key if available (alternative to credentials file)
            if (StringUtils.hasText(config.getApiKey())) {
                optionsBuilder.setApiKey(config.getApiKey());
            }

            this.translateService = optionsBuilder.build().getService();
            log.info("Google Translate service initialized successfully");
        } catch (IOException e) {
            log.error("Failed to initialize Google Translate service: {}", e.getMessage(), e);
            throw new ZeusServiceException(ErrorCode.ExternalApiError, "Failed to initialize Google Translate service");
        }
    }

    @Override
    public String detectLanguage(String text) {
        try {
            if (!StringUtils.hasText(text)) {
                return "";
            }

            log.debug("Detecting language for text: {}", text.length() > 100 ? text.substring(0, 100) + "..." : text);

            Detection detection = translateService.detect(text);
            String languageCode = detection.getLanguage();
            float confidence = detection.getConfidence();

            log.debug("Detected language: {} with confidence: {}", languageCode, confidence);
            return languageCode;
        } catch (Exception e) {
            log.error("Failed to detect language: {}", e.getMessage(), e);
            throw new ZeusServiceException(ErrorCode.ExternalApiError, "Failed to detect language: " + e.getMessage());
        }
    }
}
