package co.sandai.zeus.infra.oss;

public enum OSSProviderSource {
    // aliyun 上海，兼容
    <PERSON><PERSON>("aliyun"),
    <PERSON><PERSON><PERSON>("aliyun-hongkong");

    public final String value;

    OSSProviderSource(String value) {
        this.value = value;
    }

    public static OSSProviderSource fromValue(String value) {
        for (OSSProviderSource source : OSSProviderSource.values()) {
            if (source.value.equals(value)) {
                return source;
            }
        }
        throw new IllegalArgumentException("Unknown oss provider value: " + value);
    }

    @Override
    public String toString() {
        return this.value;
    }
}
