package co.sandai.zeus.infra.prompt;

import co.sandai.zeus.infra.web.log.LogBody;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * 调用coze的模板服务
 */
@Component
public class CozeTemplateService {

    @Value("${coze.base-url}")
    private String baseCozeUrl;

    @Value("${coze.run.workflow.uri}")
    private String cozeRunWorkflowUri;

    @Value("${coze.access.token}")
    private String cozeAccessToken;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * @param parameters workflow的start节点的参数
     * @param workflowId wordflowId
     * @param appId appId
     * @return
     */
    @LogBody(uri = "/v1/workflow/run")
    public CozeResponse runWorkflow(Object parameters, String workflowId, String appId) {
        String url = baseCozeUrl + cozeRunWorkflowUri;

        MultiValueMap headers = new HttpHeaders();
        headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        headers.set("Authorization", "Bearer " + cozeAccessToken);
        CozeRequest request = CozeRequest.builder()
                .parameters(parameters)
                .workflowId(workflowId)
                .appId(appId)
                .build();

        return restTemplate.postForObject(url, new HttpEntity<>(request, headers), CozeResponse.class);
    }

    @Data
    @Builder
    public static class CozeRequest {
        private Object parameters;

        @JsonProperty("workflow_id")
        private String workflowId;

        @JsonProperty("app_id")
        private String appId;
    }

    @Data
    public static class CozeResponse {
        private Integer code;
        private String cost;

        private String data;

        @JsonProperty("debug_url")
        private String debugUrl;

        private String msg;

        private Integer token;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }
}
