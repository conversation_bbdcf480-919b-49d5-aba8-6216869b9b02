package co.sandai.zeus.infra.oauth2;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * Manual OAuth2 service implementation
 */
@Slf4j
@Service
public class OAuth2Service {

    @Value("${oauth2.google.client-id:}")
    private String googleClientId;

    @Value("${oauth2.google.client-secret:}")
    private String googleClientSecret;

    @Value("${oauth2.google.redirect-uri:}")
    private String googleRedirectUri;

    @Value("${oauth2.discord.client-id:}")
    private String discordClientId;

    @Value("${oauth2.discord.client-secret:}")
    private String discordClientSecret;

    @Value("${oauth2.discord.redirect-uri:}")
    private String discordRedirectUri;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public String getGoogleAuthorizationUrl() {
        String baseUrl = "https://accounts.google.com/o/oauth2/v2/auth";
        String scope = "openid profile email";

        return baseUrl + "?" + "client_id="
                + URLEncoder.encode(googleClientId, StandardCharsets.UTF_8) + "&redirect_uri="
                + URLEncoder.encode(googleRedirectUri, StandardCharsets.UTF_8) + "&scope="
                + URLEncoder.encode(scope, StandardCharsets.UTF_8) + "&response_type=code"
                + "&access_type=offline";
    }

    public String getDiscordAuthorizationUrl() {
        String baseUrl = "https://discord.com/oauth2/authorize";
        String scope = "email identify";

        return baseUrl + "?" + "client_id="
                + URLEncoder.encode(discordClientId, StandardCharsets.UTF_8) + "&redirect_uri="
                + URLEncoder.encode(discordRedirectUri, StandardCharsets.UTF_8) + "&scope="
                + URLEncoder.encode(scope, StandardCharsets.UTF_8) + "&response_type=code";
    }

    public OAuth2UserInfo handleGoogleCallback(String code) throws IOException {
        // Exchange code for access token
        String accessToken = exchangeGoogleCodeForToken(code);

        // Get user info using access token
        return getGoogleUserInfo(accessToken);
    }

    public OAuth2UserInfo handleDiscordCallback(String code) throws IOException {
        // Exchange code for access token
        String accessToken = exchangeDiscordCodeForToken(code);

        // Get user info using access token
        return getDiscordUserInfo(accessToken);
    }

    private String exchangeGoogleCodeForToken(String code) throws IOException {
        String tokenUrl = "https://oauth2.googleapis.com/token";

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("client_id", googleClientId);
        params.add("client_secret", googleClientSecret);
        params.add("code", code);
        params.add("grant_type", "authorization_code");
        params.add("redirect_uri", googleRedirectUri);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(tokenUrl, request, String.class);

        JsonNode jsonNode = objectMapper.readTree(response.getBody());
        return jsonNode.get("access_token").asText();
    }

    private String exchangeDiscordCodeForToken(String code) throws IOException {
        String tokenUrl = "https://discord.com/api/oauth2/token";

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("client_id", discordClientId);
        params.add("client_secret", discordClientSecret);
        params.add("code", code);
        params.add("grant_type", "authorization_code");
        params.add("redirect_uri", discordRedirectUri);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(tokenUrl, request, String.class);

        JsonNode jsonNode = objectMapper.readTree(response.getBody());
        return jsonNode.get("access_token").asText();
    }

    private OAuth2UserInfo getGoogleUserInfo(String accessToken) throws IOException {
        String userInfoUrl = "https://www.googleapis.com/oauth2/v2/userinfo";

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);

        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(userInfoUrl, HttpMethod.GET, request, String.class);

        JsonNode jsonNode = objectMapper.readTree(response.getBody());

        String email = jsonNode.get("email").asText();
        String name = jsonNode.has("name") ? jsonNode.get("name").asText() : null;

        // If name is empty or null, use email prefix
        if (name == null || name.trim().isEmpty()) {
            name = extractEmailPrefix(email);
        }

        return OAuth2UserInfo.builder()
                .email(email)
                .name(name)
                .avatarUrl(jsonNode.has("picture") ? jsonNode.get("picture").asText() : null)
                .build();
    }

    private OAuth2UserInfo getDiscordUserInfo(String accessToken) throws IOException {
        String userInfoUrl = "https://discord.com/api/users/@me";

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);

        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(userInfoUrl, HttpMethod.GET, request, String.class);

        JsonNode jsonNode = objectMapper.readTree(response.getBody());

        String avatarUrl = null;
        if (jsonNode.has("avatar") && !jsonNode.get("avatar").isNull()) {
            String userId = jsonNode.get("id").asText();
            String avatarHash = jsonNode.get("avatar").asText();
            avatarUrl = "https://cdn.discordapp.com/avatars/" + userId + "/" + avatarHash + ".png";
        }

        String email = jsonNode.get("email").asText();
        String name =
                jsonNode.has("global_name") && !jsonNode.get("global_name").isNull()
                        ? jsonNode.get("global_name").asText()
                        : (jsonNode.has("username") ? jsonNode.get("username").asText() : null);

        // If name is empty or null, use email prefix
        if (name == null || name.trim().isEmpty()) {
            name = extractEmailPrefix(email);
        }

        return OAuth2UserInfo.builder()
                .email(email)
                .name(name)
                .avatarUrl(avatarUrl)
                .build();
    }

    /**
     * Extract the prefix from an email address (part before @)
     * @param email the email address
     * @return the prefix part of the email
     */
    private String extractEmailPrefix(String email) {
        if (email == null || email.trim().isEmpty()) {
            return "User";
        }

        int atIndex = email.indexOf('@');
        if (atIndex > 0) {
            return email.substring(0, atIndex);
        }

        return email; // fallback if no @ found
    }
}
