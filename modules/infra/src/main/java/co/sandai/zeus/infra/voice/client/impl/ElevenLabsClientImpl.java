package co.sandai.zeus.infra.voice.client.impl;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.infra.voice.client.ElevenLabsClient;
import co.sandai.zeus.infra.voice.config.ElevenLabsConfig;
import co.sandai.zeus.infra.voice.dto.ElevenLabsVoiceDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * Implementation of the ElevenLabsClient interface
 * Handles raw HTTP communication with the ElevenLabs API
 */
@Component
@Slf4j
public class ElevenLabsClientImpl implements ElevenLabsClient {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final ElevenLabsConfig config;

    @Value("${elevenlabs.collection.id:}")
    private String collectionId;

    public ElevenLabsClientImpl(ElevenLabsConfig config) {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
        this.config = config;
    }

    /**
     * Generic method to fetch paginated results from the ElevenLabs API
     *
     * @param baseUrl the base URL for the API endpoint
     * @param queryParams initial query parameters (without pagination params)
     * @param responseDataField the field name in the response containing the data array
     * @param processor function to process each data item from the response
     * @param <T> the type of objects to return
     * @return a list of processed objects from all pages
     * @throws JsonProcessingException if there's an error parsing the response
     * @throws HttpClientErrorException if there's an HTTP error in the request
     */
    private <T> List<T> fetchPaginatedResults(
            String baseUrl,
            Map<String, String> queryParams,
            String responseDataField,
            Function<Map<String, Object>, T> processor)
            throws JsonProcessingException, HttpClientErrorException {

        // Set up headers with API key
        HttpHeaders headers = getHeaders();

        // Create HTTP entity with headers
        HttpEntity<String> entity = new HttpEntity<>(headers);

        // List to store all results from all pages
        List<T> allResults = new ArrayList<>();

        // Pagination handling
        String nextPageToken = null;
        boolean hasMore = true;

        while (hasMore) {
            // Build URL with query parameters and pagination token
            StringBuilder urlBuilder = new StringBuilder(baseUrl);

            // Add query parameters
            boolean hasParams = false;

            if (queryParams != null && !queryParams.isEmpty()) {
                urlBuilder.append('?');
                hasParams = true;

                int paramCount = 0;
                for (Map.Entry<String, String> param : queryParams.entrySet()) {
                    if (paramCount > 0) {
                        urlBuilder.append('&');
                    }
                    urlBuilder.append(param.getKey()).append('=').append(param.getValue());
                    paramCount++;
                }
            }

            if (nextPageToken != null) {
                urlBuilder.append(hasParams ? "&" : "?");
                urlBuilder.append("next_page_token=").append(nextPageToken);
            }

            String url = urlBuilder.toString();
            log.debug("Fetching paginated data, URL: {}", url);

            // Send request
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            // Parse response
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = objectMapper.readValue(response.getBody(), Map.class);

            // Extract pagination information
            nextPageToken = (String) responseMap.get("next_page_token");
            hasMore = Boolean.TRUE.equals(responseMap.get("has_more"));
            log.debug("Pagination info - has_more: {}, next_page_token: {}", hasMore, nextPageToken);

            // Extract data items
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataItems = (List<Map<String, Object>>) responseMap.get(responseDataField);

            if (dataItems == null || dataItems.isEmpty()) {
                log.debug("No data found in current page");
                // No more data despite what the API might have indicated
                break;
            }

            // Process data items in this page
            for (Map<String, Object> dataItem : dataItems) {
                T processedItem = processor.apply(dataItem);
                if (processedItem != null) {
                    allResults.add(processedItem);
                }
            }

            log.debug(
                    "Fetched {} items from current page, total items so far: {}", dataItems.size(), allResults.size());

            // If no next_page_token or has_more is false, exit the loop
            if (nextPageToken == null || !hasMore) {
                break;
            }
        }

        log.info("Total items fetched: {}", allResults.size());
        return allResults;
    }

    @Override
    public List<ElevenLabsVoiceDTO> getVoices() {
        try {
            // Set up query parameters
            Map<String, String> queryParams = new HashMap<>();
            if (collectionId != null && !collectionId.isEmpty()) {
                queryParams.put("collection_id", collectionId);
            }

            // Use the generic pagination method with a processor for voice data
            return fetchPaginatedResults(
                    config.getBaseUrl() + "v2/voices", queryParams, "voices", this::processVoiceData);

        } catch (HttpClientErrorException e) {
            log.error("ElevenLabs API error: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Failed to get voices from ElevenLabs: " + e.getMessage());
        } catch (JsonProcessingException e) {
            log.error("Error parsing ElevenLabs API response: {}", e.getMessage());
            throw new ZeusServiceException(ErrorCode.ThirdPartyError, "Error parsing response from ElevenLabs");
        }
    }

    /**
     * Process a single voice data item from the API response
     *
     * @param voiceData the map containing voice data
     * @return the processed ElevenLabsVoiceDTO object
     */
    private ElevenLabsVoiceDTO processVoiceData(Map<String, Object> voiceData) {
        ElevenLabsVoiceDTO voice = new ElevenLabsVoiceDTO();
        voice.setVoiceId((String) voiceData.get("voice_id"));
        voice.setName((String) voiceData.get("name"));
        voice.setDescription((String) voiceData.getOrDefault("description", ""));

        // Extract preview URL if available
        String previewUrl = (String) voiceData.get("preview_url");
        // Extract language from labels.language field if available
        Object labelsObj = voiceData.get("labels");
        if (labelsObj instanceof Map<?, ?>) {
            Map<?, ?> labels = (Map<?, ?>) labelsObj;
            Object lang = labels.get("language");
            if (lang instanceof String) {
                voice.setLanguage((String) lang);
            }
        }
        if (previewUrl != null && !previewUrl.isEmpty()) {
            voice.setPreviewUrl(previewUrl);
        }

        // verified_languages is an array of language objects
        List<Object> verifiedLanguagesObj = (List<Object>) voiceData.get("verified_languages");
        if (verifiedLanguagesObj != null) {
            try {
                // Convert the array to a JSON string
                String verifiedLanguagesJson = objectMapper.writeValueAsString(verifiedLanguagesObj);
                voice.setVerifiedLanguages(verifiedLanguagesJson);
            } catch (JsonProcessingException e) {
                log.error("Error serializing verified languages to JSON", e);
            }
        }

        // Set cloned flag
        voice.setCloned((Boolean) voiceData.getOrDefault("is_cloned", false));

        return voice;
    }

    // Custom Resource handling for file name and Content-Type
    static class MultipartFileResource extends AbstractResource {
        private final MultipartFile file;

        public MultipartFileResource(MultipartFile file) {
            this.file = file;
        }

        @Override
        public String getFilename() {
            return file.getOriginalFilename();
        }

        @Override
        public String getDescription() {
            return "File: " + file.getOriginalFilename();
        }

        @Override
        public InputStream getInputStream() {
            try {
                return file.getInputStream();
            } catch (IOException e) {
                log.error("Error getting input stream from MultipartFile: {}", e.getMessage());
                throw new ZeusServiceException(ErrorCode.ThirdPartyError, "Error preparing audio file");
            }
        }

        @Override
        public long contentLength() {
            return file.getSize();
        }
    }

    @Override
    public ElevenLabsVoiceDTO createVoice(String name, String description, List<MultipartFile> multiPartFileList) {
        try {
            // Set up headers with API key
            HttpHeaders headers = getHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("name", name);
            body.add("description", description);
            for (MultipartFile file : multiPartFileList) {
                ByteArrayResource resource = new ByteArrayResource(file.getBytes()) {
                    @Override
                    public String getFilename() {
                        return file.getOriginalFilename();
                    }
                };
                body.add("files", resource);
            }
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // Send request to ElevenLabs API
            ResponseEntity<String> response = restTemplate.exchange(
                    config.getBaseUrl() + "v1/voices/add", HttpMethod.POST, requestEntity, String.class);

            // Check HTTP status code and handle non-successful responses
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error(
                        "ElevenLabs createVoice failed, status: {}, body: {}",
                        response.getStatusCode(),
                        response.getBody());
                throw new ZeusServiceException(
                        ErrorCode.ThirdPartyError, "Failed to create voice in ElevenLabs: " + response.getStatusCode());
            }

            // Parse response
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = objectMapper.readValue(response.getBody(), Map.class);

            // Create voice DTO from response
            ElevenLabsVoiceDTO voice = new ElevenLabsVoiceDTO();
            voice.setVoiceId((String) responseMap.get("voice_id"));
            voice.setName(name);
            voice.setDescription(description);
            voice.setCloned(true);

            // Assuming we get a preview URL in the response
            if (responseMap.containsKey("preview_url")) {
                String previewUrl = (String) responseMap.get("preview_url");
                voice.setPreviewUrl(previewUrl);
            }

            return voice;

        } catch (HttpClientErrorException e) {
            log.error("ElevenLabs API error: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Failed to create voice in ElevenLabs: " + e.getMessage());
        } catch (JsonProcessingException e) {
            log.error("Error parsing ElevenLabs API response: {}", e.getMessage());
            throw new ZeusServiceException(ErrorCode.ThirdPartyError, "Error parsing response from ElevenLabs");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public byte[] textToSpeech(String voiceId, String text) {
        try {
            // Set up headers with API key
            HttpHeaders headers = getHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));

            // Prepare request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);
            requestBody.put(
                    "voice_settings",
                    Map.of(
                            "stability", 0.5,
                            "similarity_boost", 0.75));

            // Create HTTP entity
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // Send request to ElevenLabs API
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    config.getBaseUrl() + "v1/text-to-speech/" + voiceId + "/stream",
                    HttpMethod.POST,
                    requestEntity,
                    byte[].class);

            // Return audio data
            byte[] audioData = response.getBody();
            if (audioData != null && audioData.length > 0) {
                return audioData;
            } else {
                throw new ZeusServiceException(
                        ErrorCode.ThirdPartyError, "Empty response from ElevenLabs text-to-speech API");
            }
        } catch (HttpClientErrorException e) {
            log.error("ElevenLabs API error: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Failed to convert text to speech: " + e.getMessage());
        }
    }

    @Override
    public String detectLanguage(String text) {
        try {
            // Set up headers with API key
            HttpHeaders headers = getHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // Prepare request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);

            // Create HTTP entity
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // Send request to ElevenLabs language detection API
            ResponseEntity<Map> responseEntity = restTemplate.exchange(
                    config.getBaseUrl() + "v1/language-detection", HttpMethod.POST, requestEntity, Map.class);

            // Parse response
            @SuppressWarnings("unchecked")
            Map<String, Object> responseBody = responseEntity.getBody();
            if (responseBody != null && responseBody.containsKey("detected_language")) {
                return responseBody.get("detected_language").toString();
            }

            // Default to English if detection fails
            return "en";
        } catch (HttpClientErrorException e) {
            log.error("ElevenLabs API language detection error: {}", e.getMessage());
            // Default to English if API call fails
            return "en";
        }
    }

    /**
     * Download the voice preview content from the provided URL
     * @param previewUrl URL of the preview audio file
     * @return Byte array containing the audio data
     */
    public byte[] downloadVoicePreview(String previewUrl) {
        try {
            HttpHeaders headers = getHeaders();
            ResponseEntity<byte[]> response =
                    restTemplate.exchange(previewUrl, HttpMethod.GET, new HttpEntity<>(headers), byte[].class);

            return response.getBody();
        } catch (HttpClientErrorException e) {
            log.error("Error downloading voice preview: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Failed to download voice preview from ElevenLabs: " + e.getMessage());
        }
    }

    private HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("xi-api-key", config.getApiKey());
        return headers;
    }

    @Override
    public boolean deleteVoice(String voiceId) {
        try {
            // Set up headers with API key
            HttpHeaders headers = getHeaders();

            // Create HTTP entity with headers
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // Send DELETE request to ElevenLabs API
            ResponseEntity<String> response = restTemplate.exchange(
                    config.getBaseUrl() + "v1/voices/" + voiceId, HttpMethod.DELETE, entity, String.class);

            // Return success status based on HTTP response
            return response.getStatusCode().is2xxSuccessful();

        } catch (HttpClientErrorException e) {
            log.error("ElevenLabs API error when deleting voice {}: {}", voiceId, e.getMessage());
            // Return false to indicate deletion failed rather than throwing exception
            return false;
        } catch (Exception e) {
            log.error("Unexpected error when deleting voice {}: {}", voiceId, e.getMessage(), e);
            // Return false to indicate deletion failed rather than throwing exception
            return false;
        }
    }

    @Override
    public String speechToText(byte[] audioData, String language) {
        try {
            // Set up headers with API key
            HttpHeaders headers = getHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // Create multipart request
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // Add audio data
            ByteArrayResource resource = new ByteArrayResource(audioData) {
                @Override
                public String getFilename() {
                    return "audio.mp3"; // Default filename
                }
            };
            body.add("file", resource); // Parameter name must be 'file' for ElevenLabs API

            // Add language if provided
            if (language != null && !language.isEmpty()) {
                body.add("language", language);
            }

            // Add required model_id parameter (using a valid model ID for speech-to-text)
            body.add("model_id", "scribe_v1"); // Use ElevenLabs' speech-to-text model

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // Send request to ElevenLabs API
            ResponseEntity<String> response = restTemplate.exchange(
                    config.getBaseUrl() + "v1/speech-to-text", HttpMethod.POST, requestEntity, String.class);

            // Parse the JSON response
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = objectMapper.readValue(response.getBody(), Map.class);

            // Extract the transcribed text
            if (responseMap.containsKey("text")) {
                return (String) responseMap.get("text");
            } else {
                log.error("ElevenLabs API response did not contain text field: {}", response.getBody());
                throw new ZeusServiceException(
                        ErrorCode.VoiceSpeechToTextFailed, "Failed to extract text from API response");
            }

        } catch (HttpClientErrorException e) {
            log.error("ElevenLabs API error during speech-to-text conversion: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.VoiceSpeechToTextFailed, "Failed to convert speech to text: " + e.getMessage());
        } catch (JsonProcessingException e) {
            log.error("Error parsing ElevenLabs API response: {}", e.getMessage());
            throw new ZeusServiceException(ErrorCode.VoiceSpeechToTextFailed, "Error parsing response from ElevenLabs");
        }
    }
}
