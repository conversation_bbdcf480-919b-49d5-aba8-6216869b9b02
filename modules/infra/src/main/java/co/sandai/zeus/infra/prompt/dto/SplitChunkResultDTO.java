package co.sandai.zeus.infra.prompt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SplitChunkResultDTO {

    @JsonProperty("recommend_plots")
    private List<ChunkDTO> recommendChunks;

    @JsonProperty("candidate_plots")
    private List<List<ChunkDTO>> candidateChunksList;
}
