package co.sandai.zeus.infra.prompt;

import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.infra.infer.dto.InferCommonResponse;
import co.sandai.zeus.infra.prompt.dto.*;
import co.sandai.zeus.infra.web.log.LogBody;
import com.alibaba.fastjson.JSONObject;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class PromptService {

    @Value("${prompt-enhancement.base-url}")
    private String basePolishUrl;

    @Value("${athena.url}")
    private String athenaUrl;

    @Value("${athena.api.key}")
    private String athenaApiKey;

    @Value("${inspiration.prompt.mode}")
    private String inspirationPromptMode;

    @Value("${coze.inspiration.app.id}")
    private String cozeInspirationAppId;

    @Value("${coze.ar.caption.app.id}")
    private String cozeArCaptionAppId;

    @Value("${coze.gen.story.workflow.id}")
    private String cozeGenStoryWorkflowId;

    @Value("${coze.split.chunk.workflow.id}")
    private String cozeSplitChunkWorkflowId;

    @Value("${coze.regen.chunk.workflow.id}")
    private String cozeRegenChunkWorkflowId;

    @Value("${coze.access.token}")
    private String cozeAccessToken;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private CozeTemplateService cozeTemplateService;

    @Value("${coze.ar.caption.pe.workflow.map}")
    private String cozeArCaptionPEWorkflowMapString;

    @LogBody(uri = "/prompt/paraphrase")
    public PromptEnhancementResultDTO enhancePrompt(
            String prompt, String prefixUrl, String prefixType, boolean genFirstFrame, String enhancementType) {

        String url = athenaUrl + "/public/api/v1/prompt/paraphrase";
        // Create headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-KEY", athenaApiKey);

        PromptEnhancementRequest req = PromptEnhancementRequest.builder()
                .textPromptByUser(prompt)
                .prefixType(prefixType)
                .prefixUrl(prefixUrl)
                // .genFirstFrame(false) // Not used
                // .version("v2") // Not used
                .enhancementType(enhancementType)
                .build();

        // Create request entity with headers and body
        HttpEntity<PromptEnhancementRequest> requestEntity = new HttpEntity<>(req, headers);

        // Use exchange method instead of postForObject to include headers
        try {
            // Use InferCommonResponse to handle the nested data structure
            ParameterizedTypeReference<InferCommonResponse<PromptEnhancementResultDTO>> responseType =
                    new ParameterizedTypeReference<InferCommonResponse<PromptEnhancementResultDTO>>() {};

            ResponseEntity<InferCommonResponse<PromptEnhancementResultDTO>> response =
                    restTemplate.exchange(url, HttpMethod.POST, requestEntity, responseType);

            InferCommonResponse<PromptEnhancementResultDTO> wrapper = response.getBody();
            // Check for error conditions and extract result
            if (wrapper == null || wrapper.getData() == null) {
                LogUtil.error(log, "Invalid response from prompt enhancement API");
                throw new RuntimeException("Invalid response from prompt enhancement API");
            }

            if (wrapper.getError() != null && !wrapper.getError().isEmpty()) {
                LogUtil.errorf(log, "API error: {0}", wrapper.getError());
                throw new RuntimeException("API error: " + wrapper.getError());
            }

            // Handle response data
            PromptEnhancementResultDTO result = wrapper.getData();
            if (result.getGeneratedImagePrompt() == null) {
                result.setGeneratedImagePrompt("");
            }

            return result;
        } catch (Exception e) {
            LogUtil.errorf(log, "Error occurred while enhancing prompt: {0}", e);
            throw new RuntimeException("Failed to enhance prompt", e);
        }
    }

    @LogBody(uri = "/prompt/suggestions")
    public List<PromptSuggestionDTO> getSuggestions(
            String prompt,
            String prefixUrl,
            String prefixType,
            int suggestionCnt,
            boolean enablePE,
            String enhancementType,
            String suggestionType,
            Map<String, Integer> cropArea) {
        String url = athenaUrl + "/public/api/v1/prompt/suggestions";

        // Create headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-KEY", athenaApiKey);

        // Create request body
        Map<String, Object> requestBody = new HashMap<>();

        requestBody.put("text_prompt", prompt);
        requestBody.put("prefix_url", prefixUrl);
        requestBody.put("prefix_type", prefixType);
        requestBody.put("suggestion_cnt", suggestionCnt);
        requestBody.put("suggestion_type", suggestionType);
        requestBody.put("enable_enhancement", enablePE);
        requestBody.put("enhancement_type", enhancementType);
        requestBody.put("crop_area", cropArea);
        // Create request entity
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            ParameterizedTypeReference<InferCommonResponse<JSONObject>> typeReference =
                    new ParameterizedTypeReference<>() {};
            ResponseEntity<InferCommonResponse<JSONObject>> response =
                    restTemplate.exchange(url, HttpMethod.POST, requestEntity, typeReference);

            if (response != null && response.getBody() != null) {
                JSONObject data = response.getBody().getData();

                // Log suggestion_type and enhancement_type fields from the response
                String realSuggestionType = data.getString("suggestion_type");
                String realEnhancementType = data.getString("enhancement_type");
                LogUtil.infof(
                        log,
                        "Received suggestion_type: {0}, enhancement_type: {1}",
                        realSuggestionType,
                        realEnhancementType);

                // Get suggestions array with explicit JSONObject type
                List<JSONObject> suggestionsJson =
                        data.getJSONArray("suggestions").toJavaList(JSONObject.class);

                // Create list to hold the results
                List<PromptSuggestionDTO> result = new ArrayList<>(suggestionsJson.size());

                // Process each suggestion with schema validation
                for (JSONObject suggestion : suggestionsJson) {
                    String promptText = suggestion.getString("prompt");
                    String enhancedPrompt = suggestion.getString("enhanced_prompt");

                    PromptSuggestionDTO dto = new PromptSuggestionDTO();
                    dto.setPrompt(promptText);
                    dto.setEnhancedPrompt(enhancedPrompt);

                    result.add(dto);
                }

                return result;
            } else {
                String errorMsg = "Failed to get prompt suggestions, received null or empty response";
                LogUtil.errorf(log, errorMsg + ": {0}", response);
                throw new RuntimeException(errorMsg);
            }
        } catch (Exception e) {
            String errorMsg = "Error occurred while getting prompt suggestions";
            LogUtil.errorf(log, errorMsg + ": {0}", e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    public String enhanceTextPrompt(String prompt, String enhancementType) {
        PromptEnhancementResultDTO res = enhancePrompt(prompt, null, null, false, enhancementType);
        return res.getGeneratedPrompt();
    }

    /**
     * 并发增强多个文本提示词
     *
     * @param chunks          提示词列表
     * @param enhancementType 增强类型
     * @return 增强后的提示词结果列表
     */
    public List<String> enhanceTextPrompts(List<ChunkDTO> chunks, String enhancementType, String imageUrl) {
        if ("coze".equalsIgnoreCase(inspirationPromptMode)) {
            JSONObject parameters =
                    new JSONObject().fluentPut("image", imageUrl).fluentPut("plots", chunks);
            String workFlowId = JSONObject.parseObject(cozeArCaptionPEWorkflowMapString)
                    .getString(StringUtils.defaultString(enhancementType, "default"));
            CozeTemplateService.CozeResponse cozeResponse =
                    cozeTemplateService.runWorkflow(parameters, workFlowId, cozeArCaptionAppId);
            String data = cozeResponse.getData();
            if (StringUtils.isNotBlank(data)) {
                return CozeConverter.fromArCaptionResponse(data);
            } else {
                LogUtil.errorf(log, "coze enhance text prompts failed, response: {0}", cozeResponse);
                // 如果返回为空，后面继续执行，相当于用polish方案兜底
                enhancementType = "common_translation";
            }
        }
        final String finalEnhancementType = enhancementType;
        List<CompletableFuture<String>> futures = chunks.stream()
                .map(chunk -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return enhanceTextPrompt(chunk.getPrompt(), finalEnhancementType);
                    } catch (Exception e) {
                        LogUtil.errorf(log, "Failed to enhance prompt: {0}", e, chunk.getPrompt());
                        return null;
                    }
                }))
                .toList();

        return futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
    }

    @LogBody(uri = "/prompt/enhancement-types")
    public List<String> listEnhancementTypes() {
        String url = athenaUrl + "/public/api/v1/prompt/enhancement-types";

        // Create headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-KEY", athenaApiKey);

        HttpEntity<String> entity = new HttpEntity<>(headers);

        ParameterizedTypeReference<InferCommonResponse<JSONObject>> typeReference =
                new ParameterizedTypeReference<>() {};
        ResponseEntity<InferCommonResponse<JSONObject>> response =
                restTemplate.exchange(url, HttpMethod.GET, entity, typeReference);

        if (response != null && response.getBody() != null) {
            JSONObject body = response.getBody().getData();
            return Arrays.asList(body.getObject("enhancement_types", String[].class));
        } else {
            LogUtil.errorf(log, "Failed to get prompt enhancement types, response: {0}", response);
            return Collections.emptyList();
        }
    }

    @LogBody(uri = "/generateStory")
    public GenStoryResultDTO genStory(String prompt, String imageUrl, int count) {
        if ("coze".equalsIgnoreCase(inspirationPromptMode)) {
            JSONObject parameters =
                    new JSONObject().fluentPut("textprompt", prompt).fluentPut("image", imageUrl);
            CozeTemplateService.CozeResponse cozeResponse =
                    cozeTemplateService.runWorkflow(parameters, cozeGenStoryWorkflowId, cozeInspirationAppId);
            String data = cozeResponse.getData();
            if (StringUtils.isNotBlank(data)) {
                JSONObject jsonObject = JSONObject.parseObject(data);
                GenStoryResultDTO result = new GenStoryResultDTO();
                result.setGeneratedStories(jsonObject.entrySet().stream()
                        .map(entry -> String.valueOf(entry.getValue()))
                        .collect(Collectors.toList()));
                return result;
            } else {
                LogUtil.errorf(log, "coze generate story failed, response: {0}", cozeResponse);
                // 如果返回为空，后面继续执行，相当于用polish方案兜底
            }
        }

        String url = basePolishUrl + "/generateStory";
        GenStoryRequest req = GenStoryRequest.builder()
                .storyPrompt(prompt)
                .imageUrl(imageUrl)
                .count(count)
                .build();
        return restTemplate.postForObject(url, req, GenStoryResultDTO.class);
    }

    @LogBody(uri = "/generateChunkPrompt")
    public SplitChunkResultDTO splitChunk(String imageUrl, String storyPrompt, Integer chunkCount) {
        if ("coze".equalsIgnoreCase(inspirationPromptMode)) {
            JSONObject parameters =
                    new JSONObject().fluentPut("story", storyPrompt).fluentPut("image", imageUrl);
            CozeTemplateService.CozeResponse cozeResponse =
                    cozeTemplateService.runWorkflow(parameters, cozeSplitChunkWorkflowId, cozeInspirationAppId);
            String data = cozeResponse.getData();
            if (StringUtils.isNotBlank(data)) {
                return CozeConverter.fromRawDataToSplitChunkResultDTO(data);
            } else {
                LogUtil.errorf(log, "coze generate chunk prompt failed, response: {0}", cozeResponse);
                // 如果返回为空，后面继续执行，相当于用polish方案兜底
            }
        }
        String url = basePolishUrl + "/generateChunkPrompt";
        SplitChunkRequest req = new SplitChunkRequest().setImageUrl(imageUrl).setStoryPrompt(storyPrompt);
        return restTemplate.postForObject(url, req, SplitChunkResultDTO.class);
    }

    @LogBody(uri = "/updateChunkPrompt")
    public UpdateChunkPromptResultDTO updateChunkPrompt(UpdateChunkPromptRequestDTO request) {
        if ("coze".equalsIgnoreCase(inspirationPromptMode)) {
            JSONObject parameters = new JSONObject()
                    .fluentPut("inputplots", CozeConverter.fromChangeableChunksToPlotStr(request.getChunks()))
                    .fluentPut("image", request.getImageUrl());
            CozeTemplateService.CozeResponse cozeResponse =
                    cozeTemplateService.runWorkflow(parameters, cozeRegenChunkWorkflowId, cozeInspirationAppId);
            String data = cozeResponse.getData();
            if (StringUtils.isNotBlank(data)) {
                return CozeConverter.fromRawDataToUpdateChunkPromptResultDTO(data);
            } else {
                LogUtil.errorf(log, "coze update chunk prompt failed, response: {0}", cozeResponse);
                // 如果返回为空，后面继续执行，相当于用polish方案兜底
            }
        }

        String url = basePolishUrl + "/updateChunkPrompt";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateChunkPromptRequestDTO> entity = new HttpEntity<>(request, headers);
        return restTemplate.postForObject(url, entity, UpdateChunkPromptResultDTO.class);
    }
}
