package co.sandai.zeus.infra;

import co.sandai.zeus.common.utils.StringUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.ImageModerationRequest;
import com.aliyun.green20220302.models.ImageModerationResponse;
import com.aliyun.green20220302.models.ImageModerationResponseBody;
import com.aliyun.green20220302.models.TextModerationRequest;
import com.aliyun.green20220302.models.TextModerationResponse;
import com.aliyun.green20220302.models.TextModerationResponseBody;
import com.aliyun.green20220302.models.VideoModerationRequest;
import com.aliyun.green20220302.models.VideoModerationResponse;
import com.aliyun.green20220302.models.VideoModerationResponseBody;
import com.aliyun.green20220302.models.VideoModerationResultResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.ExtendsParameters;
import com.aliyun.teautil.models.RuntimeOptions;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import java.util.*;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Getter
public class Green {

    @Value("${aliyun.ak}")
    private String aliyunAk;

    @Value("${aliyun.sk}")
    private String aliyunSk;

    @Value("${aliyun.green.endpoint:green-cip.cn-shanghai.aliyuncs.com}")
    private String endpoint;

    @Value("${aliyun.green.image-service:baselineCheck}")
    private String imageService;

    @Value("${aliyun.green.video-service:videoDetection}")
    private String videoService;

    @Value("${aliyun.green.video-result-callback}")
    private String videoResultCallback;

    @Value("${aliyun.green.ga-ip:}")
    private String gaIPAddr;

    @Autowired
    private GreenOpenApiSDK greenOpenApiSDK;

    Client client;

    private RuntimeOptions getRuntimeOptions() {
        RuntimeOptions options = new RuntimeOptions();

        Map<String, String> headers = new HashMap<>();

        ExtendsParameters extendsParameters = new ExtendsParameters();
        extendsParameters.setHeaders(headers);

        options.setExtendsParameters(extendsParameters);
        options.extendsParameters.setHeaders(headers);

        return options;
    }

    @PostConstruct
    public void init() {
        try {
            Config config = new Config().setAccessKeyId(aliyunAk).setAccessKeySecret(aliyunSk);
            config.setEndpoint(endpoint);
            config.setRegionId("cn-shanghai");
            config.setProtocol("https");
            config.setConnectTimeout(10000);
            config.setReadTimeout(120000);
            client = new Client(config);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Builder
    @Getter
    public static class ModerateTextResult {
        long userId;
        boolean isValid;
        String reason;
        Float confidence;
    }

    @Builder
    @Getter
    public static class ModerateImageResult {
        long assetId;
        boolean isValid;
        String riskLevel;
        String reason;
        Float confidence;
    }

    @Builder
    @Getter
    public static class ModerateVideoResult {
        long dataId;
        boolean isValid;
        String riskLevel;
        Float confidence;
    }

    @Builder
    @Getter
    public static class ModerateVideoCallbackRequest {
        String checksum;
        String content;
    }

    @Builder
    @Getter
    public static class SubmitVideoModerationTaskResult {
        String seed;
        String taskId;
    }

    private boolean isUseGA() {
        return StringUtils.isNoneBlank(gaIPAddr);
    }

    public ModerateImageResult moderateImage(String imageUrl, long assetId) {
        HashMap<String, String> args = new HashMap<>();
        args.put("imageUrl", imageUrl);
        args.put("assetId", String.valueOf(assetId));
        ImageModerationRequest imageModerationRequest =
                new ImageModerationRequest().setService(imageService).setServiceParameters(JSON.toJSONString(args));
        log.info("moderate image, url: {}, asset id: {}", imageUrl, assetId);
        try {
            ImageModerationResponseBody body;
            if (isUseGA()) {
                body = greenOpenApiSDK.moderateImage(imageService, args);
            } else {
                RuntimeOptions runtime = getRuntimeOptions();
                ImageModerationResponse res = client.imageModerationWithOptions(imageModerationRequest, runtime);
                body = res.getBody();
            }
            ImageModerationResponseBody.ImageModerationResponseBodyData data = body.getData();
            if (Objects.isNull(data)) {
                throw new RuntimeException(body.getMsg());
            }

            String riskLevel = data.getRiskLevel();
            boolean valid = riskLevel.equals("none") || riskLevel.equals("low");

            // 初始化置信度和原因
            Float confidence = null;
            String reason = "";

            if (!valid) {
                // 审核不通过时，直接获取第一个结果（一定存在）
                var firstResult = data.getResult().getFirst();
                reason = firstResult.getLabel();

                // 尝试提取置信度值
                try {
                    confidence = firstResult.getConfidence();
                } catch (Exception e) {
                    log.debug("Could not extract confidence from image moderation result: {}", e.getMessage());
                }
            }

            log.debug("Image moderation result confidence: {}", confidence);

            return ModerateImageResult.builder()
                    .assetId(assetId)
                    .riskLevel(data.getRiskLevel())
                    .isValid(valid)
                    .confidence(confidence)
                    .reason(reason)
                    .build();
        } catch (TeaException error) {
            log.error("image moderation get tea err: {}", error.getData().get("Recommend"));
            throw new RuntimeException(error);
        } catch (Exception error) {
            throw new RuntimeException(error);
        }
    }

    public ModerateTextResult moderateText(String content, long accountId) {
        if (Objects.isNull(content) || content.isBlank()) {
            return ModerateTextResult.builder().isValid(true).build();
        }
        String truncatedContent = content;
        if (content.length() > 600) {
            truncatedContent = content.substring(0, 600);
        }
        try {
            String service = "comment_multilingual_pro";
            HashMap<String, String> args = new HashMap<>();
            args.put("content", truncatedContent);
            args.put("accountId", String.valueOf(accountId));
            TextModerationResponseBody body;
            if (isUseGA()) {
                body = greenOpenApiSDK.moderationText(service, args);
            } else {

                ObjectMapper objectMapper = new ObjectMapper();
                String argsJson = objectMapper.writeValueAsString(args);

                TextModerationRequest textModerationRequest =
                        new TextModerationRequest().setService(service).setServiceParameters(argsJson);

                RuntimeOptions runtime = getRuntimeOptions();
                TextModerationResponse res = client.textModerationWithOptions(textModerationRequest, runtime);
                body = res.getBody();
            }
            TextModerationResponseBody.TextModerationResponseBodyData data = body.getData();
            if (Objects.isNull(data)) {
                throw new RuntimeException(body.getMessage());
            }
            String labels = data.getLabels();
            String reason = data.getReason();

            // 解析标签集合
            Set<String> labelSet = new HashSet<>(Arrays.asList(labels.split(",")));
            Set<String> invalidLabels = new HashSet<>(Arrays.asList(new String[] {"sexuality", "regional"}));
            invalidLabels.retainAll(labelSet);
            boolean valid = labelSet.isEmpty() || invalidLabels.isEmpty();

            return ModerateTextResult.builder()
                    .isValid(valid)
                    .reason(reason)
                    .userId(accountId)
                    .build();
        } catch (TeaException error) {
            log.error("text moderation get tea err: {}, {}", error.getData().get("Recommend"), error.message);
            throw new RuntimeException(error);
        } catch (Exception error) {
            if (error.getMessage() != null && error.getMessage().contains("timed out")) {
                log.warn("time out, skip moderate text");
                return ModerateTextResult.builder()
                        .isValid(true)
                        .reason("time out, skip moderate text: " + error.getMessage())
                        .userId(accountId)
                        .build();
            } else {
                throw new RuntimeException(error);
            }
        }
    }

    public SubmitVideoModerationTaskResult submitVideoModerationTask(String videoUrl, long dataId) {
        String seed = StringUtil.randomString(10);
        HashMap<String, String> args = new HashMap<>();
        args.put("dataId", Long.toString(dataId));
        args.put("url", videoUrl);
        args.put("callback", videoResultCallback);
        args.put("seed", seed);
        try {
            VideoModerationResponseBody body;
            if (isUseGA()) {
                body = greenOpenApiSDK.moderateVideo(videoService, args);
            } else {
                String argsJson = JSON.toJSONString(args);
                VideoModerationRequest videoModerationRequest =
                        new VideoModerationRequest().setService(videoService).setServiceParameters(argsJson);
                RuntimeOptions runtimeOptions = getRuntimeOptions();
                VideoModerationResponse res = client.videoModerationWithOptions(videoModerationRequest, runtimeOptions);
                body = res.getBody();
            }
            VideoModerationResponseBody.VideoModerationResponseBodyData data = body.getData();
            if (Objects.isNull(data)) {
                throw new RuntimeException(body.getMessage());
            }
            return SubmitVideoModerationTaskResult.builder()
                    .taskId(data.getTaskId())
                    .seed(seed)
                    .build();
        } catch (TeaException error) {
            log.error("video moderation get tea err: {}", error.getData().get("Recommend"));
            throw new RuntimeException(error);
        } catch (Exception error) {
            throw new RuntimeException(error);
        }
    }

    public ModerateVideoResult parseVideoModerationCallbackRequest(ModerateVideoCallbackRequest request) {
        String content = request.getContent();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            @SuppressWarnings("unchecked")
            HashMap<String, Object> args = objectMapper.readValue(content, HashMap.class);
            VideoModerationResultResponseBody body = new VideoModerationResultResponseBody();
            VideoModerationResultResponseBody.toModel(args, body);

            VideoModerationResultResponseBody.VideoModerationResultResponseBodyData data = body.getData();
            VideoModerationResultResponseBody.VideoModerationResultResponseBodyDataFrameResult frameResult =
                    data.getFrameResult();

            String dataId = data.getDataId();
            String riskLevel = data.getRiskLevel();

            Float confidence = 0F;
            if (Objects.nonNull(frameResult) && frameResult.getFrameNum() > 0) {
                confidence = frameResult.getFrames().stream()
                        .map(frame -> frame.getResults().stream()
                                .map(result -> result.getResult().stream()
                                        .map(
                                                VideoModerationResultResponseBody
                                                                .VideoModerationResultResponseBodyDataFrameResultFramesResultsResult
                                                        ::getConfidence)
                                        .max(Float::compare)
                                        .orElse(null))
                                .filter(Objects::nonNull)
                                .max(Float::compare)
                                .orElse(null))
                        .filter(Objects::nonNull)
                        .max(Float::compare)
                        .orElse(null);
            }

            VideoModerationResultResponseBody.VideoModerationResultResponseBodyDataAudioResult audioResult =
                    data.getAudioResult();
            if (Objects.nonNull(audioResult)) {
                log.info(
                        "parse video moderation result got audio result, dataId: {}, riskLevel: {}",
                        dataId,
                        audioResult.getRiskLevel());
            }

            boolean valid = riskLevel.equals("none") || riskLevel.equals("low");
            return ModerateVideoResult.builder()
                    .dataId(Long.parseLong(dataId))
                    .isValid(valid)
                    .riskLevel(riskLevel)
                    .confidence(confidence)
                    .build();

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
