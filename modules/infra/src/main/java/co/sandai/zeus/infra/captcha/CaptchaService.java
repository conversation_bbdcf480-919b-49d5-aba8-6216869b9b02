package co.sandai.zeus.infra.captcha;

import co.sandai.zeus.common.log.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CaptchaService implements InitializingBean {

    @Value("${captcha.service.host:captcha.cn-shanghai.aliyuncs.com}")
    private String endpoint;

    @Value("${captcha.sceneId:o8h83s59}")
    private String sceneId;

    @Value("${aliyun.ak}")
    private String accessKeyId;

    @Value("${aliyun.sk}")
    private String accessKeySecret;

    private static com.aliyun.captcha20230305.Client client;

    public boolean checkCaptcha(String captchaVerifyParam) {
        // 创建APi请求
        com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaRequest request =
                new com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaRequest();
        // 本次验证的场景ID，建议传入，防止前端被篡改场景
        request.sceneId = sceneId;
        // 前端传来的验证参数 CaptchaVerifyParam
        request.captchaVerifyParam = captchaVerifyParam;
        // ====================== 发起请求） ======================
        try {
            com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaResponse resp =
                    client.verifyIntelligentCaptcha(request);
            // 建议使用您系统中的日志组件，打印返回
            // 获取验证码验证结果（请注意判空），将结果返回给前端。出现异常建议认为验证通过，优先保证业务可用，然后尽快排查异常原因。
            Boolean captchaVerifyResult = resp.body.result.verifyResult;
            if (captchaVerifyResult == null || !captchaVerifyResult) {
                // 验证失败
                log.info(
                        "Captcha verify failed, captchaVerifyResult: {}, captchaVerifyCode: {}, body: {}",
                        captchaVerifyResult,
                        resp.body.result.verifyCode,
                        resp.body);
                return false;
            }
            return true;
        } catch (Throwable error) {
            // 出现异常建议认为验证通过，优先保证业务可用，然后尽快排查异常原因。
            LogUtil.errorf(log, "Captcha verify failed, error", error);
            return true;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // ====================== 初始化配置 ======================
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        // 设置您的AccessKey ID 和 AccessKey Secret。
        config.accessKeyId = accessKeyId;
        config.accessKeySecret = accessKeySecret;
        // 设置请求地址 国内调用地址 captcha.cn-shanghai.aliyuncs.com   新加坡调用地址 captcha.ap-southeast-1.aliyuncs.com
        config.endpoint = endpoint;
        // 设置连接超时为5000毫秒
        config.connectTimeout = 5000;
        // 设置读超时为5000毫秒
        config.readTimeout = 5000;
        client = new com.aliyun.captcha20230305.Client(config);
    }
}
