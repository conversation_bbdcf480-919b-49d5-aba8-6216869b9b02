package co.sandai.zeus.infra.voice.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for Minimax voice data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MinimaxVoiceDTO {
    private String voiceId;
    private String name;
    private String description;
    private String language;
    private String gender; // Voice gender
    private String previewUrl;
    private boolean isCloned;
}
