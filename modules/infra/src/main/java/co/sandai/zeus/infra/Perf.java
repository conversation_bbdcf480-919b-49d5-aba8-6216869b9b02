package co.sandai.zeus.infra;

import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Perf {
    public static <T> T measureExecutionTime(String funcName, Supplier<T> function) {
        long startTime = System.currentTimeMillis();
        T result = function.get();
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        if (duration >= 200) {
            log.warn("function: {}, execution time: {}ms, exceed 200ms", funcName, duration);
        }
        return result;
    }
}
