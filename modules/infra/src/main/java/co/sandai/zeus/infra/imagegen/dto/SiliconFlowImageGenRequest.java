package co.sandai.zeus.infra.imagegen.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Data
@Accessors(chain = true)
@Slf4j
public class SiliconFlowImageGenRequest {

    private String prompt;

    private String model = "black-forest-labs/FLUX.1-schnell";

    @JsonProperty("image_size")
    private String imageSize = "1024x576";

    /**
     * 生成图片的数量
     * 看官方文档, FLUX免费模型只只支持1~4
     */
    @JsonProperty("batch_size")
    private Integer batchSize;

    @JsonProperty("prompt_enhancement")
    private Boolean promptEnhancement;

    /**
     * 随机指定风格
     * @param prompt
     * @return
     */
    public SiliconFlowImageGenRequest setPrompt(String prompt) {
        String style = STYLE_PROMPT.get((int) (Math.random() * STYLE_PROMPT.size()));
        this.prompt = prompt + ". " + style;
        return this;
    }

    public SiliconFlowImageGenRequest setAspectRatio(SizeValueObject size, ModelEnum modelEnum) {
        boolean appoint = false;
        if (size.getHeight() != null && size.getWidth() != null) {
            appoint = true;
        }
        size.calculate();
        int w = size.getWidth();
        int h = size.getHeight();
        //        if (modelEnum.isCharge() && !appoint) {
        //            // 付费场景，节省一点
        //            w = w * 3 / 4;
        //            h = h * 3 / 4;
        //        }
        this.imageSize = w + "x" + h;
        return this;
    }

    public void setWH(int w, int h) {
        this.imageSize = w + "x" + h;
    }

    @AllArgsConstructor
    @Getter
    public enum ModelEnum {

        /**
         * 这个rate limit 每分钟2次目前，很多时候不够用
         */
        FLUX_SCHNELL_FREE("black-forest-labs/FLUX.1-schnell", false, 1),

        FLUX_DEV_CHARGE("black-forest-labs/FLUX.1-dev", true, 1),
        ;

        private String name;
        /**
         * 是否收费
         */
        private boolean charge;

        private int maxBatchSize;
    }

    @Data
    @Accessors(chain = true)
    public static class ModelParam {
        private ModelEnum modelEnum;
        /**
         * 是否需要pe
         */
        private Boolean promptEnhancement;
    }

    /**
     * Preset Style	UUID
     * 3D Render
     * Acrylic
     * Anime General
     * Creative
     * Dynamic
     * Fashion
     * Game Concept
     * Graphic Design 3D
     * Illustration
     * None
     * Portrait
     * Portrait Cinematic
     * Ray Traced
     * Stock Photo
     * Watercolor
     */
    private static final List<String> STYLE_PROMPT = Collections.unmodifiableList(Arrays.asList(
            "3D Render",
            "Acrylic",
            "Anime General",
            "Creative",
            "Dynamic",
            "Fashion",
            "Game Concept",
            "Graphic Design 3D",
            "Illustration",
            "None",
            "Portrait",
            "Portrait Cinematic",
            "Ray Traced",
            "Stock Photo",
            "Watercolor"));
}
