package co.sandai.zeus.infra.web.log;

import co.sandai.zeus.common.log.LogUtil;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class WhitelistRegistry {

    private static final Map<String, Boolean> logRequestMap = new LinkedHashMap<>();
    private static final Map<String, Boolean> logResponseMap = new LinkedHashMap<>();

    private static final List<String> uriList = new ArrayList<>();
    private static final String[] EMPTY_ARRAY = new String[] {};

    public static void addUri(LogBody logBody) {
        // 判断之前注册的uri是否是当前的后缀
        if (StringUtils.endsWithAny(logBody.uri(), uriList.toArray(EMPTY_ARRAY))) {
            LogUtil.warnf(
                    log,
                    "URI conflicts: {0}, uri in the list is suffix of current uri, the LogBody control may confused",
                    logBody.uri());
        }
        // 判断当前的uri是否是之前注册过的后缀
        for (String s : uriList) {
            if (StringUtils.endsWith(s, logBody.uri())) {
                LogUtil.warnf(
                        log,
                        "URI conflicts: {0}, current uri is the suffix of uri in the list, the LogBody control may confused",
                        logBody.uri());
            }
        }

        logRequestMap.put(logBody.uri(), logBody.logRequest());
        logResponseMap.put(logBody.uri(), logBody.logResponse());

        uriList.add(logBody.uri());
    }

    /**
     * 在白名单内的才打印http请求body
     * 只要有后缀字符串匹配上，就算是在白名单
     */
    public static boolean shouldLogRequest(String uri) {
        return logRequestMap.entrySet().stream()
                .filter(entry -> uri.matches(".*"
                        + entry.getKey().replace(".", "\\.").replace("?", "\\?").replace("*", ".*")))
                .findFirst()
                .map(Map.Entry::getValue)
                .orElse(false);
    }

    /**
     * 在白名单内的才打印http请求body
     * 只要有后缀字符串匹配上，就算是在白名单
     */
    public static boolean shouldLogResponse(String uri) {
        return logResponseMap.entrySet().stream()
                .filter(entry -> uri.matches(".*"
                        + entry.getKey().replace(".", "\\.").replace("?", "\\?").replace("*", ".*")))
                .findFirst()
                .map(Map.Entry::getValue)
                .orElse(false);
    }
}
