package co.sandai.zeus.infra.infer;

import co.sandai.zeus.infra.web.log.LogBody;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class ArtifactClient {

    @Value("${athena.url}")
    private String athenaUrl;

    @Value("${athena.api.key}")
    private String athenaApiKey;

    @Value("${athena.upload.with.ga}")
    private boolean uploadWithGA = false;

    @Resource
    private RestTemplate restTemplate;

    @LogBody(uri = "/public/api/v1/artifacts/*")
    public ArtifactUploadResponseDTO uploadArtifact(String name, String fileExtension, byte[] content)
            throws IOException {
        return uploadArtifact(name, fileExtension, new ByteArrayInputStream(content));
    }

    public ArtifactUploadResponseDTO uploadArtifact(String name, String fileExtension, InputStream content)
            throws IOException {
        String ext = fileExtension;
        if (!ext.startsWith(".")) {
            ext = "." + ext;
        }
        // Create request body
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("name", name);
        requestBody.put("file_extension", ext);
        requestBody.put("max_allowed_size", 1024 * 1024 * 50); // 50MB
        requestBody.put("upload_expire_seconds", 3600 * 24); // 24 hours
        requestBody.put("upload_with_ga", uploadWithGA);

        // Create headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-KEY", athenaApiKey);

        // Create request entity
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        // Send request using RestTemplate
        ResponseEntity<String> response = restTemplate.exchange(
                athenaUrl + "/public/api/v1/artifacts/", HttpMethod.POST, requestEntity, String.class);

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new IOException("Failed to get upload URL: " + response.getStatusCode());
        }

        // Parse response
        ObjectMapper mapper = new ObjectMapper();
        JsonNode responseJson = mapper.readTree(response.getBody());
        JsonNode data = responseJson.get("data");
        String artifactUrn = data.get("urn").asText();
        String artifactUrl = data.get("url").asText();
        String uploadUrl = data.get("upload_info").get("upload_url").asText();
        JsonNode uploadHeaders = data.get("upload_info").get("headers");

        // Upload content using HttpURLConnection
        URL url = URI.create(uploadUrl).toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("PUT");

        uploadHeaders.fields().forEachRemaining(entry -> {
            connection.setRequestProperty(entry.getKey(), entry.getValue().asText());
        });

        connection.setDoOutput(true);
        try (OutputStream os = connection.getOutputStream()) {
            content.transferTo(os);
            os.flush();
        }

        int responseCode = connection.getResponseCode();
        if (responseCode < 200 || responseCode >= 300) {
            throw new IOException("Failed to upload content: " + responseCode);
        }

        return new ArtifactUploadResponseDTO(artifactUrn, artifactUrl, name);
    }

    public record ArtifactUploadResponseDTO(String urn, String url, String name) {}
}
