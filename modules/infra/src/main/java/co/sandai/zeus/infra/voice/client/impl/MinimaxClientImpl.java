package co.sandai.zeus.infra.voice.client.impl;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.infra.voice.client.MinimaxClient;
import co.sandai.zeus.infra.voice.config.MinimaxConfig;
import co.sandai.zeus.infra.voice.dto.MinimaxVoiceDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * Implementation of the MinimaxClient interface
 * Handles raw HTTP communication with the Minimax API
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MinimaxClientImpl implements MinimaxClient {
    @Value("${zeus.env}")
    private String env;

    public List<MinimaxVoiceDTO> getVoices() {
        log.info("Reading voices from local Minimax JSON file");
        List<MinimaxVoiceDTO> voices = new ArrayList<>();
        String fileName = "minimax_voices_%s.json".formatted(env);
        String filePath = "/voices/" + fileName;
        try {
            log.info("Attempting to load resource: {}", filePath);
            URL resource = getClass().getClassLoader().getResource(filePath.substring(1));
            InputStream stream = resource.openStream();
            voices = processVoicesFromInputStream(stream);
        } catch (Exception e) {
            log.error("Failed to read voices from JSON file: {} {}", filePath, e.getMessage(), e);
            throw ZeusServiceException.internalError("Failed to read voices from JSON file: " + filePath);
        }
        return voices;
    }

    /**
     * Process a JSON input stream to extract voice data
     * @param inputStream InputStream containing JSON voice data
     * @return List of MinimaxVoiceDTO objects
     * @throws IOException if there is an error reading the stream
     */
    protected List<MinimaxVoiceDTO> processVoicesFromInputStream(java.io.InputStream inputStream) throws IOException {
        List<MinimaxVoiceDTO> voices = new ArrayList<>();

        // Parse JSON using type-safe parameterized types
        Map<String, Object> jsonMap = objectMapper.readValue(
                inputStream, objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class));

        if (jsonMap.containsKey("data") && jsonMap.get("data") instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) jsonMap.get("data");

            if (data.containsKey("voice_list") && data.get("voice_list") instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> voiceList = (List<Map<String, Object>>) data.get("voice_list");

                // Map each voice data to MinimaxVoiceDTO
                for (Map<String, Object> voiceData : voiceList) {
                    MinimaxVoiceDTO voice = new MinimaxVoiceDTO();

                    String uniqId = (String) voiceData.get("uniq_id"); // 真正请求tts需要使用这个id
                    // String voiceId =  (String)voiceData.get("voice_id");

                    if (uniqId != null && !uniqId.isEmpty()) {
                        voice.setVoiceId(uniqId);
                    }

                    if (voiceData.containsKey("voice_name") && voiceData.get("voice_name") instanceof String) {
                        voice.setName((String) voiceData.get("voice_name"));
                    }

                    if (voiceData.containsKey("description") && voiceData.get("description") instanceof String) {
                        voice.setDescription((String) voiceData.get("description"));
                    }

                    // Determine language and gender from tags
                    String language = "en"; // Default
                    String gender = null;
                    if (voiceData.containsKey("tag_list") && voiceData.get("tag_list") instanceof List) {
                        @SuppressWarnings("unchecked")
                        List<String> tags = (List<String>) voiceData.get("tag_list");

                        if (tags != null && !tags.isEmpty()) {
                            // 将所有tag转为小写，便于模糊匹配
                            List<String> lowerTags = tags.stream()
                                    .filter(t -> t != null)
                                    .map(t -> t.toLowerCase())
                                    .toList();

                            // 语言匹配
                            if (lowerTags.stream()
                                    .anyMatch(
                                            t -> t.contains("chinese") || t.contains("中文") || t.contains("mandarin"))) {
                                language = "zh";
                            } else if (lowerTags.stream().anyMatch(t -> t.contains("english") || t.contains("en"))) {
                                language = "en";
                            }

                            // 性别匹配
                            if (lowerTags.stream().anyMatch(t -> t.contains("male") || t.contains("男"))) {
                                gender = "male";
                            } else if (lowerTags.stream().anyMatch(t -> t.contains("female") || t.contains("女"))) {
                                gender = "female";
                            }
                        }
                    }
                    voice.setLanguage(language);
                    voice.setGender(gender);

                    if (voiceData.containsKey("sample_audio")) {
                        voice.setPreviewUrl((String) voiceData.get("sample_audio"));
                    }

                    voice.setCloned(true); // Assuming all voices in the list are available
                    voices.add(voice);
                }
            }
        }

        return voices;
    }

    private final MinimaxConfig config;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public MinimaxVoiceDTO createVoice(String name, String description, List<MultipartFile> files) {
        try {
            if (files == null || files.isEmpty()) {
                throw new ZeusServiceException(
                        ErrorCode.InvalidParameters, "At least one audio file is required for voice cloning");
            }

            // Step 1: Upload audio file to get file_id
            Long fileId = uploadAudioFile(files.get(0));

            // Step 2: Clone voice using the file_id
            return cloneVoice(fileId, name, description);
        } catch (HttpClientErrorException e) {
            log.error("Minimax API error: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Failed to create voice in Minimax: " + e.getMessage());
        } catch (IOException e) {
            log.error("Error processing files or parsing Minimax API response: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Error processing request or response from Minimax");
        }
    }

    /**
     * Upload an audio file to Minimax API to get a file_id
     * @param file Audio file (MP3, M4A, WAV)
     * @return File ID assigned by Minimax API
     */
    private Long uploadAudioFile(MultipartFile file) throws IOException {
        // Set up headers with API key
        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // Prepare multipart request
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("purpose", "voice_clone");

        // Add file as ByteArrayResource
        ByteArrayResource fileResource = new ByteArrayResource(file.getBytes()) {
            @Override
            public String getFilename() {
                return file.getOriginalFilename();
            }
        };
        body.add("file", fileResource);

        // Create HTTP entity
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        // Build URL with GroupId parameter
        String url = config.getBaseUrl() + "v1/files/upload";
        if (config.getGroupId() != null && !config.getGroupId().isEmpty()) {
            url += "?GroupId=" + config.getGroupId();
        }

        // Send request to Minimax API
        log.info("Uploading audio file to Minimax API: {}", url);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

        // Check HTTP status code and handle non-successful responses
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("Minimax file upload failed, status: {}, body: {}", response.getStatusCode(), response.getBody());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Failed to upload audio file to Minimax: " + response.getStatusCode());
        }

        // Parse response to get file_id
        Map<String, Object> responseMap = objectMapper.readValue(
                response.getBody(), new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});
        @SuppressWarnings("unchecked")
        Map<String, Object> fileMap = (Map<String, Object>) responseMap.get("file");

        if (fileMap == null || !fileMap.containsKey("file_id")) {
            log.error("Minimax file upload missing file_id in response: {}", response.getBody());
            throw new ZeusServiceException(ErrorCode.ThirdPartyError, "Invalid response from Minimax file upload API");
        }

        Long fileId = ((Number) fileMap.get("file_id")).longValue();
        log.info("Successfully uploaded file to Minimax, file_id: {}", fileId);
        return fileId;
    }

    /**
     * Clone a voice using the uploaded file_id
     * @param fileId File ID returned from file upload API
     * @param name Voice name
     * @param description Voice description
     * @return MinimaxVoiceDTO containing voice details
     */
    private MinimaxVoiceDTO cloneVoice(Long fileId, String name, String description) throws JsonProcessingException {
        // Set up headers with API key
        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Generate a unique voice ID
        // Voice ID must be at least 8 characters, contain letters and numbers, and start with a letter
        String voiceId = "voice" + UUID.randomUUID().toString().replace("-", "").substring(0, 12);

        // Create request body
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("file_id", fileId);
        requestBody.put("voice_id", voiceId);
        requestBody.put("noise_reduction", Boolean.TRUE); // Optional: Enable noise reduction

        // Create HTTP entity
        HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(requestBody), headers);

        // Build URL with GroupId parameter
        String url = config.getBaseUrl() + "v1/voice_clone";
        if (config.getGroupId() != null && !config.getGroupId().isEmpty()) {
            url += "?GroupId=" + config.getGroupId();
        }

        // Send request to Minimax API
        log.info("Sending voice clone request to Minimax API: {}", url);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

        // Check HTTP status code and handle non-successful responses
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("Minimax voice clone failed, status: {}, body: {}", response.getStatusCode(), response.getBody());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Failed to clone voice in Minimax: " + response.getStatusCode());
        }

        // Parse response
        Map<String, Object> responseMap = objectMapper.readValue(
                response.getBody(), new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});
        @SuppressWarnings("unchecked")
        Map<String, Object> baseResp = (Map<String, Object>) responseMap.get("base_resp");

        // Check if voice cloning was successful
        if (baseResp == null
                || !(baseResp.containsKey("status_code") && Integer.valueOf(0).equals(baseResp.get("status_code")))) {
            String errorMsg = baseResp != null && baseResp.containsKey("status_msg")
                    ? (String) baseResp.get("status_msg")
                    : "Unknown error";
            log.error("Minimax voice clone failed: {}", errorMsg);
            throw new ZeusServiceException(ErrorCode.ThirdPartyError, "Failed to clone voice in Minimax: " + errorMsg);
        }

        // Create voice DTO from response
        MinimaxVoiceDTO voice = new MinimaxVoiceDTO();
        voice.setVoiceId(voiceId);
        voice.setName(name);
        voice.setDescription(description);
        voice.setLanguage("zh"); // Default to Chinese as per Minimax API typical usage
        voice.setCloned(true);

        log.info("Successfully cloned voice in Minimax, voice_id: {}", voiceId);
        return voice;
    }

    /**
     * Convert a hex string to a byte array
     * @param s Hex string to convert
     * @return Byte array containing the decoded data
     */
    private byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    @Override
    public byte[] textToSpeech(String voiceId, String text) {
        try {
            log.info(
                    "Converting text to speech with Minimax API, voice_id: {}, text: {}",
                    voiceId,
                    text.substring(0, Math.min(text.length(), 50)));

            // Set up headers with API key
            HttpHeaders headers = getHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(List.of(MediaType.APPLICATION_OCTET_STREAM));

            // Build request URL with groupId parameter - using v2 endpoint
            String url = config.getBaseUrl() + "v1/t2a_v2?GroupId=" + config.getGroupId();

            // Prepare request body with nested structure for voice and audio settings
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);
            requestBody.put("model", "speech-02-hd"); // Using HD model for better quality
            requestBody.put("stream", false);

            // Voice settings as nested object
            Map<String, Object> voiceSettings = new HashMap<>();
            voiceSettings.put("voice_id", voiceId);
            voiceSettings.put("speed", 1.0); // Normal speed
            voiceSettings.put("vol", 1.0); // Normal volume
            voiceSettings.put("pitch", 0); // Default pitch (using integer instead of float)
            requestBody.put("voice_setting", voiceSettings);

            // Audio settings as nested object
            Map<String, Object> audioSettings = new HashMap<>();
            audioSettings.put("sample_rate", 32000);
            audioSettings.put("bitrate", 128000);
            audioSettings.put("format", "mp3");
            audioSettings.put("channel", 1); // Mono
            requestBody.put("audio_setting", audioSettings);

            // Create HTTP entity
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // Send request to Minimax API
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

            // Check response status code
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ZeusServiceException(
                        ErrorCode.ThirdPartyError,
                        "Failed to convert text to speech with Minimax API: " + response.getStatusCode());
            }

            String jsonResponse = response.getBody();
            if (jsonResponse == null || jsonResponse.isEmpty()) {
                throw new ZeusServiceException(
                        ErrorCode.ThirdPartyError, "Empty response from Minimax text-to-speech API");
            }

            try {
                // Parse the JSON response with proper type information
                Map<String, Object> responseMap = objectMapper.readValue(
                        jsonResponse, new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                // Check the base response status
                Object baseRespObj = responseMap.get("base_resp");
                if (!(baseRespObj instanceof Map)) {
                    throw new ZeusServiceException(ErrorCode.ThirdPartyError, "Invalid base_resp format");
                }

                @SuppressWarnings("unchecked")
                Map<String, Object> baseResp = (Map<String, Object>) baseRespObj;
                Object statusCodeObj = baseResp.get("status_code");
                int statusCode = statusCodeObj instanceof Number ? ((Number) statusCodeObj).intValue() : -1;

                if (statusCode != 0) {
                    Object statusMsgObj = baseResp.get("status_msg");
                    String errorMsg = statusMsgObj instanceof String ? (String) statusMsgObj : "Unknown error";
                    throw new ZeusServiceException(ErrorCode.ThirdPartyError, "Minimax API error: " + errorMsg);
                }

                // Extract the data containing audio URL
                Object dataObj = responseMap.get("data");
                if (!(dataObj instanceof Map)) {
                    throw new ZeusServiceException(ErrorCode.ThirdPartyError, "Invalid data format");
                }

                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) dataObj;

                byte[] audioData;
                // First check if audio hex data is directly available
                if (data.containsKey("audio")
                        && data.get("audio") != null
                        && !String.valueOf(data.get("audio")).isEmpty()) {
                    try {
                        // Decode hex string to bytes
                        String audioHex = (String) data.get("audio");
                        log.info("Found direct audio data in hex format, length: {} chars", audioHex.length());
                        audioData = hexStringToByteArray(audioHex);
                    } catch (Exception e) {
                        log.error("Failed to decode audio hex data: {}", e.getMessage());
                        throw new ZeusServiceException(
                                ErrorCode.ThirdPartyError,
                                "Failed to decode audio data from Minimax response: " + e.getMessage());
                    }
                } else if (data.containsKey("subtitle_file")) {
                    // Otherwise download from URL if available
                    String audioUrl = (String) data.get("subtitle_file");
                    log.info("No direct audio data found. Downloading from URL: {}", audioUrl);

                    // Download the audio file
                    HttpHeaders downloadHeaders = new HttpHeaders();
                    HttpEntity<String> downloadEntity = new HttpEntity<>(downloadHeaders);
                    ResponseEntity<byte[]> audioResponse =
                            restTemplate.exchange(audioUrl, HttpMethod.GET, downloadEntity, byte[].class);

                    audioData = audioResponse.getBody();
                } else {
                    throw new ZeusServiceException(
                            ErrorCode.ThirdPartyError, "Neither audio data nor download URL found in Minimax response");
                }
                if (audioData != null && audioData.length > 0) {
                    // Extract and log audio details from response if available
                    Map<String, Object> extraInfo = (Map<String, Object>) responseMap.get("extra_info");
                    if (extraInfo != null) {
                        log.info(
                                "Successfully generated audio from Minimax, length: {} ms, size: {} bytes",
                                extraInfo.get("audio_length"),
                                extraInfo.get("audio_size"));
                    } else {
                        log.info("Successfully downloaded audio from Minimax, size: {} bytes", audioData.length);
                    }
                    return audioData;
                } else {
                    throw new ZeusServiceException(
                            ErrorCode.ThirdPartyError, "Empty audio data from Minimax download URL");
                }
            } catch (JsonProcessingException e) {
                log.error("Failed to parse Minimax API response: {}", e.getMessage());
                throw new ZeusServiceException(
                        ErrorCode.ThirdPartyError, "Failed to parse Minimax API response: " + e.getMessage());
            }
        } catch (HttpClientErrorException e) {
            log.error("Minimax API error: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Failed to convert text to speech: " + e.getMessage());
        } catch (Exception e) {
            log.error("Error in text-to-speech processing: {}", e.getMessage());
            throw new ZeusServiceException(
                    ErrorCode.ThirdPartyError, "Error processing text-to-speech request: " + e.getMessage());
        }
    }

    /**
     * Helper method to create headers with API key
     * @return HttpHeaders with Authorization header set
     */
    private HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + config.getApiKey());
        return headers;
    }

    @Override
    public boolean deleteVoice(String voiceId, String voiceType) {
        try {
            log.info("Deleting Minimax voice, voiceId: {}, voiceType: {}", voiceId, voiceType);

            // Set up headers with API key
            HttpHeaders headers = getHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // Create request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("voice_id", voiceId);
            requestBody.put("voice_type", voiceType);

            // Create HTTP entity
            HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(requestBody), headers);

            // Build URL with GroupId parameter
            String url = config.getBaseUrl() + "v1/delete_voice";
            if (config.getGroupId() != null && !config.getGroupId().isEmpty()) {
                url += "?GroupId=" + config.getGroupId();
            }

            // Send DELETE request to Minimax API
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

            // Check response status code
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error(
                        "Minimax voice deletion failed, status: {}, body: {}",
                        response.getStatusCode(),
                        response.getBody());
                return false;
            }

            // Parse response
            Map<String, Object> responseMap = objectMapper.readValue(
                    response.getBody(), new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

            // Check base_resp status code
            @SuppressWarnings("unchecked")
            Map<String, Object> baseResp = (Map<String, Object>) responseMap.get("base_resp");
            if (baseResp == null
                    || !(baseResp.containsKey("status_code")
                            && Integer.valueOf(0).equals(baseResp.get("status_code")))) {
                String errorMsg = baseResp != null && baseResp.containsKey("status_msg")
                        ? (String) baseResp.get("status_msg")
                        : "Unknown error";
                log.error("Minimax voice deletion failed: {}", errorMsg);
                return false;
            }

            log.info("Successfully deleted Minimax voice, voiceId: {}", voiceId);
            return true;

        } catch (HttpClientErrorException e) {
            log.error("Minimax API error when deleting voice {}: {}", voiceId, e.getMessage());
            // Return false to indicate deletion failed rather than throwing exception
            return false;
        } catch (Exception e) {
            log.error("Unexpected error when deleting voice {}: {}", voiceId, e.getMessage(), e);
            // Return false to indicate deletion failed rather than throwing exception
            return false;
        }
    }
}
