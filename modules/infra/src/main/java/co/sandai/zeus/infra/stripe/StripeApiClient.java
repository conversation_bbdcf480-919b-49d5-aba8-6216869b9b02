package co.sandai.zeus.infra.stripe;

import static co.sandai.zeus.common.log.digest.PrintLog.TypeEnum.CLIENT_DIGEST;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.Subscription;
import com.stripe.model.SubscriptionCollection;
import com.stripe.model.checkout.Session;
import com.stripe.param.CustomerCreateParams;
import com.stripe.param.SubscriptionListParams;
import com.stripe.param.SubscriptionUpdateParams;
import com.stripe.param.checkout.SessionRetrieveParams;
import org.springframework.stereotype.Component;

@Component
public class StripeApiClient {

    @PrintLog(fileType = CLIENT_DIGEST)
    public static Customer createOutCustomer(String name, String email) throws StripeException {
        CustomerCreateParams params =
                CustomerCreateParams.builder().setName(name).setEmail(email).build();
        return Customer.create(params);
    }

    @PrintLog(fileType = CLIENT_DIGEST)
    public Session retriveCheckoutSession(String sessionId) throws StripeException {
        // Retrieve the Checkout Session from the API with line_items expanded
        SessionRetrieveParams params = SessionRetrieveParams.builder()
                .addExpand("line_items")
                .addExpand("invoice")
                .addExpand("customer")
                .build();

        return Session.retrieve(sessionId, params, null);
    }

    @PrintLog(fileType = CLIENT_DIGEST)
    public Subscription cancelSubscriptionAtPeriodEnd(String outCustomerId, String priceId) throws StripeException {
        SubscriptionListParams listParams = SubscriptionListParams.builder()
                .setCustomer(outCustomerId)
                .setPrice(priceId)
                .setLimit(3L)
                .build();

        SubscriptionCollection subscriptions = Subscription.list(listParams);
        String subscriptionId;
        if (subscriptions != null
                && subscriptions.getData() != null
                && !subscriptions.getData().isEmpty()) {
            subscriptionId = subscriptions.getData().get(0).getId();
        } else {
            throw new ZeusServiceException(
                    ErrorCode.InvalidParameters,
                    "No active subscription found for current plan, maybe already cancelled.");
        }
        // Set your secret key. Remember to switch to your live secret key in production.
        // See your keys here: https://dashboard.stripe.com/apikeys
        Subscription resource = Subscription.retrieve(subscriptionId);

        SubscriptionUpdateParams params =
                SubscriptionUpdateParams.builder().setCancelAtPeriodEnd(true).build();

        return resource.update(params);
    }

    @PrintLog(fileType = CLIENT_DIGEST)
    public Subscription querySubscription(String outCustomerId, String priceId) throws StripeException {
        SubscriptionListParams listParams = SubscriptionListParams.builder()
                .setCustomer(outCustomerId)
                .setPrice(priceId)
                .setLimit(3L)
                .build();

        SubscriptionCollection subscriptions = Subscription.list(listParams);
        if (subscriptions != null
                && subscriptions.getData() != null
                && !subscriptions.getData().isEmpty()) {
            return subscriptions.getData().getFirst();
        }
        return null;
    }
}
