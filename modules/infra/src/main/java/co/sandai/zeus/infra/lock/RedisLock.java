package co.sandai.zeus.infra.lock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RedisLock {
    /**
     * 分布式锁的key，格式化字符串，支持占位符{0}, {1}等
     * 其中{0}表示切点方法的第一个参数，{1}表示第二个参数，以此类推
     * @return
     */
    String key();

    long timeout() default 5000; // default timeout in milliseconds

    /**
     * 为了获取锁的等待时间，单位毫秒
     * @return
     */
    long waitTime() default 500;

    /**
     * 锁的租约时间，单位毫秒
     * @return
     */
    long leaseTime() default 5000;
}
