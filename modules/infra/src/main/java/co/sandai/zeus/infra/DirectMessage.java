package co.sandai.zeus.infra;

import com.aliyun.dm20151123.Client;
import com.aliyun.dm20151123.models.SingleSendMailRequest;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DirectMessage {

    @Value("${aliyun.ak}")
    private String aliyunAk;

    @Value("${aliyun.sk}")
    private String aliyunSk;

    @Value("${aliyun.dm.account-name}")
    private String aliyunDMAccountName;

    @Value("${aliyun.dm.endpoint:dm.aliyuncs.com}")
    private String aliyunDMEndpoint;

    Client client;

    @PostConstruct
    public void init() {
        try {
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                    .setAccessKeyId(aliyunAk)
                    .setAccessKeySecret(aliyunSk);
            // Endpoint 请参考 https://api.aliyun.com/product/Dm
            config.endpoint = aliyunDMEndpoint;
            client = new Client(config);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void sendEmail(String to, String subject, String body) {
        SingleSendMailRequest singleSendMailRequest = new SingleSendMailRequest()
                .setAccountName(aliyunDMAccountName)
                .setAddressType(1)
                .setReplyToAddress(false)
                .setToAddress(to)
                .setHtmlBody(body)
                .setSubject(subject);
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            client.singleSendMailWithOptions(singleSendMailRequest, runtime);
        } catch (TeaException error) {
            log.error("send email get tea err: {}", error.getData().get("Recommend"));
            throw new RuntimeException(error);
        } catch (Exception error) {
            throw new RuntimeException(error);
        }
    }
}
