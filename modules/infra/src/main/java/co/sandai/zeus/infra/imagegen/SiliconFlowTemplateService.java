package co.sandai.zeus.infra.imagegen;

import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.infra.imagegen.dto.SiliconFlowImageGenRequest;
import co.sandai.zeus.infra.imagegen.dto.SiliconFlowImageGenResponse;
import co.sandai.zeus.infra.imagegen.dto.SiliconFlowImageItem;
import co.sandai.zeus.infra.imagegen.dto.SizeValueObject;
import co.sandai.zeus.infra.web.log.LogBody;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class SiliconFlowTemplateService {

    @Value("${siliconflow.sk}")
    private String siliconflowSk;

    @Autowired
    private RestTemplate restTemplate;

    @LogBody(uri = "v1/images/generations")
    List<String> generateImage(
            String prompt,
            SizeValueObject aspectRatio,
            Integer count,
            SiliconFlowImageGenRequest.ModelParam modelParam) {
        String url = "https://api.siliconflow.cn/v1/images/generations";

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", String.format("Bearer %s", siliconflowSk));
        headers.set("Content-Type", "application/json");

        List<String> imageUrls = new ArrayList<>();
        SiliconFlowImageGenRequest.ModelEnum model = modelParam.getModelEnum();
        while (count > 0) {
            int batchSize = model.getMaxBatchSize() > count ? count : model.getMaxBatchSize();
            SiliconFlowImageGenRequest request = new SiliconFlowImageGenRequest()
                    .setModel(model.getName())
                    .setBatchSize(batchSize)
                    .setPrompt(prompt)
                    .setPromptEnhancement(modelParam.getPromptEnhancement())
                    .setAspectRatio(aspectRatio, model);
            HttpEntity<SiliconFlowImageGenRequest> entity = new HttpEntity<>(request, headers);

            ResponseEntity<SiliconFlowImageGenResponse> res =
                    restTemplate.exchange(url, HttpMethod.POST, entity, SiliconFlowImageGenResponse.class);
            SiliconFlowImageGenResponse result = res.getBody();
            if (result != null && !result.images.isEmpty()) {
                for (SiliconFlowImageItem image : result.images) {
                    imageUrls.add(image.getUrl());
                }
            } else {
                LogUtil.errorf(log, "SiliconFlowImageGenResponse is null or images is empty");
            }
            count -= batchSize;
        }

        return imageUrls;
    }
}
