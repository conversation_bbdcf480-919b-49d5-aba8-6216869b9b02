package co.sandai.zeus.infra;

import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;
import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.SmartLifecycle;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Slf4j
@Getter
@Component
public class IDGenerator implements SmartLifecycle {

    @Value("${HOSTNAME:}")
    private String hostName;

    private String workerName;
    private Short idGenWorkerId;
    private ScheduledExecutorService renewExecutor;

    public static final String ID_GEN_WORKER_INDEX_KEY = "IDGen.WorkerId.Index";
    public static final String ID_GEN_WORKER_LOCK_KEY = "IDGen.WorkerId.Lock";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private void initWorkerName() {
        try {
            if (!StringUtils.hasText(hostName)) {
                hostName = InetAddress.getLocalHost().getHostName();
            }
            String pid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
            String rand = String.valueOf(ThreadLocalRandom.current().nextInt(1000));
            this.workerName = String.format("%s-%s-%s", hostName, pid, rand);
        } catch (Exception e) {
            this.workerName = "fallback-" + UUID.randomUUID().toString().substring(0, 8);
        }
    }

    private String getWorkerIdValueKey(short value) {
        return String.format("IDGen.WorkerId.Value:%d", value);
    }

    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 100))
    private short getWorkerId() {
        Boolean ok = redisTemplate.opsForValue().setIfAbsent(ID_GEN_WORKER_LOCK_KEY, workerName, Duration.ofSeconds(2));

        if (Boolean.FALSE.equals(ok)) {
            throw new RuntimeException("Cannot acquire worker registration lock");
        }

        try {
            Long workerId = redisTemplate.opsForValue().increment(ID_GEN_WORKER_INDEX_KEY);
            if (workerId == null) {
                throw new RuntimeException("Failed to generate worker ID");
            }

            workerId = workerId % 60;
            String workerKey = getWorkerIdValueKey(workerId.shortValue());

            Boolean success = redisTemplate.opsForValue().setIfAbsent(workerKey, workerName, Duration.ofSeconds(30));

            if (Boolean.FALSE.equals(success)) {
                throw new RuntimeException("Worker ID conflict: " + workerId);
            }

            return workerId.shortValue();
        } finally {
            redisTemplate.delete(ID_GEN_WORKER_LOCK_KEY);
        }
    }

    private void startRenewalTask(short workerId) {
        renewExecutor = Executors.newSingleThreadScheduledExecutor();
        String workerKey = getWorkerIdValueKey(workerId);

        renewExecutor.scheduleAtFixedRate(
                () -> {
                    try {
                        if (workerName.equals(redisTemplate.opsForValue().get(workerKey))) {
                            redisTemplate.expire(workerKey, Duration.ofSeconds(30));
                            log.debug("Renewed worker ID: {}", workerId);
                        }
                    } catch (Exception e) {
                        log.error("WorkerID renew failed", e);
                    }
                },
                20,
                20,
                TimeUnit.SECONDS);
    }

    public long getNextId() {
        return YitIdHelper.nextId();
    }

    @Override
    public void start() {
        initWorkerName();
        this.idGenWorkerId = this.getWorkerId();

        IdGeneratorOptions options = new IdGeneratorOptions(this.idGenWorkerId);
        YitIdHelper.setIdGenerator(options);
        startRenewalTask(this.idGenWorkerId);

        log.info("ID generator initialized with worker {} ID: {}", this.workerName, this.idGenWorkerId);
    }

    @Override
    public void stop() {
        try {
            if (renewExecutor != null) {
                renewExecutor.shutdownNow();
            }
            if (idGenWorkerId != null) {
                String workerKey = getWorkerIdValueKey(this.idGenWorkerId);
                if (workerName.equals(redisTemplate.opsForValue().get(workerKey))) {
                    redisTemplate.delete(workerKey);
                }
            }
        } catch (Exception e) {
            log.error("WorkerID release failed", e);
        }
        log.info("ID generator released worker {} ID: {}", this.workerName, this.idGenWorkerId);
    }

    @Override
    public boolean isRunning() {
        return this.idGenWorkerId != null;
    }
}
