package co.sandai.zeus.infra.infer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class InferJob {
    private String id;

    @JsonProperty("project_name")
    private String projectName;

    private Integer priority;

    private List<Output> outputs;

    @JsonProperty("retrieved_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")
    private ZonedDateTime retrievedAt;

    @JsonProperty("pipeline_id")
    private String pipelineId;

    @JsonProperty("streaming_key")
    private String streamingKey;

    private State state;

    @JsonProperty("created_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")
    private ZonedDateTime createdAt;

    @JsonProperty("pipeline_position")
    private Integer pipelinePosition;

    private List<Step> steps;

    private String status;

    @JsonProperty("updated_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")
    private ZonedDateTime updatedAt;

    private String type;

    @JsonProperty("pre_processors")
    private List<PreProcessor> preProcessors;

    private Integer progress;

    @JsonProperty("request_id")
    private String requestId;

    private List<Input> inputs;

    private ZonedDateTime deadline;

    private String group;

    private String model;

    private JobOptions options;

    @JsonProperty("started_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")
    private ZonedDateTime startedAt;

    private String prompt;

    @JsonProperty("model_version")
    private String modelVersion;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("enhanced_prompt")
    private String enhancedPrompt;

    private String queue;

    @JsonProperty("completed_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")
    private ZonedDateTime completedAt;

    // Inner classes for nested objects
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Output {
        private String name;
        private String urn;
        private Map<String, Object> meta;
        private String url;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Input {
        private String name;
        private String urn;
        private String url;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class State {
        @JsonProperty("estimate_start_time")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")
        private ZonedDateTime estimateStartTime;

        @JsonProperty("estimate_complete_time")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")
        private ZonedDateTime estimateCompleteTime;

        @JsonProperty("start_time")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")
        private ZonedDateTime startTime;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Step {
        private Integer duration;
        private String prompt;

        @JsonProperty("enhanced_prompt")
        private String enhancedPrompt;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class PreProcessor {
        private String id;
        private String type;
        private String status;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class JobOptions {
        @JsonProperty("tSchedulerFunc")
        private String tSchedulerFunc;

        @JsonProperty("tSchedulerFuncArgs")
        private String tSchedulerFuncArgs;

        private Integer seed;
        private String aspectRatio;
        private Boolean enableWatermark;
        private List<String> specialTokens;
        private String vaeModel;
        private Boolean dryRun;
        private String resolution;
        private Boolean promptEnhancement;
        private String promptEnhancementType;
        private Integer cropTimeStart;
        private Integer cropTimeEnd;
    }
    /**
     * Gets the estimate start time from the state map
     *
     * @return The parsed ZonedDateTime or null if not available
     */
    public ZonedDateTime getEstimateStartTime() {
        if (Objects.isNull(state)) {
            return null;
        }
        if (Objects.nonNull(state.startTime)) {
            return state.startTime;
        }
        if (Objects.nonNull(state.getEstimateStartTime())) {
            return state.estimateStartTime;
        }
        return null;
    }

    /**
     * Gets the estimate complete time from the state map
     *
     * @return The parsed ZonedDateTime or null if not available
     */
    public ZonedDateTime getEstimateCompleteTime() {
        if (Objects.isNull(state)) {
            return null;
        }
        return state.getEstimateCompleteTime();
    }
}
