package co.sandai.zeus.infra.voice.client;

import co.sandai.zeus.infra.voice.dto.MinimaxVoiceDTO;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * Client interface for Minimax API
 * This interface defines methods for interacting with the Minimax API
 * without exposing domain layer concepts
 */
public interface MinimaxClient {

    /**
     * Get available voices from Minimax
     * @return List of Minimax voice DTOs
     */
    List<MinimaxVoiceDTO> getVoices();

    /**
     * Create a new voice using Minimax API
     * @param name Voice name
     * @param description Voice description
     * @param files List of audio files
     * @return Created voice DTO
     */
    MinimaxVoiceDTO createVoice(String name, String description, List<MultipartFile> files);

    /**
     * Convert text to speech using Minimax API
     * @param voiceId ID of the voice to use
     * @param text Text to convert to speech
     * @return Audio data as bytes
     */
    byte[] textToSpeech(String voiceId, String text);

    /**
     * Delete a voice using Minimax API
     * @param voiceId ID of the voice to delete
     * @param voiceType Type of voice ("voice_cloning" or "voice_generation")
     * @return true if deletion was successful, false otherwise
     */
    boolean deleteVoice(String voiceId, String voiceType);
}
