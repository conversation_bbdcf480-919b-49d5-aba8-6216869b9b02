package co.sandai.zeus.infra.voice.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Configuration for ElevenLabs API
 */
@Getter
@Component
public class ElevenLabsConfig {
    @Value("${elevenlabs.api.key:}")
    private String apiKey;

    @Value("${elevenlabs.api.baseUrl:https://api.elevenlabs.io/}")
    private String baseUrl;

    @Value("${elevenlabs.collection.id:}")
    private String collectionId;
}
