package co.sandai.zeus.infra.web.log;

import jakarta.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.Set;
import org.reflections.Reflections;
import org.reflections.scanners.Scanners;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.reflections.util.FilterBuilder;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationUtils;

@Configuration
public class LoggingConfig {

    public static final String basePackage = "co.sandai.zeus"; // Replace with the base package to scan

    @PostConstruct
    public void init() {
        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage(basePackage))
                .filterInputsBy(new FilterBuilder().includePackage(basePackage))
                .setScanners(Scanners.MethodsAnnotated));

        Set<Method> methodsAnnotatedWith = reflections.getMethodsAnnotatedWith(LogBody.class);
        for (Method method : methodsAnnotatedWith) {
            LogBody methodAnnotation = AnnotationUtils.findAnnotation(method, LogBody.class);
            if (methodAnnotation != null) {
                WhitelistRegistry.addUri(methodAnnotation);
            }
        }
    }
}
