package co.sandai.zeus.infra.lock;

import co.sandai.zeus.config.SystemRedisConfig;
import java.io.IOException;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {

    @Autowired
    SystemRedisConfig systemRedisConfig;

    @Bean
    public RedissonClient redissonClient() throws IOException {
        Config config = new Config();
        String address = String.format("redis://%s:%d", systemRedisConfig.getHost(), systemRedisConfig.getPort());
        config.useSingleServer()
                .setAddress(address)
                .setPassword(systemRedisConfig.getPassword())
                .setConnectionPoolSize(systemRedisConfig.getPoolSize())
                .setDatabase(systemRedisConfig.getDatabase())
                .setConnectTimeout(systemRedisConfig.getConnTimeout())
                .setTimeout(systemRedisConfig.getReadTimeout())
                .setConnectionMinimumIdleSize(systemRedisConfig.getPoolMinIdle());

        return Redisson.create(config);
    }
}
