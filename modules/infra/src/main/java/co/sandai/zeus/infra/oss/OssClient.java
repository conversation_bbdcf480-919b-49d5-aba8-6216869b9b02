package co.sandai.zeus.infra.oss;

import co.sandai.zeus.config.SpringContext;
import jakarta.annotation.PostConstruct;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

@Component
@ConfigurationProperties(prefix = "oss")
public class OssClient {

    private S3Client s3Client;
    private String bucketName;
    private OSSProviderSource providerSource;
    private S3Presigner s3PreSigner;

    @Autowired
    private Environment environment;

    private Map<String, Map<String, String>> providers;

    @Value("${oss.default-provider}")
    OSSProviderSource defaultProvider;

    private Map<String, String> bucketUrlTemplates = new HashMap<>();
    // 会自动读取配置文件中的 oss.bucket-url-templates 的配置
    public void setBucketUrlTemplates(Map<String, String> templates) {
        this.bucketUrlTemplates = templates;
    }

    @PostConstruct
    public void init() {
        this.initClient(defaultProvider);
    }

    public String getProviderConfig(OSSProviderSource source, String key) {
        if (Objects.isNull(environment)) {
            environment = SpringContext.getBean(Environment.class);
        }
        return environment.getProperty(String.format("oss.providers.%s.%s", source, key));
    }

    public void initClient(OSSProviderSource source, boolean useInternalEndpoint) {
        String endpointKey = "endpoint";
        if (useInternalEndpoint) {
            endpointKey = "endpoint-internal";
        }

        String defaultBucket = getProviderConfig(source, "default-bucket");
        String ossAK = getProviderConfig(source, "ak");
        String ossSK = getProviderConfig(source, "sk");
        String ossEndpoint = getProviderConfig(source, endpointKey);

        AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(ossAK, ossSK);
        this.s3Client = S3Client.builder()
                .region(Region.AWS_GLOBAL)
                .endpointOverride(URI.create(ossEndpoint))
                .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                .serviceConfiguration(S3Configuration.builder()
                        .pathStyleAccessEnabled(false)
                        .chunkedEncodingEnabled(false)
                        .build())
                .build();
        this.s3PreSigner = S3Presigner.builder()
                .region(Region.AWS_GLOBAL)
                .endpointOverride(URI.create(ossEndpoint))
                .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                .serviceConfiguration(S3Configuration.builder()
                        .pathStyleAccessEnabled(false)
                        .chunkedEncodingEnabled(false)
                        .build())
                .build();

        this.providerSource = source;
        this.bucketName = defaultBucket;
    }

    public void initClient(OSSProviderSource source) {
        this.initClient(source, false);
    }

    public OSSFileMeta storeFileInS3(byte[] data, String keyName, String contentType) {
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(keyName)
                .contentType(contentType)
                .build();
        s3Client.putObject(putObjectRequest, RequestBody.fromBytes(data));

        OSSFileMeta fileMeta = new OSSFileMeta();
        fileMeta.setBucket(bucketName);
        fileMeta.setPath(keyName);
        fileMeta.setSource(providerSource);

        return fileMeta;
    }

    public OSSFileMeta storeFileInS3Temporarily(byte[] data, String keyName, String contentType) {
        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(keyName)
                .contentType(contentType)
                // 60分钟后过期
                .expires(Instant.now().plusSeconds(3600))
                .build();
        s3Client.putObject(putObjectRequest, RequestBody.fromBytes(data));

        OSSFileMeta fileMeta = new OSSFileMeta();
        fileMeta.setBucket(bucketName);
        fileMeta.setPath(keyName);
        fileMeta.setSource(providerSource);

        return fileMeta;
    }

    public String getPublicUrl(String filePath, int expiresInMinutes) {
        String urlTemplate = bucketUrlTemplates.get(bucketName);
        if (urlTemplate != null) {
            return String.format(urlTemplate, filePath);
        }
        return createPreSignedGetUrl(this.bucketName, filePath, expiresInMinutes);
    }

    public String createPreSignedGetUrl(String bucketName, String keyName, int expiresInMinutes) {
        GetObjectRequest objectRequest =
                GetObjectRequest.builder().bucket(bucketName).key(keyName).build();

        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(expiresInMinutes))
                .getObjectRequest(objectRequest)
                .build();

        PresignedGetObjectRequest presignedRequest = s3PreSigner.presignGetObject(presignRequest);

        return presignedRequest.url().toExternalForm();
    }

    /**
     * 使用默认bucketName
     */
    public String createPreSignedGetUrl(String keyName, int expiresInMinutes) {
        return createPreSignedGetUrl(bucketName, keyName, expiresInMinutes);
    }

    public InputStream getInputStream(String keyName) {
        return s3Client.getObject(
                GetObjectRequest.builder().bucket(bucketName).key(keyName).build());
    }

    public OssClient newInstanceFromSource(OSSProviderSource provider, String bucketName, boolean useInternalEndpoint) {
        OssClient client = new OssClient();
        client.environment = this.environment;
        client.bucketUrlTemplates = new HashMap<>(this.bucketUrlTemplates);
        client.initClient(provider, useInternalEndpoint);
        client.bucketName = bucketName;
        return client;
    }

    public OssClient newInstanceFromSource(OSSProviderSource provider, String bucketName) {
        return newInstanceFromSource(provider, bucketName, false);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class OSSEndpointMeta {
        public OSSProviderSource source;
        public String bucket;
    }

    @Getter
    @Setter
    public static class OSSFileMeta extends OSSEndpointMeta {
        String path;
    }

    public static OSSEndpointMeta parseOSSEndpoint(String endpoint) {
        String[] parts = endpoint.split("\\.");
        String bucket = parts[0];
        OSSProviderSource provider = null;
        if (endpoint.endsWith(".aliyuncs.com")) {
            provider = OSSProviderSource.AliYun;
        }
        OSSEndpointMeta res = new OSSEndpointMeta();
        res.setBucket(bucket);
        res.setSource(provider);
        return res;
    }

    public static OSSFileMeta parseOSSUrl(String ossUrl) {
        try {
            URL url = URI.create(ossUrl).toURL();
            String host = url.getHost();
            String path = url.getPath().substring(1);
            OSSEndpointMeta endpointMeta = parseOSSEndpoint(host);
            OSSFileMeta meta = new OSSFileMeta();
            meta.setBucket(endpointMeta.getBucket());
            meta.setSource(endpointMeta.getSource());
            meta.setPath(path);
            return meta;
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
    }
}
