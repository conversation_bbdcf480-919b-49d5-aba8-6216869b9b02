package co.sandai.zeus.infra.imagegen.dto;

import co.sandai.zeus.common.log.LogUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Data
@Slf4j
@Accessors(chain = true)
public class SizeValueObject {

    private String aspectRatio;

    private Integer width;

    private Integer height;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public void calculate() {
        int w = 1024, h = 1024;
        if (this.getWidth() != null) {
            w = this.getWidth();
            if (this.getHeight() != null) {
                h = this.getHeight();
                setWH(w, h);
                return;
            }
            if (StringUtils.isNotBlank(this.getAspectRatio())) {
                String[] aspectRatio = this.getAspectRatio().split(":");
                if (aspectRatio.length != 2) {
                    LogUtil.warnf(
                            log, "invalid input aspectRatio: {0}, fail to set this of image", this.getAspectRatio());
                    setWH(w, h);
                    return;
                }
                int ww = Integer.parseInt(aspectRatio[0]);
                int hh = Integer.parseInt(aspectRatio[1]);
                if (ww > 0 && hh > 0) {
                    h = w * hh / ww;
                    setWH(w, h);
                    return;
                }
            }
            LogUtil.warnf(log, "invalid input this: {0}, fail to set this of image", this);
            setWH(w, h);
            return;
        }
        // 剩下的情况, width为null
        if (this.getHeight() != null) {
            h = this.getHeight();
            if (StringUtils.isNotBlank(this.getAspectRatio())) {
                String[] aspectRatio = this.getAspectRatio().split(":");
                if (aspectRatio.length != 2) {
                    LogUtil.warnf(
                            log, "invalid input aspectRatio: {0}, fail to set this of image", this.getAspectRatio());
                    setWH(w, h);
                    return;
                }
                int ww = Integer.parseInt(aspectRatio[0]);
                int hh = Integer.parseInt(aspectRatio[1]);
                if (ww > 0 && hh > 0) {
                    w = h * ww / hh;
                    setWH(w, h);
                    return;
                }
            }
            LogUtil.warnf(log, "invalid input this: {0}, fail to set this of image", this);
            setWH(w, h);
            return;
        }
        // 剩下的情况, height和width都为null
        // aspectRatio不为null
        if (this.getAspectRatio() == null) {
            // 默认的值
            w = 512;
            h = 512;
        } else if (this.getAspectRatio().equals("16:9")) {
            w = 1024;
            h = 576;
        } else if (this.getAspectRatio().equals("9:16")) {
            w = 576;
            h = 1024;
        } else if (this.getAspectRatio().equals("1:1")) {
            w = 1024;
            h = 1024;
        } else {
            // 默认的值
            w = 512;
            h = 512;
        }

        setWH(w, h);
    }

    public void setWH(int w, int h) {
        this.setWidth(w);
        this.setHeight(h);
    }
}
