package co.sandai.zeus.infra.imagegen;

import co.sandai.zeus.infra.imagegen.dto.SiliconFlowImageGenRequest;
import co.sandai.zeus.infra.imagegen.dto.SizeValueObject;
import co.sandai.zeus.infra.imagegen.enums.ApiProviderEnum;
import co.sandai.zeus.infra.imagegen.enums.BizSceneEnum;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

@Service
@Slf4j
public class ImageGenClient {

    @Resource
    private SiliconFlowTemplateService siliconFlowTemplateService;

    @Retryable(retryFor = HttpClientErrorException.TooManyRequests.class, backoff = @Backoff(delay = 1000 * 30))
    public List<String> generateImage(String prompt, SizeValueObject size, Integer count, BizSceneEnum bizSceneEnum) {
        SiliconFlowImageGenRequest.ModelParam model;
        ApiProviderEnum apiProvider;
        switch (bizSceneEnum) {
            case VIDEO_GEN_FIRST_FRAME:
                apiProvider = ApiProviderEnum.SILICON_FLOW;
                model = new SiliconFlowImageGenRequest.ModelParam()
                        .setModelEnum(SiliconFlowImageGenRequest.ModelEnum.FLUX_SCHNELL_FREE)
                        .setPromptEnhancement(false);
                break;
            case INSPIRATION_IMAGE_GEN:
                apiProvider = ApiProviderEnum.SILICON_FLOW;
                model = new SiliconFlowImageGenRequest.ModelParam()
                        .setModelEnum(SiliconFlowImageGenRequest.ModelEnum.FLUX_DEV_CHARGE)
                        .setPromptEnhancement(false);
                break;
            default:
                apiProvider = ApiProviderEnum.SILICON_FLOW;
                model = new SiliconFlowImageGenRequest.ModelParam()
                        .setModelEnum(SiliconFlowImageGenRequest.ModelEnum.FLUX_SCHNELL_FREE)
                        .setPromptEnhancement(false);
                break;
        }
        return siliconFlowTemplateService.generateImage(prompt, size, count, model);
    }
}
