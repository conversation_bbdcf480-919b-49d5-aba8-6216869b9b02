package co.sandai.zeus.infra.web.log;

import co.sandai.zeus.common.log.LogUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

public class LoggingInterceptor implements ClientHttpRequestInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(LoggingInterceptor.class);

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
            throws IOException {
        long startTime = System.currentTimeMillis();
        logRequest(
                request,
                body,
                WhitelistRegistry.shouldLogRequest(request.getURI().getPath()));
        ClientHttpResponse response = new CustomBufferingClientHttpResponseWrapper(execution.execute(request, body));
        logResponse(
                response, WhitelistRegistry.shouldLogResponse(request.getURI().getPath()), startTime);

        return response;
    }

    private void logRequest(HttpRequest request, byte[] body, boolean logBody) throws IOException {
        LogUtil.infof(
                logger,
                "[HTTP REQUEST] URI={0}, Method={1}, Request Headers={2}",
                request.getURI(),
                request.getMethod(),
                request.getHeaders());
        if (logBody) {
            logger.info("Request Body: {}", new String(body, StandardCharsets.UTF_8));
        }
    }

    private void logResponse(ClientHttpResponse response, boolean logBody, long startTime) throws IOException {
        LogUtil.infof(
                logger,
                "[HTTP RESPONSE] Status code: {0}, Status text: {1}, Time taken: {3} ms, Response Headers: {2}",
                response.getStatusCode(),
                response.getStatusText(),
                response.getHeaders(),
                System.currentTimeMillis() - startTime);
        if (logBody) {
            StringBuilder inputStringBuilder = new StringBuilder();
            try (BufferedReader bufferedReader =
                    new BufferedReader(new InputStreamReader(response.getBody(), StandardCharsets.UTF_8))) {
                String line = bufferedReader.readLine();
                while (line != null) {
                    inputStringBuilder.append(line);
                    inputStringBuilder.append('\n');
                    line = bufferedReader.readLine();
                }
            }
            logger.info("Response Body: {}", inputStringBuilder.toString());
        }
    }
}
