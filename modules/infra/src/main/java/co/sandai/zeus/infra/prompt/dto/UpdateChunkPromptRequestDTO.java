package co.sandai.zeus.infra.prompt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class UpdateChunkPromptRequestDTO {

    @JsonProperty("chunks")
    private List<Chunk> chunks;

    @JsonProperty("image_url")
    private String imageUrl;

    @Data
    public static class Chunk {
        @JsonProperty("prompt")
        private String prompt;

        @JsonProperty("duration")
        private float duration;
    }
}
