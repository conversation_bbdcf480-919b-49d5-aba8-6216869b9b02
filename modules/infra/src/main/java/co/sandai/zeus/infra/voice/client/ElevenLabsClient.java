package co.sandai.zeus.infra.voice.client;

import co.sandai.zeus.infra.voice.dto.ElevenLabsVoiceDTO;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * Client interface for ElevenLabs API
 * This interface defines methods for interacting with the ElevenLabs API
 * without exposing domain layer concepts
 */
public interface ElevenLabsClient {

    /**
     * Get available voices from ElevenLabs
     * @return List of ElevenLabs voice DTOs
     */
    List<ElevenLabsVoiceDTO> getVoices();

    /**
     * Create a new voice using ElevenLabs API
     * @param name Voice name
     * @param description Voice description
     * @param files List of audio files
     * @return Created voice DTO
     */
    ElevenLabsVoiceDTO createVoice(String name, String description, List<MultipartFile> files);

    /**
     * Convert text to speech using ElevenLabs API
     * @param voiceId ID of the voice to use
     * @param text Text to convert to speech
     * @return Audio data as bytes
     */
    byte[] textToSpeech(String voiceId, String text);

    /**
     * Detect language of text using ElevenLabs API
     * @param text Text to detect language for
     * @return Detected language code (e.g., "en", "zh")
     */
    String detectLanguage(String text);

    /**
     * Delete a voice using ElevenLabs API
     * @param voiceId ID of the voice to delete
     * @return true if deletion was successful, false otherwise
     */
    boolean deleteVoice(String voiceId);

    /**
     * Convert speech to text using ElevenLabs API
     * @param audioData Audio data as bytes to convert to text
     * @param language Optional language code (e.g., "en", "zh"). If null, auto-detect language
     * @return Transcribed text from the audio
     */
    String speechToText(byte[] audioData, String language);
}
