package co.sandai.zeus.infra.prompt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PromptEnhancementRequest {
    public enum PEPrefixType {
        Image,
        Video
    }

    @JsonProperty("text_prompt")
    public String textPromptByUser;

    // @JsonProperty("gen_first_frame")
    // public Boolean genFirstFrame;

    @JsonProperty("prefix_url")
    public String prefixUrl;

    @JsonProperty("prefix_type")
    public String prefixType;

    // public String version; // Not used

    @JsonProperty("enhancement_type")
    public String enhancementType;
}
