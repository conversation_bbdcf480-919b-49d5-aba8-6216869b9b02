package co.sandai.zeus.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class SystemConfig {
    public static final String PRODUCT_NAME_MAGI = "magi";
    public static final String PRODUCT_NAME_GAGA = "magi";

    @Value("${zeus.product-name:magi}")
    private String productName;

    @Value("${zeus.env}")
    private String env;

    // Auth
    @Value("${zeus.auth.disable-email-login:false}")
    private boolean disableEmailLogin;

    @Value("${zeus.auth.cookie.expire-in:604800}")
    private int expireIn;

    @Value("${zeus.auth.cookie.name:session}")
    private String cookieName;

    @Value("${zeus.auth.cookie.http-only:true}")
    private boolean httpOnly;

    // Generation
    @Value("${zeus.generation.new-user-first-task-queue:}")
    private String newUserFirstTaskQueue;

    public boolean isMagi() {
        return PRODUCT_NAME_MAGI.equals(productName);
    }

    public boolean isGaGa() {
        return PRODUCT_NAME_GAGA.equals(productName);
    }

    public boolean isDev() {
        return "dev".equals(env);
    }

    public boolean isProd() {
        return "prod".equals(env);
    }

    public boolean isStaging() {
        return "staging".equals(env);
    }
}
