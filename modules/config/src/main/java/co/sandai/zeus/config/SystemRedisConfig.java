package co.sandai.zeus.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class SystemRedisConfig {

    @Value("${zeus.redis.host}")
    private String host;

    @Value("${zeus.redis.port}")
    private Integer port;

    @Value("${zeus.redis.password}")
    private String password;

    @Value("${zeus.redis.connect-timeout:20000}")
    private int connTimeout;

    @Value("${zeus.redis.timeout:20000}")
    private int readTimeout;

    @Value("${zeus.redis.pool.size:100}")
    private int poolSize;

    @Value("${zeus.redis.pool.min-idle:5}")
    private int poolMinIdle;

    @Value("${zeus.redis.pool.max-idle:10}")
    private int poolMaxIdle;

    @Value("${zeus.redis.database}")
    private Integer database;
}
