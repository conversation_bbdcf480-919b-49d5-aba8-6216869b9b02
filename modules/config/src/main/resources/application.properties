spring.application.name=zeus

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

spring.jackson.date-format=yyyy-MM-dd'T'HH:mm:ss.SSSXXX
spring.jackson.time-zone=UTC

server.shutdown=graceful

zeus.auth.cookie.name=session
zeus.auth.cookie.expire-in=604800
zeus.auth.cookie.http-only=true
zeus.auth.redis.session-prefix=session:

mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.arg-name-based-constructor-auto-mapping=true
mybatis.configuration.default-enum-type-handler=co.sandai.zeus.type_handler.EnumTypeHandler
mybatis.mapper-locations=classpath:mapper/**/*.xml

spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.maximum-pool-size=800
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

logging.config=classpath:log4j2.xml

inspiration.prompt.mode=coze
coze.base-url=https://api.coze.com
coze.run.workflow.uri=/v1/workflow/run
coze.inspiration.app.id=7454099086678179846
coze.gen.story.workflow.id=7457856187741634566
coze.split.chunk.workflow.id=7455975352721080325
coze.regen.chunk.workflow.id=7457856273725423622
coze.ar.caption.app.id=7457147348068040711
coze.ar.caption.pe.workflow.map={"default":7459664005645828104,"ar_01":7459664005645828104,"ar_02":7459979475942490117}

leonardo.base-url=https://cloud.leonardo.ai/api/rest/v1
leonardo.gen.image.uri=/generations
leonardo.get.image.uri=/generations/<YOUR_GENERATION_ID>
leonardo.wait.interval=2000
leonardo.max.retries=10
leonardo.flux.speed.model.id=1dd50843-d653-4516-a8e3-f0238ee453ff
leonardo.flux.precision.model.id=b2614463-296c-462a-9586-aafdb8f00e36
