<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" packages="co.sandai.zeus.common.log">
    <!--变量配置 -->
    <Properties>
        <!-- 格式化输出：%date表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%n是换行符 -->
        <property name="LOG_PATTERN" value="%clr{%d{DEFAULT}}{faint}`|`%clr{%traceId}{green}`|`%clr{%t}{yellow}`|`%clr{%logger{-10}}{cyan}`|`%clr{%-5level}{magenta}`|`%clr{%msg}{faint} %clr{%throwable}{red}%n"/>

    </Properties>
    <Appenders>
        <RollingRandomAccessFile name="AppDefault" fileName="logs/app-default.log" filePattern="logs/app-default%d{yyyyMMdd}.log">
            <PatternLayout alwaysWriteExceptions="true"  pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="DigestFile" fileName="logs/zeus-digest.log" filePattern="logs/zeus-digest%d{yyyyMMdd}.log" >
            <PatternLayout alwaysWriteExceptions="true"  pattern="%clr{%d{DEFAULT}}{faint}`|`%clr{%traceId}{green}`|`%clr{%msg}{yellow}%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="ClientDigestFile" fileName="logs/zeus-client-digest.log" filePattern="logs/zeus-client-digest%d{yyyyMMdd}.log" >
            <PatternLayout alwaysWriteExceptions="true"  pattern="%clr{%d{DEFAULT}}{faint}`|`%clr{%traceId}{green}`|`%clr{%msg}{yellow}%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="DefaultFile" fileName="logs/zeus-default.log" filePattern="logs/zeus-default%d{yyyyMMdd}.log">
            <PatternLayout alwaysWriteExceptions="true"  pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="ErrorFile" fileName="logs/error.log" filePattern="logs/error.log">
            <PatternLayout alwaysWriteExceptions="true"  pattern="%clr{%d{DEFAULT}}{faint}`|`%clr{%traceId}{green}`|`%clr{%t}{yellow}`|`%clr{%logger{-10}}{cyan}`|`%clr{%msg}{faint} %clr{%throwable}{red}%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>
        <!-- Schedulerx日志 -->
        <SchedulerxLog4j2Appender name="schedulerxLog"
                                  timeFormat="yyyy-MM-dd'T'HH:mmZ"
                                  timeZone="UTC"
                                  ignoreExceptions="true">
            <PatternLayout pattern="%d %-5level [%thread] %logger{0}: %msg"/>
        </SchedulerxLog4j2Appender>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>
    </Appenders>
    <Loggers>
        <Root level="info">
            <AppenderRef ref="AppDefault"/>
            <AppenderRef ref="Console"/>
        </Root>
        <Logger name="co.sandai.zeus" level="info" additivity="false">
            <AppenderRef ref="ErrorFile"/>
            <AppenderRef ref="DefaultFile"/>
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="DigestLogger" level="info" additivity="false">
            <AppenderRef ref="DigestFile"/>
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="ClientDigestLogger" level="info" additivity="false">
            <AppenderRef ref="ClientDigestFile"/>
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="ErrorLogger" level="error" additivity="false">
            <AppenderRef ref="ErrorFile"/>
            <AppenderRef ref="DefaultFile"/>
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="schedulerx" level="info" additivity="false">
            <AppenderRef ref="schedulerxLog" />
            <AppenderRef ref="DefaultFile"/>
        </Logger>
    </Loggers>
</Configuration>