package co.sandai.zeus;

import co.sandai.zeus.domain.task.service.TaskService;
import java.io.FileOutputStream;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class TaskServiceTest {

    @Autowired
    private TaskService taskService;

    public void testMergeTaskChunks() {
        long taskId = 617434647429701L;
        byte[] data = taskService.mergeResultChunks(taskId);
        String name = "staging-output-h264-crf17-preset-slow.mp4";
        try (FileOutputStream fos = new FileOutputStream(name)) {
            fos.write(data);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
