package co.sandai.zeus.api.user.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Test class for PublicPathConfiguration to verify URL pattern matching functionality.
 */
class PublicPathConfigurationTest {

    private PublicPathConfiguration publicPathConfiguration;

    @BeforeEach
    void setUp() {
        publicPathConfiguration = new PublicPathConfiguration();
    }

    @Test
    void testExactMatches() {
        assertTrue(publicPathConfiguration.isPublicPath("/"));
        assertTrue(publicPathConfiguration.isPublicPath("/api/v1/user/signup"));
        assertTrue(publicPathConfiguration.isPublicPath("/api/v1/user/login"));
        assertTrue(publicPathConfiguration.isPublicPath("/actuator/prometheus"));
    }

    @Test
    void testPathVariableMatches() {
        assertTrue(publicPathConfiguration.isPublicPath("/api/v1/tasks/123/chunks.m3u8"));
        assertTrue(publicPathConfiguration.isPublicPath("/api/v1/tasks/abc123"));
        assertTrue(publicPathConfiguration.isPublicPath("/api/v1/share/xyz789"));

        // Test with different types of IDs
        assertTrue(publicPathConfiguration.isPublicPath("/api/v1/tasks/12345"));
        assertTrue(publicPathConfiguration.isPublicPath("/api/v1/share/share-id-123"));
    }

    @Test
    void testWildcardMatches() {
        // Test /** patterns
        assertTrue(publicPathConfiguration.isPublicPath("/auth/oauth2/google"));
        assertTrue(publicPathConfiguration.isPublicPath("/auth/login"));
        assertTrue(publicPathConfiguration.isPublicPath("/auth/logout"));
        assertTrue(publicPathConfiguration.isPublicPath("/auth/deep/nested/path"));

        assertTrue(publicPathConfiguration.isPublicPath("/v3/api-docs"));
        assertTrue(publicPathConfiguration.isPublicPath("/v3/api-docs/swagger-config"));

        assertTrue(publicPathConfiguration.isPublicPath("/swagger-ui/index.html"));
        assertTrue(publicPathConfiguration.isPublicPath("/swagger-ui/swagger-ui-bundle.js"));
    }

    @Test
    void testNonPublicPaths() {
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/user/profile"));
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/tasks"));
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/admin/users"));
        assertFalse(publicPathConfiguration.isPublicPath("/private/data"));
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/user/settings"));
    }

    @Test
    void testHttpMethodSpecificPaths() {
        // GET /api/v1/assets should be public
        assertTrue(publicPathConfiguration.isPublicPath("/api/v1/assets", "GET"));

        // POST /api/v1/assets should NOT be public (requires authentication)
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/assets", "POST"));

        // DELETE /api/v1/assets/123 should NOT be public (requires authentication)
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/assets/123", "DELETE"));

        // PUT /api/v1/assets/123 should NOT be public (requires authentication)
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/assets/123", "PUT"));
    }

    @Test
    void testHttpMethodSpecificPathsWithNullOrEmpty() {
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/assets", null));
        assertFalse(publicPathConfiguration.isPublicPath(null, "GET"));
        assertFalse(publicPathConfiguration.isPublicPath("", "GET"));
    }

    @Test
    void testNullAndEmptyPaths() {
        assertFalse(publicPathConfiguration.isPublicPath(null));
        assertFalse(publicPathConfiguration.isPublicPath(""));
    }

    @Test
    void testCaseSensitivity() {
        // AntPathMatcher is case-sensitive by default
        assertFalse(publicPathConfiguration.isPublicPath("/API/V1/USER/SIGNUP"));
        assertFalse(publicPathConfiguration.isPublicPath("/Auth/login"));
    }

    @Test
    void testEdgeCases() {
        // Test paths that might be similar but shouldn't match
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/tasks/123/chunks.mp4")); // Wrong extension
        assertFalse(publicPathConfiguration.isPublicPath("/api/v1/tasks/123/other")); // Wrong path
        assertFalse(publicPathConfiguration.isPublicPath("/authx/login")); // Similar but not matching
        assertFalse(publicPathConfiguration.isPublicPath("/v3/api-docsx")); // Similar but not matching
    }
}
