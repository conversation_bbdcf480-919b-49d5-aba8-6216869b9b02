{"voices": [{"voice_id": "HuK8QKF35exsCh2e7fLT", "name": "<PERSON><PERSON> - Italian Pro Talent, e-learning, news, webinar, istitutional.", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_multilingual_sts_v2": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "it", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_multilingual_sts_v2": "", "eleven_flash_v2_5": "", "eleven_v2_5_flash": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "standard", "descriptive": "classy", "age": "middle_aged", "gender": "male", "language": "it", "use_case": "informative_educational"}, "description": "Middle Age Pro Talent. Perfect for e-learning, webinar, news, workshop, istitutional.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/Ma07zSyFYi8Lp3VrvmVs.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751370477, "whitelisted_emails": [], "public_owner_id": "d0bc74494e98081c4e15ba13dc421de4c9112aa6b7715616e49e2abad7b7a8b6", "original_voice_id": "HuK8QKF35exsCh2e7fLT", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 730, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "high_quality", "reader_app_enabled": true, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "<PERSON><PERSON> - Italian Pro Talent, e-learning, news, webinar, istitutional.", "description": "Middle Age Pro Talent. Perfect for e-learning, webinar, news, workshop, istitutional.", "labels": {"language": "it", "descriptive": "classy", "age": "middle_aged", "gender": "male", "accent": "standard", "use_case": "informative_educational", "locale": "it-IT"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_turbo_v2_5", "eleven_multilingual_sts_v2", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5"], "verified_languages": [{"language": "bg", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "bg-BG", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/68821d8b-641b-428b-a740-cb44920418fa.mp3"}, {"language": "cs", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "cs-CZ", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/c760cc06-9691-4602-bc1d-a513432756c1.mp3"}, {"language": "de", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "de-DE", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/f02013f9-3920-47ed-9427-cd79ac3be3e3.mp3"}, {"language": "hi", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "hi-IN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/fea3e4bf-512f-4175-ab22-d11dee696b0d.mp3"}, {"language": "it", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "it-IT", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/aac46631-c3a5-4a94-84f5-f48a56e11a8c.mp3"}, {"language": "el", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "el-GR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/a6b8b49e-35eb-4970-80e6-14af6295c954.mp3"}, {"language": "pl", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "pl-PL", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/c035a79b-79a1-47fd-b2df-61db13aad0db.mp3"}, {"language": "es", "model_id": "eleven_multilingual_v2", "accent": "peninsular", "locale": "es-ES", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/e9da45ad-7077-40cd-bd3e-a88f2ff9eb62.mp3"}, {"language": "pt", "model_id": "eleven_multilingual_v2", "accent": "brazilian", "locale": "pt-BR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/a3fa3c89-5faa-4b24-bbec-973a34a45e7b.mp3"}, {"language": "fr", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "fr-FR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/004aa6a3-3319-434c-b26f-5bd9e374a3bf.mp3"}, {"language": "hr", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "hr-HR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/ab9d168b-e2ab-4044-9983-1bcfcb999a51.mp3"}, {"language": "id", "model_id": "eleven_multilingual_v2", "accent": "sundanese", "locale": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/PmcWCeDWyhXdgWnKoeG8s6OvV0L2/voices/HuK8QKF35exsCh2e7fLT/47569352-71ec-4bcc-b673-56158a714ef8.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751370477}, {"voice_id": "AB9XsbSA4eLG12t2myjN", "name": "<PERSON><PERSON><PERSON>", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_multilingual_sts_v2": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "ru", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_multilingual_sts_v2": "", "eleven_flash_v2_5": "", "eleven_v2_5_flash": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "standard", "descriptive": "upbeat", "age": "young", "gender": "female", "language": "ru", "use_case": "social_media"}, "description": "Young Russian female with a gentle voice, perfect for social media use case.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/4hputbU2TnFfBoUjn7CL.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751370147, "whitelisted_emails": [], "public_owner_id": "d0fd99854e7517a8890c2f536b4fb89a9408d2dfa8cd7c7be15e4692e72a2a57", "original_voice_id": "AB9XsbSA4eLG12t2myjN", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 730, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "high_quality", "reader_app_enabled": true, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "<PERSON><PERSON><PERSON>", "description": "Young Russian female with a gentle voice, perfect for social media use case.", "labels": {"language": "ru", "descriptive": "upbeat", "age": "young", "gender": "female", "accent": "standard", "use_case": "social_media", "locale": "ru-RU"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_turbo_v2_5", "eleven_multilingual_sts_v2", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5"], "verified_languages": [{"language": "bg", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "bg-BG", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/615cb84e-bf01-48e7-a1f1-9c0f3f38217e.mp3"}, {"language": "cs", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "cs-CZ", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/6a78680e-aaf9-4089-a377-266af50c3393.mp3"}, {"language": "it", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "it-IT", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/57844029-9ed6-4389-b9bf-b32b2d997323.mp3"}, {"language": "pt", "model_id": "eleven_multilingual_v2", "accent": "brazilian", "locale": "pt-BR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/7073e067-328a-412f-baa9-dc1591df64ae.mp3"}, {"language": "hi", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "hi-IN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/19473451-edfa-455b-9e83-e413d3e43e6d.mp3"}, {"language": "es", "model_id": "eleven_multilingual_v2", "accent": "latin", "locale": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/f9590bb9-1cc1-4789-b2ac-64e1a1cb48b8.mp3"}, {"language": "fr", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "fr-FR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/430f69b3-e302-445f-bc6d-0d5f7f6cde15.mp3"}, {"language": "da", "model_id": "eleven_multilingual_v2", "accent": "zealandic", "locale": "da-DK", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/34647257-3e6b-4233-8c2a-e38777ff9cd8.mp3"}, {"language": "id", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "id-ID", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/249edd36-72b5-469b-b942-22025816e714.mp3"}, {"language": "uk", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "uk-UA", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ddlHYphssKY5fkGbtwue9a7Obfp1/voices/AB9XsbSA4eLG12t2myjN/dbad4a9d-361a-4841-b16a-ca75c9976961.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751370147}, {"voice_id": "3DPhHWXDY263XJ1d2EPN", "name": "<PERSON>", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_multilingual_sts_v2": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "it", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_multilingual_sts_v2": "", "eleven_flash_v2_5": "", "eleven_v2_5_flash": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "standard", "descriptive": "gentle", "age": "young", "gender": "female", "language": "it", "use_case": "narrative_story"}, "description": "A young, dynamic and cheerful woman's voice. Perfect for audiobooks, advertising, podcasts.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/custom/voices/3DPhHWXDY263XJ1d2EPN/YcN8v5oIOX09iQfWoPXH.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751369580, "whitelisted_emails": [], "public_owner_id": "e9a6840c69b79812b77ea81fa11d55aaf80dcf1938fa0137bf7f514f67f75c99", "original_voice_id": "3DPhHWXDY263XJ1d2EPN", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 730, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "high_quality", "reader_app_enabled": true, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "<PERSON>", "description": "A young, dynamic and cheerful woman's voice. Perfect for audiobooks, advertising, podcasts.", "labels": {"language": "it", "descriptive": "gentle", "age": "young", "gender": "female", "accent": "standard", "use_case": "narrative_story", "locale": "it-IT"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_turbo_v2_5", "eleven_multilingual_sts_v2", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5"], "verified_languages": [{"language": "ar", "model_id": "eleven_multilingual_v2", "accent": "modern", "locale": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/8RYUsFHsalUXjwVG0LjhNwH1j022/voices/3DPhHWXDY263XJ1d2EPN/c58a43a1-8224-43f9-984a-e51cac1a615b.mp3"}, {"language": "es", "model_id": "eleven_multilingual_v2", "accent": "peninsular", "locale": "es-ES", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/8RYUsFHsalUXjwVG0LjhNwH1j022/voices/3DPhHWXDY263XJ1d2EPN/e78827b1-8ef2-45f5-83b8-132d0f06cce2.mp3"}, {"language": "it", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "it-IT", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/8RYUsFHsalUXjwVG0LjhNwH1j022/voices/3DPhHWXDY263XJ1d2EPN/ecf70966-0f78-475a-ad08-e99b9bf44d6f.mp3"}, {"language": "el", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "el-GR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/8RYUsFHsalUXjwVG0LjhNwH1j022/voices/3DPhHWXDY263XJ1d2EPN/316473a2-e896-440d-b980-d7f7fa0a1271.mp3"}, {"language": "hi", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "hi-IN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/8RYUsFHsalUXjwVG0LjhNwH1j022/voices/3DPhHWXDY263XJ1d2EPN/18b8c84c-003d-45c8-acaf-374ae063e2b6.mp3"}, {"language": "pt", "model_id": "eleven_multilingual_v2", "accent": "brazilian", "locale": "pt-BR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/8RYUsFHsalUXjwVG0LjhNwH1j022/voices/3DPhHWXDY263XJ1d2EPN/50dfbc95-53fa-43c3-8d9b-803a7abf6802.mp3"}, {"language": "fi", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "fi-FI", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/8RYUsFHsalUXjwVG0LjhNwH1j022/voices/3DPhHWXDY263XJ1d2EPN/42bdf854-b5d1-43c2-ad3b-92c4ddd42662.mp3"}, {"language": "id", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "id-ID", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/8RYUsFHsalUXjwVG0LjhNwH1j022/voices/3DPhHWXDY263XJ1d2EPN/f9b0f722-46da-4b6a-81cd-79682037d93b.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751369580}, {"voice_id": "gedzfqL7OGdPbwm0ynTP", "name": "Nadia", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "ru", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_flash_v2_5": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"age": "young", "descriptive": "crisp", "language": "ru", "gender": "female", "use_case": "informative_educational"}, "description": "A young Native Russian female voice. Great for Informative content.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/custom/voices/gedzfqL7OGdPbwm0ynTP/w3TZGiSgIypkSFp0lr0Q.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751369500, "whitelisted_emails": [], "public_owner_id": "a7861f21f1fcebc9960e7dabb066d0e541d5642a4630c69aeeb8cb2a8fc6a307", "original_voice_id": "gedzfqL7OGdPbwm0ynTP", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 90, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "high_quality", "reader_app_enabled": false, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "Nadia", "description": "A young Native Russian female voice. Great for Informative content.", "labels": {"language": "ru", "descriptive": "crisp", "age": "young", "gender": "female", "use_case": "informative_educational", "locale": "ru-RU"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_v2_5_flash", "eleven_multilingual_v2", "eleven_flash_v2_5"], "verified_languages": [{"language": "pt", "model_id": "eleven_multilingual_v2", "accent": "brazilian", "locale": "pt-BR", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/okVahF6EHShqIh96afTcCKlI65A2/voices/gedzfqL7OGdPbwm0ynTP/6c1e70cc-b645-4d5e-a030-27096b4f6405.mp3"}, {"language": "cs", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "cs-CZ", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/okVahF6EHShqIh96afTcCKlI65A2/voices/gedzfqL7OGdPbwm0ynTP/8573f296-25d8-46e0-91df-a807a79637e3.mp3"}, {"language": "es", "model_id": "eleven_multilingual_v2", "accent": "peninsular", "locale": "es-ES", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/okVahF6EHShqIh96afTcCKlI65A2/voices/gedzfqL7OGdPbwm0ynTP/37a65434-0b4a-4fb9-be09-7ddf5920b425.mp3"}, {"language": "de", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "de-DE", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/okVahF6EHShqIh96afTcCKlI65A2/voices/gedzfqL7OGdPbwm0ynTP/bb288a18-85f3-4442-a1d0-27020039e0e5.mp3"}, {"language": "hi", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "hi-IN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/okVahF6EHShqIh96afTcCKlI65A2/voices/gedzfqL7OGdPbwm0ynTP/31149511-d6cc-4f60-bfda-b8b14af5f246.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751369500}, {"voice_id": "aQROLel5sQbj1vuIVi6B", "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_multilingual_sts_v2": "fine_tuned", "eleven_flash_v2_5": "failed"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "fr", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_multilingual_sts_v2": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "parisian", "descriptive": "professional", "age": "middle_aged", "gender": "male", "language": "fr", "use_case": "narrative_story"}, "description": "Middle aged French male voice. Ideal for audioguide and all narration projects.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ajGQb2hVVXdBWWu6HHJEkWasByJ3/voices/aQROLel5sQbj1vuIVi6B/shwzqRrR51OISZWUl7EN.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751369359, "whitelisted_emails": [], "public_owner_id": "b247dc107f092f9c7fd88dd7ac6a4e149890d670ca93290b7924034b604422c1", "original_voice_id": "aQROLel5sQbj1vuIVi6B", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 730, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "high_quality", "reader_app_enabled": true, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "description": "Middle aged French male voice. Ideal for audioguide and all narration projects.", "labels": {"language": "fr", "descriptive": "professional", "age": "middle_aged", "gender": "male", "accent": "parisian", "use_case": "narrative_story", "locale": "fr-FR"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_multilingual_v2", "eleven_turbo_v2_5", "eleven_multilingual_sts_v2", "eleven_v2_5_flash", "eleven_flash_v2_5"], "verified_languages": [{"language": "es", "model_id": "eleven_multilingual_v2", "accent": "latin", "locale": null, "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ajGQb2hVVXdBWWu6HHJEkWasByJ3/voices/aQROLel5sQbj1vuIVi6B/d6397999-6fd6-4f64-941f-b236c241c73f.mp3"}, {"language": "hi", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "hi-IN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ajGQb2hVVXdBWWu6HHJEkWasByJ3/voices/aQROLel5sQbj1vuIVi6B/5c8075f3-5732-43fe-a631-031e80d74393.mp3"}, {"language": "pl", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "pl-PL", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ajGQb2hVVXdBWWu6HHJEkWasByJ3/voices/aQROLel5sQbj1vuIVi6B/dca3fa1e-8b62-45b0-9aab-d3edc9740a3f.mp3"}, {"language": "ru", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "ru-RU", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ajGQb2hVVXdBWWu6HHJEkWasByJ3/voices/aQROLel5sQbj1vuIVi6B/cc805943-9a00-4d7a-ac3e-0524b18e136c.mp3"}, {"language": "fil", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "fil-PH", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ajGQb2hVVXdBWWu6HHJEkWasByJ3/voices/aQROLel5sQbj1vuIVi6B/90ab17a3-5458-4feb-92cc-0159e74e90de.mp3"}, {"language": "cs", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "cs-CZ", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ajGQb2hVVXdBWWu6HHJEkWasByJ3/voices/aQROLel5sQbj1vuIVi6B/597d765e-d998-4412-bc79-1823f39a7645.mp3"}, {"language": "ar", "model_id": "eleven_multilingual_v2", "accent": "modern standard", "locale": "ar-SA", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/ajGQb2hVVXdBWWu6HHJEkWasByJ3/voices/aQROLel5sQbj1vuIVi6B/d73c24c4-d58e-4347-ba74-ff06d055d15a.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751369359}, {"voice_id": "t8BrjWUT5Z23DLLBzbuY", "name": "Modulated French female voice", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_multilingual_sts_v2": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "fr", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_multilingual_sts_v2": "", "eleven_flash_v2_5": "", "eleven_v2_5_flash": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "standard", "descriptive": "modulated", "age": "middle_aged", "gender": "female", "language": "fr", "use_case": "narrative_story"}, "description": "Modulated French female voice, ideal for V3 and conversational !", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/NnZ1eXhNpTYqF1jmlswPUrr9U4v2/voices/t8BrjWUT5Z23DLLBzbuY/Li9DboPs3GZTOiCSBXL4.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751369325, "whitelisted_emails": [], "public_owner_id": "02c2c5edb7dcf4a04ebace122804b84abf70a985db8fdb6ae15109d9b9e01406", "original_voice_id": "t8BrjWUT5Z23DLLBzbuY", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 730, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "professional", "reader_app_enabled": null, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "Modulated French female voice", "description": "Modulated French female voice, ideal for V3 and conversational !", "labels": {"language": "fr", "descriptive": "modulated", "age": "middle_aged", "gender": "female", "accent": "standard", "use_case": "narrative_story", "locale": "fr-FR"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_turbo_v2_5", "eleven_v2_5_flash", "eleven_flash_v2_5", "eleven_multilingual_v2", "eleven_multilingual_sts_v2"], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751369325}, {"voice_id": "FTNCalFNG5bRnkkaP5Ug", "name": "<PERSON>", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_multilingual_sts_v2": "fine_tuned", "eleven_flash_v2_5": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "de", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_multilingual_sts_v2": "", "eleven_flash_v2_5": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "standard", "descriptive": "calm", "age": "middle_aged", "gender": "male", "language": "de", "use_case": "narrative_story"}, "description": "A calm male German studio-quality voice. Perfect for German language and English with a German accent. <PERSON>'s clear, easy-to-understand narration is excellent for German voiceovers, audiobooks and podcasts, accurate video dubbing, advertising, and social media such as Reels, Stories, and YouTube. ", "preview_url": "https://storage.googleapis.com/eleven-public-prod/custom/voices/FTNCalFNG5bRnkkaP5Ug/MQ8WiEa4ozV4HevPeMba.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751369275, "whitelisted_emails": [], "public_owner_id": "80d9191368f6824d0aacf4080a0894aecb0c4bdd82928eb623e585caf54a84b7", "original_voice_id": "FTNCalFNG5bRnkkaP5Ug", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 730, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "high_quality", "reader_app_enabled": true, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "<PERSON>", "description": "A calm male German studio-quality voice. Perfect for German language and English with a German accent. <PERSON>'s clear, easy-to-understand narration is excellent for German voiceovers, audiobooks and podcasts, accurate video dubbing, advertising, and social media such as Reels, Stories, and YouTube. ", "labels": {"language": "de", "descriptive": "calm", "age": "middle_aged", "gender": "male", "accent": "standard", "use_case": "narrative_story", "locale": "de-DE"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_turbo_v2_5", "eleven_multilingual_sts_v2", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5"], "verified_languages": [{"language": "hi", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "hi-IN", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/M5v8YBujF5MxArJI46g2wPsKtpp1/voices/FTNCalFNG5bRnkkaP5Ug/6ec686ed-fb69-47a6-8491-7592006ec60b.mp3"}, {"language": "pl", "model_id": "eleven_multilingual_v2", "accent": "standard", "locale": "pl-PL", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/M5v8YBujF5MxArJI46g2wPsKtpp1/voices/FTNCalFNG5bRnkkaP5Ug/0ddedfa5-00c1-4b32-9a39-93589d8a895f.mp3"}, {"language": "es", "model_id": "eleven_multilingual_v2", "accent": "peninsular", "locale": "es-ES", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/M5v8YBujF5MxArJI46g2wPsKtpp1/voices/FTNCalFNG5bRnkkaP5Ug/eeac8ec6-beb4-43f9-8c63-f7d26c57e81c.mp3"}, {"language": "fr", "model_id": "eleven_multilingual_v2", "accent": "belgian", "locale": "fr-BE", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/M5v8YBujF5MxArJI46g2wPsKtpp1/voices/FTNCalFNG5bRnkkaP5Ug/71d6f10a-0fd0-4148-8ea3-89aa8a69ee18.mp3"}, {"language": "id", "model_id": "eleven_multilingual_v2", "accent": "javanese", "locale": "jv-ID", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/M5v8YBujF5MxArJI46g2wPsKtpp1/voices/FTNCalFNG5bRnkkaP5Ug/65f38d6b-a39d-4413-8c80-6196f392e966.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751369275}, {"voice_id": "dCnu06FiOZma2KVNUoPZ", "name": "<PERSON><PERSON>", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_multilingual_sts_v2": "fine_tuned", "eleven_flash_v2_5": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "de", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_multilingual_sts_v2": "", "eleven_flash_v2_5": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "standard", "descriptive": "confident", "age": "young", "gender": "female", "language": "de", "use_case": "narrative_story"}, "description": "Female, 20s - 30s, opinionated and confident, yet soft and empathetic, with a relaxed creak at times. Native German.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/O7y9fOlmbYT9qQKWhyIAOS3gelR2/voices/dCnu06FiOZma2KVNUoPZ/d8limkNELFtsYVKDugiF.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751369249, "whitelisted_emails": [], "public_owner_id": "dfad8b2f4835158499a4fc8aa6b1570d022c5196a553d7fca7788446fb3fa4a6", "original_voice_id": "dCnu06FiOZma2KVNUoPZ", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": true, "rate": 1, "fiat_rate": null, "notice_period": 730, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "high_quality", "reader_app_enabled": true, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "<PERSON><PERSON>", "description": "Female, 20s - 30s, opinionated and confident, yet soft and empathetic, with a relaxed creak at times. Native German.", "labels": {"language": "de", "descriptive": "confident", "age": "young", "gender": "female", "accent": "standard", "use_case": "narrative_story", "locale": "de-DE"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_turbo_v2_5", "eleven_multilingual_sts_v2", "eleven_multilingual_v2", "eleven_v2_5_flash", "eleven_flash_v2_5"], "verified_languages": [{"language": "en", "model_id": "eleven_multilingual_v2", "accent": null, "locale": "en-US", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/O7y9fOlmbYT9qQKWhyIAOS3gelR2/voices/dCnu06FiOZma2KVNUoPZ/4c17dbcd-bb3b-4c99-b20a-08468b2ef7fd.mp3"}, {"language": "es", "model_id": "eleven_multilingual_v2", "accent": "peninsular", "locale": "es-ES", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/O7y9fOlmbYT9qQKWhyIAOS3gelR2/voices/dCnu06FiOZma2KVNUoPZ/6a805aa5-3de9-4c19-b088-236f00475d4f.mp3"}], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751369249}, {"voice_id": "z1EhmmPwF0ENGYE8dBE6", "name": "Christian <PERSON> - Conversational v2 Clean", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_multilingual_sts_v2": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "de", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_turbo_v2_5": "", "eleven_multilingual_sts_v2": "", "eleven_flash_v2_5": "", "eleven_v2_5_flash": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "standard", "age": "middle_aged", "language": "de", "gender": "male", "use_case": "conversational"}, "description": "A natural and friendly speaking style that conveys the feel of a genuine, warm conversation. This voice is perfect for podcasts, interviews, casual videos, and any production requiring an authentic and approachable tone. Whether you’re creating content for social media, YouTube, or corporate storytelling, this voice brings a relatable and engaging quality that resonates with audiences.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/database/user/yAkZ0o1apPRKi8NGRRnh7wGgDJv1/voices/z1EhmmPwF0ENGYE8dBE6/4gAUKXBCYNKHP6Btawi3.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751369223, "whitelisted_emails": [], "public_owner_id": "c3b3f952ee4e94b2e54eb155b0548ce4ebe70b12b218941564ff4dd930e5dd0f", "original_voice_id": "z1EhmmPwF0ENGYE8dBE6", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 730, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "high_quality", "reader_app_enabled": true, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "Christian <PERSON> - Conversational v2 Clean", "description": "A natural and friendly speaking style that conveys the feel of a genuine, warm conversation. This voice is perfect for podcasts, interviews, casual videos, and any production requiring an authentic and approachable tone. Whether you’re creating content for social media, YouTube, or corporate storytelling, this voice brings a relatable and engaging quality that resonates with audiences.", "labels": {"language": "de", "age": "middle_aged", "accent": "standard", "gender": "male", "use_case": "conversational", "locale": "de-DE"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_flash_v2_5", "eleven_multilingual_v2", "eleven_multilingual_sts_v2", "eleven_turbo_v2_5", "eleven_v2_5_flash"], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751369223}, {"voice_id": "nHNZWlqUWtEKPr3hhFQP", "name": "<PERSON><PERSON>", "samples": null, "category": "professional", "fine_tuning": {"is_allowed_to_fine_tune": true, "state": {"eleven_multilingual_v2": "fine_tuned", "eleven_turbo_v2_5": "fine_tuned", "eleven_flash_v2_5": "fine_tuned", "eleven_v2_5_flash": "fine_tuned"}, "verification_failures": [], "verification_attempts_count": 0, "manual_verification_requested": false, "language": "pt", "progress": {}, "message": {"eleven_multilingual_v2": "", "eleven_flash_v2_5": ""}, "dataset_duration_seconds": null, "verification_attempts": null, "slice_ids": null, "manual_verification": null, "max_verification_attempts": 0, "next_max_verification_attempts_reset_unix_ms": 0}, "labels": {"accent": "brazilian", "descriptive": "formal", "age": "middle_aged", "gender": "female", "language": "pt", "use_case": "entertainment_tv"}, "description": "\nYoung, soft, sweet, Brazilian female voice. Perfect for narrating entertainment, and News.", "preview_url": "https://storage.googleapis.com/eleven-public-prod/r8lAwcnaBaQN3obCzPFeVuqOb4Q2/voices/nHNZWlqUWtEKPr3hhFQP/346b3571-64e4-47e5-afb9-b56bdf72470b.mp3", "available_for_tiers": [], "settings": null, "sharing": {"status": "copied", "history_item_sample_id": null, "date_unix": 1751369177, "whitelisted_emails": [], "public_owner_id": "1a856d3b1e04ace7dee9f395f4d2fe74db9cf19cf8672c56233459865db48ba2", "original_voice_id": "nHNZWlqUWtEKPr3hhFQP", "financial_rewards_enabled": false, "free_users_allowed": true, "live_moderation_enabled": false, "rate": 1, "fiat_rate": null, "notice_period": 90, "disable_at_unix": null, "voice_mixing_allowed": false, "featured": false, "category": "professional", "reader_app_enabled": null, "image_url": "", "ban_reason": null, "liked_by_count": 0, "cloned_by_count": 0, "name": "<PERSON><PERSON>", "description": "\nYoung, soft, sweet, Brazilian female voice. Perfect for narrating entertainment, and News.", "labels": {"language": "pt", "descriptive": "formal", "age": "middle_aged", "gender": "female", "accent": "brazilian", "use_case": "entertainment_tv", "locale": "pt-BR"}, "review_status": "not_requested", "review_message": null, "enabled_in_library": false, "instagram_username": null, "twitter_username": null, "youtube_username": null, "tiktok_username": null, "moderation_check": null, "reader_restricted_on": null}, "high_quality_base_model_ids": ["eleven_v2_5_flash", "eleven_multilingual_v2", "eleven_flash_v2_5"], "verified_languages": [], "safety_control": null, "voice_verification": {"requires_verification": false, "is_verified": false, "verification_failures": [], "verification_attempts_count": 0, "language": null, "verification_attempts": null}, "permission_on_resource": "admin", "is_owner": false, "is_legacy": false, "is_mixed": false, "created_at_unix": 1751369177}], "has_more": true, "total_count": 25, "next_page_token": "fG5ITlpXbHFVV3RFS1ByM2hoRlFQ"}