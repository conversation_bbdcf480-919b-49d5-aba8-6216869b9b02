package co.sandai.zeus.api.payment;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.RedirectException;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.payment.PaymentService;
import co.sandai.zeus.domain.payment.dao.OrderDO;
import com.stripe.exception.StripeException;
import jakarta.annotation.Resource;
import java.util.Objects;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 它不是一个RestFul风格的api
 */
@Controller
@RequestMapping("/payment/sessions")
public class SessionSuccessController {

    @Resource
    private PaymentService paymentService;

    @GetMapping("/checkout/success")
    @PrintLog
    public void checkoutSuccess(@RequestParam(value = "session_id") String checkoutSessionId)
            throws InterruptedException {
        OrderDO orderDO = paymentService.queryOrderBySessionId(checkoutSessionId);
        if (Objects.isNull(orderDO)) {
            try {
                // webhook还未同步成功，主动查询api进行同步
                orderDO = paymentService.syncOrderFromCheckoutSession(checkoutSessionId, false, false);
            } catch (StripeException e) {
                throw ZeusServiceException.internalError("Failed to sync order", e)
                        .code(ErrorCode.ThirdPartyError);
            }
        }
        if (Objects.nonNull(orderDO)) {
            // 直接重定向
            throw new RedirectException(
                    ErrorCode.Redirect,
                    String.format(
                            "/app?paySuccess=true&payValue=%s&payCurrency=%s",
                            orderDO.getAmount(), orderDO.getCurrency()));
        }
    }
}
