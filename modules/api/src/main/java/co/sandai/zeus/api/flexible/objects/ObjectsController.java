package co.sandai.zeus.api.flexible.objects;

import co.sandai.zeus.api.flexible.objects.view.ObjectVO;
import co.sandai.zeus.domain.flexible.ObjectDTO;
import co.sandai.zeus.domain.flexible.ObjectsService;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/objects")
@Slf4j
public class ObjectsController {

    @Resource
    private ObjectsService objectsService;

    @GetMapping
    public List<ObjectDTO> listObjects(
            @RequestParam(required = false, defaultValue = "0") int offset,
            @RequestParam(required = false, defaultValue = "10") int limit,
            @RequestParam(required = false) String type) {
        return objectsService.listObjects(offset, limit, type);
    }

    @GetMapping("/{id}")
    public ObjectDTO getObject(@PathVariable Long id) {
        return objectsService.getObject(id);
    }

    @PostMapping
    public ObjectVO createObject(@RequestBody ObjectVO objectVO) {
        ObjectDTO objectDTO = toDTO(objectVO);
        return toVO(objectsService.createObject(objectDTO));
    }

    @PutMapping("/{id}")
    public ObjectDTO updateObject(@PathVariable Long id, @RequestBody ObjectVO objectVO) {
        return objectsService.updateObject(id, toDTO(objectVO));
    }

    @DeleteMapping("/{id}")
    public void deleteObject(@PathVariable Long id) {
        objectsService.deleteObject(id);
    }

    private ObjectDTO toDTO(ObjectVO objectVO) {
        ObjectDTO objectDTO = new ObjectDTO();
        objectDTO.setId(objectVO.getId());
        objectDTO.setType(objectVO.getType());
        objectDTO.setData(objectVO.getData());
        objectDTO.setUserId(objectVO.getUserId());
        objectDTO.setDeleted(objectVO.getDeleted());
        objectDTO.setCreateTime(objectVO.getCreateTime());
        objectDTO.setUpdateTime(objectVO.getUpdateTime());
        return objectDTO;
    }

    private ObjectVO toVO(ObjectDTO objectDTO) {
        ObjectVO objectVO = new ObjectVO();
        objectVO.setId(objectDTO.getId());
        objectVO.setType(objectDTO.getType());
        objectVO.setData(objectDTO.getData());
        objectVO.setUserId(objectDTO.getUserId());
        objectVO.setDeleted(objectDTO.getDeleted());
        objectVO.setCreateTime(objectDTO.getCreateTime());
        objectVO.setUpdateTime(objectDTO.getUpdateTime());
        return objectVO;
    }
}
