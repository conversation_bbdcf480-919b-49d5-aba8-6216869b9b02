package co.sandai.zeus.api.credit.view;

import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.common.vo.ListResponseByToken;
import co.sandai.zeus.domain.credit.dao.UserCreditTransactionDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserCreditTransactionPageVo
        extends ListResponseByToken<UserCreditTransactionPageVo.UserCreditTransactionVo> {

    @Data
    @Accessors(chain = true)
    public static class UserCreditTransactionVo {
        private int amount;
        private String createTime;
        private String direction;
        private String reasonType;
        private String reasonSubType;

        public static UserCreditTransactionVo convertFromModel(UserCreditTransactionDO userCreditTransactionDO) {
            UserCreditTransactionVo userCreditTransactionVo = new UserCreditTransactionVo();
            userCreditTransactionVo.setCreateTime(TimeUtil.format(userCreditTransactionDO.getCreateTime()));
            userCreditTransactionVo.setDirection(userCreditTransactionDO.getDirection());
            userCreditTransactionVo.setAmount(userCreditTransactionDO.getAmount());
            userCreditTransactionVo.setReasonType(
                    UserCreditTransactionDO.ReasonTypeEnum.getTextByName(userCreditTransactionDO.getReasonType()));
            userCreditTransactionVo.setReasonSubType(UserCreditTransactionDO.ReasonSubTypeEnum.getTextByName(
                    userCreditTransactionDO.getReasonSubType()));
            return userCreditTransactionVo;
        }
    }
}
