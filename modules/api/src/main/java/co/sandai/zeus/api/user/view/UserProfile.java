package co.sandai.zeus.api.user.view;

import co.sandai.zeus.config.SpringContext;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.user.dao.User;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class UserProfile {
    private long id;
    private String email;
    private String nickname;
    private String description;
    private boolean emailVerified;

    @JsonIgnore
    private long avatarId;

    @JsonProperty("avatarUrl")
    public String getAvatarUrl() {
        if (avatarId == 0) {
            return "";
        }
        AssetService assetService = SpringContext.getBean(AssetService.class);
        return assetService.getAssetPublicUrlById(this.avatarId, 60);
    }

    public static UserProfile fromUser(User user) {
        return UserProfile.builder()
                .id(user.getId())
                .avatarId(user.getAvatarId())
                .emailVerified(user.isEmailVerified())
                .email(user.getEmail())
                .description(user.getDescription())
                .nickname(user.getNickname())
                .build();
    }
}
