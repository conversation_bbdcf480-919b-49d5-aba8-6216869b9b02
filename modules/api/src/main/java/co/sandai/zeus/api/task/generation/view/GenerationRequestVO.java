package co.sandai.zeus.api.task.generation.view;

import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class GenerationRequestVO {
    private String model = "mvp-internal-test";
    private long seed;
    private String aspectRatio;

    /**
     * 推理来源的condition
     */
    // TODO 这里的输入是不是需要改成数组，支持audio + video的情况
    @Nullable
    private ConditionVO source;

    /**
     * 各个chunk的条件类型应一致，除了视频续写的情况
     */
    @NotNull
    private List<ChunkVO> chunks;

    @NotNull
    private ExtraArgs extraArgs;

    private TaskSourceEnum taskSource;
}
