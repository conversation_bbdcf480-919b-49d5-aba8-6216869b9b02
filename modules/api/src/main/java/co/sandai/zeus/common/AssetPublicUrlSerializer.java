package co.sandai.zeus.common;

import co.sandai.zeus.domain.asset.service.AssetService;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AssetPublicUrlSerializer extends JsonSerializer<Long> {

    @Autowired
    AssetService assetService;

    @Override
    public void serialize(Long assetId, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
            throws IOException {
        String url = assetService.getAssetPublicUrlById(assetId, 60);
        jsonGenerator.writeString(url);
    }
}
