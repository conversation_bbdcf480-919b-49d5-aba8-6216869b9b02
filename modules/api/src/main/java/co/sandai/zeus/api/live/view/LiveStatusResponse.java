package co.sandai.zeus.api.live.view;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * Response model for live mode status check.
 */
@Data
public class LiveStatusResponse {
    private LiveModeStatus status;
    private Long streamId; // Current user's stream ID if in a session

    @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
    private LocalDateTime nextAvailableTime; // When user can use live mode again if already used

    @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
    private LocalDateTime streamEndTime;

    @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
    private LocalDateTime streamStartTime; // When the stream started (first task start time)

    private List<Long> taskIds;
}
