package co.sandai.zeus.api.task.generation.view;

import co.sandai.zeus.domain.common.CropArea;
import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 额外推理参数
 */
@Data
public class ExtraArgs {

    private Boolean enablePromptEnhancement = false;

    @JsonProperty("tSchedulerFunc")
    private String tSchedulerFunc = "";

    @JsonProperty("tSchedulerArgs")
    private String tSchedulerArgs = "";

    private CropArea cropArea = null;
    private TaskExtraInferArgs extraInferArgs = null;
}
