package co.sandai.zeus.api.user.config;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.vo.ErrorResponse;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.SessionService;
import co.sandai.zeus.domain.user.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * Combined session authentication and authorization filter.
 * Replaces both ZeusSessionFilter and AuthorizationFilter functionality.
 *
 * This filter:
 * 1. Performs session authentication by validating cookies and setting user context
 * 2. Performs authorization by checking if paths are public or require authentication
 * 3. Returns 401 for unauthenticated requests to protected paths
 */
@Slf4j
@Component
@Order(1) // Single filter combining session and authorization logic
public class ZeusSessionFilter extends OncePerRequestFilter {

    @Autowired
    private SessionService sessionService;

    @Value("${zeus.auth.cookie.name}")
    private String cookieName;

    @Autowired
    private UserService userService;

    @Autowired
    private PublicPathConfiguration publicPathConfiguration;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Step 1: Session Authentication - Extract and validate session from cookies
        performSessionAuthentication(request);

        try {
            // Step 2: Authorization - Check if path requires authentication
            String requestPath = request.getRequestURI();
            String httpMethod = request.getMethod();

            // Check if the path is public using the mature AntPathMatcher with HTTP method support
            if (publicPathConfiguration.isPublicPath(requestPath, httpMethod)) {
                filterChain.doFilter(request, response);
                return;
            }

            // Check if user is authenticated for protected paths
            if (!AuthenticationContext.isAuthenticated()) {
                sendUnauthorizedResponse(response);
                return;
            }

            // User is authenticated and path is protected - proceed
            filterChain.doFilter(request, response);
        } finally {
            // Clear authentication context after request processing
            AuthenticationContext.clear();
        }
    }

    /**
     * Performs session authentication by reading cookies and validating session tokens.
     * Sets the authenticated user in AuthenticationContext if valid session is found.
     */
    private void performSessionAuthentication(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (!cookieName.equals(cookie.getName())) {
                    continue;
                }
                String sessionId = cookie.getValue();
                if (sessionId == null || sessionId.isEmpty()) {
                    break;
                }
                User user = sessionService.getUserBySessionToken(sessionId);
                if (user == null) {
                    break;
                }
                AuthenticationContext.setCurrentUser(user);
                log.debug("{} login with session id {}", user.getName(), sessionId);
                break; // Found valid session, no need to check other cookies
            }
        }
    }

    /**
     * Sends a 401 Unauthorized response with JSON error details.
     * Used when a protected path is accessed without authentication.
     */
    private void sendUnauthorizedResponse(HttpServletResponse response) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();

        ErrorResponse error = ErrorResponse.builder().code(ErrorCode.NotLogin).build();
        error.setMessage("Unauthorized");
        String jsonString = objectMapper.writeValueAsString(error);

        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(jsonString);
    }
}
