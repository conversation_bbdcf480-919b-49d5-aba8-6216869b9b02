package co.sandai.zeus.api.task.service;

import co.sandai.zeus.api.task.view.TaskCreateRequest;
import co.sandai.zeus.api.task.view.TaskUpdateStatusRequest;
import co.sandai.zeus.api.task.view.TaskView;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.domain.share.service.ShareService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.dao.TaskType;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.domain.user.dao.User;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class TaskApiService {

    @Autowired
    TaskService taskService;

    @Autowired
    UserApiService userApiService;

    @Autowired
    private ShareService shareService;

    public Task createTask(TaskCreateRequest taskCreateRequest) {
        return createTask(taskCreateRequest, TaskSourceEnum.NORMAL);
    }

    public Task createTask(TaskCreateRequest taskCreateRequest, TaskSourceEnum taskSourceEnum) {
        Task task = Task.builder()
                .type(TaskType.T2V)
                .cropArea(taskCreateRequest.cropArea)
                .extraInferArgs(taskCreateRequest.extraInferArgs)
                .prompt(taskCreateRequest.prompt)
                .duration(taskCreateRequest.duration)
                .model(taskCreateRequest.model)
                .aspectRatio(taskCreateRequest.aspectRatio)
                .seed(taskCreateRequest.seed)
                .tSchedulerFunc(taskCreateRequest.tSchedulerFunc)
                .tSchedulerArgs(taskCreateRequest.tSchedulerArgs)
                .enablePromptEnhancement(taskCreateRequest.enablePromptEnhancement)
                .enableAutoGenFirstFrame(taskCreateRequest.enableAutoGenFirstFrame)
                .userId(userApiService.getCurrentUser().getId())
                .taskSource(taskSourceEnum)
                .build();

        if (taskCreateRequest.sourceVideoId != null && taskCreateRequest.sourceVideoId > 0) {
            task.setSourceVideoId(taskCreateRequest.sourceVideoId);
            task.setType(TaskType.ExtendDuration);
            task.setSourceVideoStartTimestamp(taskCreateRequest.sourceVideoStartTimestamp);
            task.setSourceVideoEndTimestamp(taskCreateRequest.sourceVideoEndTimestamp);
        }

        if (taskCreateRequest.sourceImageId != null && taskCreateRequest.sourceImageId > 0) {
            task.setSourceImageId(taskCreateRequest.sourceImageId);
            task.setType(TaskType.I2V);
        }

        return taskService.submitTask(task);
    }

    public IDOnlyResponse updateStatus(long taskId, TaskUpdateStatusRequest taskUpdateStatusRequest) {
        User user = userApiService.getCurrentUser();
        Task task = taskService.getTaskById(taskId);
        if (task == null) {
            throw new ZeusServiceException(HttpStatus.NOT_FOUND, ErrorCode.NotFound, "task not found");
        }
        if (user.getId() != task.getUserId()) {
            throw ZeusServiceException.forbidden("have no permission to do this operation");
        }
        if (taskUpdateStatusRequest.status == TaskStatus.Canceled) {
            taskService.cancelTask(task);
            return IDOnlyResponse.create(taskId);
        }
        throw new ZeusServiceException(
                ErrorCode.UnSupportedOperation,
                String.format("can not update task status to %s", taskUpdateStatusRequest.status));
    }

    public TaskView getTaskDetail(long taskId) {
        User user = userApiService.getCurrentUser();
        Task task = taskService.getTaskById(taskId);
        if (Objects.isNull(task)) {
            throw new ZeusServiceException(HttpStatus.NOT_FOUND, ErrorCode.NotFound, "task not found");
        }
        //        if (Objects.nonNull(user) && user.getId() == task.getUserId()) {
        // TODO: 检查任务的 owner
        if (Objects.nonNull(user)) {
            return TaskView.fromTask(task);
        }
        boolean shared = shareService.isTaskSharedTaskById(taskId);
        if (shared) {
            return TaskView.fromTask(task);
        }

        throw ZeusServiceException.forbidden("no access to this resource");
    }

    public IDOnlyResponse deleteTask(long taskId) {
        Task task = taskService.getTaskById(taskId);
        if (Objects.isNull(task)) {
            throw new ZeusServiceException(HttpStatus.NOT_FOUND, ErrorCode.NotFound, "task not found");
        }
        User user = userApiService.getCurrentUser();
        if (user.getId() != task.getUserId()) {
            throw ZeusServiceException.forbidden("can not delete this task");
        }
        taskService.deleteTaskById(taskId);
        return IDOnlyResponse.create(taskId);
    }
}
