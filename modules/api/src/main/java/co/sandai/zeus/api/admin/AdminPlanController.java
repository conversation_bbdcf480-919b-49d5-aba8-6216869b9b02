package co.sandai.zeus.api.admin;

import co.sandai.zeus.api.admin.response.GrantMembershipResultResponse;
import co.sandai.zeus.api.admin.service.AdminPlanService;
import co.sandai.zeus.api.admin.view.GrantMembershipRequest;
import co.sandai.zeus.api.admin.view.UserPlanInfoResponse;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.common.WhiteListService;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.dao.UserPlanMapper;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/admin/plans")
public class AdminPlanController {

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private UserService userService;

    @Autowired
    private WhiteListService whiteListService;

    @Autowired
    private AdminPlanService adminPlanService;

    @Autowired
    private UserPlanMapper userPlanMapper;

    @Autowired
    private PlanConfigService planConfigService;

    /**
     * 管理员免费给用户分配会员计划
     * 只允许管理员调用
     */
    @PostMapping("/grant-membership")
    @PrintLog
    public GrantMembershipResultResponse grantMembership(@Valid @RequestBody GrantMembershipRequest request) {
        // 验证当前用户是否为超级用户
        User currentUser = userApiService.getCurrentUser();
        if (!whiteListService.isSuperUser(currentUser.getEmail())) {
            throw ZeusServiceException.forbidden("Only super users can grant free memberships");
        }
        log.info(
                "Admin {} granting membership plan {} to user {}",
                currentUser.getId(),
                request.getPlanCode(),
                request.getUserId());
        AdminPlanService.GrantMembershipResult result =
                adminPlanService.grantFreeMembership(request.getUserId(), request.getPlanCode());
        return new GrantMembershipResultResponse(
                result.success,
                result.message,
                result.orderId,
                result.userId,
                result.planCode,
                result.currentPlanCode,
                result.currentPlanExpireTime,
                result.grantTime);
    }

    /**
     * 管理员查询用户计划信息
     * 用于在分配计划前查看用户当前状态
     */
    @GetMapping("/user/{userId}")
    @PrintLog
    public UserPlanInfoResponse getUserPlanInfo(@PathVariable Long userId) {
        // 验证当前用户是否为超级用户
        User currentUser = userApiService.getCurrentUser();
        if (!whiteListService.isSuperUser(currentUser.getEmail())) {
            throw ZeusServiceException.forbidden("Only super users can view user plan information");
        }

        // 获取用户信息
        User user = userService.getUserById(userId);
        if (user == null) {
            throw ZeusServiceException.badRequest("User not found with ID: " + userId);
        }

        // 获取用户当前计划
        UserPlanDO userPlan = userPlanMapper.queryByUserId(userId);
        Plan plan = null;
        if (userPlan != null) {
            plan = planConfigService.getPlanByCode(userPlan.getPlanCode(), null);
        }

        return UserPlanInfoResponse.fromUser(user, userPlan, plan);
    }

    /**
     * 管理员根据邮箱查询用户计划信息
     * 便于管理员通过邮箱快速查找用户
     */
    @GetMapping("/user/by-email")
    @PrintLog
    public UserPlanInfoResponse getUserPlanInfoByEmail(@RequestParam String email) {
        // 验证当前用户是否为超级用户
        User currentUser = userApiService.getCurrentUser();
        if (!whiteListService.isSuperUser(currentUser.getEmail())) {
            throw ZeusServiceException.forbidden("Only super users can view user plan information");
        }

        // 获取用户信息
        User user = userService.getUserByEmail(email);
        if (user == null) {
            throw ZeusServiceException.badRequest("User not found with email: " + email);
        }

        // 获取用户当前计划
        UserPlanDO userPlan = userPlanMapper.queryByUserId(user.getId());
        Plan plan = null;
        if (userPlan != null) {
            plan = planConfigService.getPlanByCode(userPlan.getPlanCode(), null);
        }

        return UserPlanInfoResponse.fromUser(user, userPlan, plan);
    }
}
