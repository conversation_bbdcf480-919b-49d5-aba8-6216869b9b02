package co.sandai.zeus.api.user.view;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class UserEmailSignupRequest {
    @NotNull
    @NotEmpty
    @Email
    private String email;

    @NotNull
    @NotEmpty
    private String displayName;

    @NotNull
    @NotEmpty
    private String password;

    private String confirmPassword;

    private String captchaVerifyParam;
}
