package co.sandai.zeus.api.plan;

import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.domain.plan.model.Auth;
import co.sandai.zeus.domain.plan.model.MemberShip;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.PriceModel;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class PlanVo {

    private String price;
    private String originalPrice;
    private String planCode;
    private String currency;
    private String priceId;
    private String intervalType;
    private String memberShipName;
    private String description;
    private String tooltipForCredit;
    private int planGrade;
    private int creditAmount;
    private boolean isRecommend;
    private List<FeatureVO> featureList;

    @Data
    @Accessors(chain = true)
    public static class FeatureVO {
        private String description;
        private String tooltip;

        public static FeatureVO convertFromModel(Auth auth) {
            FeatureVO featureVO = new FeatureVO();
            featureVO.setDescription(auth.getDescription());
            featureVO.setTooltip(auth.getToolTip());
            return featureVO;
        }
    }

    public static PlanVo convertFromModel(Plan plan, PriceModel priceModel) {
        PlanVo planVo = new PlanVo();
        planVo.setPlanCode(plan.getPlanCode());
        if (priceModel != null) {
            planVo.setPrice(priceModel.getPrice());
            planVo.setOriginalPrice(priceModel.getOriginalPrice());
            planVo.setCurrency(priceModel.getCurrency().getCurrencyCode());
            planVo.setPriceId(priceModel.getOutPriceId());
        } else {
            LogUtil.errorf(log, "priceModel not found, planCode: {0}", plan.getPlanCode());
        }
        planVo.setIntervalType(plan.getIntervalTypeEnum().getCode());
        planVo.setMemberShipName(plan.getMemberShip().getName());
        planVo.setDescription(plan.getMemberShip().getDescription());
        planVo.setTooltipForCredit(plan.getMemberShip().getToolTipForCredit());
        planVo.setPlanGrade(plan.getPlanGrade());
        planVo.setCreditAmount(plan.getMemberShip().getSubscriptionCreditNum());
        planVo.setRecommend(plan.isRecommended());

        List<FeatureVO> featureVOList = new ArrayList<>();
        MemberShip memberShip = plan.getMemberShip();
        memberShip.getAuthList().forEach(auth -> featureVOList.add(FeatureVO.convertFromModel(auth)));
        planVo.setFeatureList(featureVOList);
        return planVo;
    }
}
