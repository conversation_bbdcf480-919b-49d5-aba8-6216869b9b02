package co.sandai.zeus.api.platform.apikey.view;

import co.sandai.zeus.domain.platform.apikey.dao.ApiKey;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ApiKeyVo {
    long id;
    String name;
    LocalDateTime createTime;

    public static ApiKeyVo fromApiKey(ApiKey apiKey) {
        return ApiKeyVo.builder()
                .id(apiKey.getId())
                .createTime(apiKey.getCreateTime())
                .name(apiKey.getName())
                .build();
    }
}
