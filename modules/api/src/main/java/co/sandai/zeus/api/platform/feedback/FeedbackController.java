package co.sandai.zeus.api.platform.feedback;

import co.sandai.zeus.api.platform.PlatformUserApiService;
import co.sandai.zeus.api.platform.feedback.view.CustomApiPackageRequestVo;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.domain.platform.feedback.FeedbackService;
import co.sandai.zeus.domain.platform.feedback.dao.CustomApiPackageRequest;
import co.sandai.zeus.domain.platform.organization.dao.Organization;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/platform/feedback")
public class FeedbackController {

    private final PlatformUserApiService platformUserApiService;
    private final FeedbackService feedbackService;

    public FeedbackController(PlatformUserApiService platformUserApiService, FeedbackService feedbackService) {
        this.platformUserApiService = platformUserApiService;
        this.feedbackService = feedbackService;
    }

    @PostMapping("/custom-api-package-request")
    public IDOnlyResponse createCustomApiPackageRequest(@Valid @RequestBody CustomApiPackageRequestVo data) {
        Organization org = platformUserApiService.getCurrentOrg();
        CustomApiPackageRequest model = CustomApiPackageRequest.builder()
                .orgId(org.getId())
                .userId(org.getUserId())
                .phone(data.getPhone())
                .detail(data.getDetail())
                .company(data.getCompany())
                .business(data.getBusiness())
                .build();
        feedbackService.createCustomApiPackageRequest(model);
        return IDOnlyResponse.create(model.getId());
    }
}
