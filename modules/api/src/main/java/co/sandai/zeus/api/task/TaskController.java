package co.sandai.zeus.api.task;

import co.sandai.zeus.api.task.service.TaskApiService;
import co.sandai.zeus.api.task.view.TaskCreateRequest;
import co.sandai.zeus.api.task.view.TaskDebugInfoVO;
import co.sandai.zeus.api.task.view.TaskResultView;
import co.sandai.zeus.api.task.view.TaskUpdateStatusRequest;
import co.sandai.zeus.api.task.view.TaskView;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.infra.infer.InferClient;
import co.sandai.zeus.infra.infer.dto.InferCommonResponse;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/tasks")
public class TaskController {
    @Autowired
    TaskService taskService;

    @Autowired
    TaskApiService taskApiService;

    @Autowired
    UserApiService userApiService;

    @Autowired
    private AssetService assetService;

    @Resource
    private InferClient inferClient;

    @GetMapping("")
    @PrintLog
    public ListResponseByPage<TaskView> getTasks(
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit,
            @RequestParam(value = "taskSource", required = false) TaskSourceEnum taskSource) {
        log.info("getTasks offset: {}, limit: {}, type: {}", offset, limit, taskSource);
        long userId = userApiService.getCurrentUser().getId();
        int taskCount = taskService.getTaskCountByUserId(userId);
        List<Task> tasks;
        if (taskSource != null) {
            tasks = taskService.getTasksByUserId(userId, offset, limit, Arrays.asList(taskSource));
        } else {
            tasks = taskService.getTasksByUserId(userId, offset, limit, Arrays.asList(TaskSourceEnum.NORMAL));
        }
        return new ListResponseByPage<>(tasks.stream().map(TaskView::fromTask).toList(), taskCount, offset, limit);
    }

    @PostMapping("")
    @PrintLog
    public TaskView createTask(@Valid @RequestBody TaskCreateRequest taskReq) {
        Task task = taskApiService.createTask(taskReq);
        return TaskView.fromTask(task);
    }

    @CrossOrigin(origins = "*") // 允许所有来源跨域请求
    @GetMapping("{taskId}/chunks.m3u8")
    @PrintLog
    public ResponseEntity<String> getM3u8Content(@PathVariable long taskId) {
        Task task = taskService.getTaskById(taskId);
        if (task == null) {
            throw new ZeusServiceException(ErrorCode.TaskNotFound);
        }
        byte[] content = taskService.getTaskM3U8FileContent(task);
        String body = new String(content, StandardCharsets.UTF_8);
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("application/vnd.apple.mpegurl"))
                .body(body);
    }

    @GetMapping("{taskId}")
    @PrintLog
    public TaskView taskDetail(@PathVariable long taskId) {
        return taskApiService.getTaskDetail(taskId);
    }

    @GetMapping("{taskId}/result")
    @PrintLog
    public TaskResultView taskResult(@PathVariable long taskId) {
        Asset asset = taskService.getTaskResultVideoAssetById(taskId);
        return TaskResultView.builder()
                .downloadUrl(assetService.getAssetPublicUrl(asset, 60))
                .build();
    }

    @PutMapping("{taskId}/status")
    @PrintLog
    public IDOnlyResponse updateStatus(@PathVariable long taskId, @Valid @RequestBody TaskUpdateStatusRequest data) {
        return taskApiService.updateStatus(taskId, data);
    }

    @DeleteMapping("{taskId}")
    @PrintLog
    public IDOnlyResponse deleteTask(@PathVariable long taskId) {
        return taskApiService.deleteTask(taskId);
    }

    @GetMapping("/{id}/debug-info")
    @PrintLog
    public TaskDebugInfoVO getDebugInfo(@PathVariable("id") Long id) {

        ResponseEntity<InferCommonResponse<JSONObject>> responseEntity = inferClient.queryTaskDetailInfo(id);
        JSONObject res;
        if (responseEntity.getBody() == null || responseEntity.getBody().getData() == null) {
            LogUtil.errorf(log, "inferClient return unexpected, response={0}", responseEntity);
            res = new JSONObject().fluentPut("rawMsg", responseEntity.getBody());
        } else {
            res = responseEntity.getBody().getData();
        }

        TaskDebugInfoVO result = new TaskDebugInfoVO();
        result.setTask(res);

        return result;
    }
}
