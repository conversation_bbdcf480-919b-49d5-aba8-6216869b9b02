package co.sandai.zeus.api.payment.webhook;

import co.sandai.zeus.api.payment.webhook.eventhandler.EventHandler;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.log.digest.PrintLog;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.*;
import com.stripe.net.Webhook;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/payment/stripe")
@Slf4j
public class StripeWebhookReceiver {

    @Value("${stripe.endpoint.secret}")
    private String endpointSecret;

    @Resource
    private List<EventHandler> eventHandlers;

    // 生成初始化方法
    private Map<String, EventHandler> eventHandlerMap;

    @PostMapping("/webhook")
    @PrintLog
    public ResponseEntity<?> handle(HttpServletRequest request) {
        String payload = getRequestBody(request);
        String sigHeader = request.getHeader("Stripe-Signature");
        Event event = null;

        try {
            event = Webhook.constructEvent(payload, sigHeader, endpointSecret);
            // 用于参数日志打印
            request.setAttribute("param", event);
        } catch (SignatureVerificationException e) {
            LogUtil.errorf(log, "signature verification failed, request={0}", e, request);
            throw ZeusServiceException.internalError("signature verification failed", e);
        }

        EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
        StripeObject stripeObject;
        if (dataObjectDeserializer.getObject().isPresent()) {
            stripeObject = dataObjectDeserializer.getObject().get();
        } else {
            throw ZeusServiceException.badRequest("Failed to deserialize Stripe event data");
        }

        // Handle the event
        EventHandler eventHandler = eventHandlerMap.get(event.getType());
        if (eventHandler != null) {
            if (eventHandler.needWholeEventData()) {
                eventHandler.handle(stripeObject, event);
            } else {
                eventHandler.handle(stripeObject);
            }
        } else {
            throw ZeusServiceException.badRequest("Unhandled event type: " + event.getType());
        }
        return ResponseEntity.ok().build();
    }

    private String getRequestBody(HttpServletRequest request) {
        try {
            return request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (IOException e) {
            throw new RuntimeException("Failed to read request body", e);
        }
    }

    @PostConstruct
    public void init() {
        eventHandlerMap = eventHandlers.stream().collect(Collectors.toMap(EventHandler::getEventKey, e -> e));
    }
}
