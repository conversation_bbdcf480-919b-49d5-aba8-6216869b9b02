package co.sandai.zeus.api.live.service;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.Data;

/**
 * Class to store all user stream information in one place
 */
@Data
public class LiveStream implements Serializable {
    private static final long serialVersionUID = 1L;

    private long id;

    private List<Long> taskIds = new ArrayList<>();

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String permitId;

    public LiveStream(long id, long userDailyLimitSeconds, String permitId) {
        this.id = id;
        this.permitId = permitId;
        this.startTime = LocalDateTime.now();
        this.endTime = LocalDateTime.now().plusSeconds(userDailyLimitSeconds);
    }

    public List<Long> getTaskIds() {
        return Collections.unmodifiableList(taskIds);
    }

    public void addTaskId(long taskId) {
        this.taskIds.add(taskId);
    }

    public boolean isValid() {
        return LocalDateTime.now().isBefore(this.endTime);
    }
}
