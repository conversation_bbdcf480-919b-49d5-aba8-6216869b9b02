package co.sandai.zeus.api.admin.view;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class GrantMembershipRequest {

    /**
     * 目标用户ID
     */
    @NotNull(message = "User ID is required")
    private Long userId;

    /**
     * 会员计划代码 (plus_plan_monthly, pro_plan_monthly, premium_plan_monthly,
     * plus_plan_yearly, pro_plan_yearly, premium_plan_yearly)
     */
    @NotNull(message = "Plan code is required")
    @Pattern(
            regexp = "^(plus|pro|premium)_plan_(monthly|yearly)$",
            message =
                    "Invalid plan code. Must be one of: plus_plan_monthly, pro_plan_monthly, premium_plan_monthly, plus_plan_yearly, pro_plan_yearly, premium_plan_yearly")
    private String planCode;
}
