package co.sandai.zeus.api.live.service;

import co.sandai.zeus.infra.IDGenerator;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisUserStreamManager {
    @Autowired
    private IDGenerator idGenerator;

    private static final String LIVE_KEY_PREFIX = "zeus:live:";
    private static final String USER_STREAM_PREFIX = LIVE_KEY_PREFIX + "user:stream:";
    private static final String next_available_time = LIVE_KEY_PREFIX + "next_available_time";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${zeus.live.stream-ttl-minutes}")
    private int streamTtlMinutes;

    public LiveStream createStream(long userId, long userDailyLimitSeconds, String permitId) {
        LiveStream stream = new LiveStream(idGenerator.getNextId(), userDailyLimitSeconds, permitId);
        RBucket<LiveStream> streamBucket = redissonClient.getBucket(USER_STREAM_PREFIX + userId);
        streamBucket.set(stream, streamTtlMinutes * 60, TimeUnit.SECONDS);

        RBucket<String> nextAvailableBucket = redissonClient.getBucket(next_available_time);
        nextAvailableBucket.set(TIME_FORMATTER.format(stream.getEndTime()));

        return stream;
    }

    public LocalDateTime getNextAvailableTime() {
        RBucket<String> nextAvailableBucket = redissonClient.getBucket(next_available_time);
        String timeStr = nextAvailableBucket.get();
        if (timeStr == null) {
            return LocalDateTime.now();
        }
        LocalDateTime l = LocalDateTime.parse(timeStr, TIME_FORMATTER);
        if (l.plusMinutes(streamTtlMinutes).isBefore(LocalDateTime.now())) {
            return LocalDateTime.now();
        }
        return l;
    }

    public LiveStream getUserStream(long userId) {
        // Use JsonJacksonCodec to read as JSON
        RBucket<LiveStream> streamBucket = redissonClient.getBucket(USER_STREAM_PREFIX + userId);
        LiveStream stream = streamBucket.isExists() ? streamBucket.get() : null;
        if (stream != null
                && stream.getStartTime().plusMinutes(streamTtlMinutes).isBefore(LocalDateTime.now())) {
            streamBucket.delete();
            return null;
        }
        return stream;
    }

    public LiveStream addTaskToStream(long userId, long taskId) {
        RBucket<LiveStream> streamBucket = redissonClient.getBucket(USER_STREAM_PREFIX + userId);
        if (streamBucket.isExists()) {
            LiveStream stream = streamBucket.get();
            stream.addTaskId(taskId);
            streamBucket.set(stream, streamTtlMinutes * 60, TimeUnit.SECONDS);
        } else {
            // TODO 异常
        }
        return streamBucket.get();
    }

    public void stopStream(long userId, LiveStream stream) {
        // Use JsonJacksonCodec to read and write as JSON
        RBucket<LiveStream> streamBucket = redissonClient.getBucket(USER_STREAM_PREFIX + userId);
        stream.setEndTime(LocalDateTime.now());
        streamBucket.set(stream, streamTtlMinutes * 60, TimeUnit.SECONDS);
        RBucket<String> nextAvailableBucket = redissonClient.getBucket(next_available_time);
        nextAvailableBucket.delete();
    }
}
