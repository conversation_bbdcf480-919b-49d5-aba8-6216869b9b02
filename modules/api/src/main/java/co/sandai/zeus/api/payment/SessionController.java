package co.sandai.zeus.api.payment;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.RedirectException;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.plan.UserPlanService;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.model.PriceModel;
import co.sandai.zeus.domain.user.dao.User;
import com.stripe.exception.StripeException;
import com.stripe.model.checkout.Session;
import com.stripe.param.checkout.SessionCreateParams;
import jakarta.annotation.Resource;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/payment/sessions")
@Slf4j
public class SessionController {

    @Resource
    private UserApiService userApiService;

    @Resource
    private PlanConfigService planService;

    @Value("${zeus.base}")
    private String host;

    private static final String HOME_PAGE = "/app";

    private static final String CHECKOUT_SUCCESS_PAGE = "/payment/sessions/checkout/success";

    private static final String PORTAL_PAGE = "/payment/sessions/portal";

    @Autowired
    private UserPlanService userPlanService;

    @GetMapping("/checkout")
    @PrintLog
    public void createCheckoutSession(@RequestParam(value = "priceId") String priceId) {
        User user = userApiService.getCurrentUser();
        if (Objects.isNull(user)) {
            throw new RedirectException(ErrorCode.Redirect, host + HOME_PAGE);
        }

        String outCustomerId = user.getOutCustomerId();
        if (StringUtils.isBlank(outCustomerId)) {
            try {
                User outerCustomer = userApiService.createOuterCustomer(user);
                outCustomerId = outerCustomer.getOutCustomerId();
            } catch (StripeException e) {
                throw ZeusServiceException.internalError("Failed to create out customer", e)
                        .code(ErrorCode.ThirdPartyError);
            }
        }

        // 获取产品类型
        PriceModel priceModel = planService.getPriceModelByPriceId(priceId);
        if (Objects.isNull(priceModel)) {
            throw ZeusServiceException.badRequest("priceModel not found, priceId: " + priceId);
        }
        PriceModel.ProductTypeEnum productType = priceModel.getProductType();

        // 产品类型决定下单模式
        SessionCreateParams.Mode mode = null;
        if (productType == PriceModel.ProductTypeEnum.CREDIT
                || productType == PriceModel.ProductTypeEnum.PLATFORM_CREDIT) {
            mode = SessionCreateParams.Mode.PAYMENT;
        } else if (productType == PriceModel.ProductTypeEnum.PLAN) {
            mode = SessionCreateParams.Mode.SUBSCRIPTION;
        }

        checkSubscriptionStatus(user, mode);

        SessionCreateParams.Builder paramBuilder = new SessionCreateParams.Builder()
                .setSuccessUrl(host + CHECKOUT_SUCCESS_PAGE + "?session_id={CHECKOUT_SESSION_ID}")
                .setCancelUrl(host + HOME_PAGE + "?paySuccess=false")
                .setMode(mode)
                .setCustomer(outCustomerId)
                .addLineItem(new SessionCreateParams.LineItem.Builder()
                        .setQuantity(1L)
                        .setPrice(priceId)
                        .build());
        if (mode == SessionCreateParams.Mode.PAYMENT) {
            // 对于一次性付款，需要额外指定需要发票
            paramBuilder.setInvoiceCreation(SessionCreateParams.InvoiceCreation.builder()
                    .setEnabled(true)
                    .build());
        }
        SessionCreateParams params = paramBuilder.build();

        log.info(
                "start checkout session, userId: {}, outCustomerId: {}, priceId: {}, session mode: {}",
                user.getId(),
                user.getOutCustomerId(),
                priceId,
                mode);
        Session session;
        try {
            session = Session.create(params);
        } catch (StripeException e) {
            throw ZeusServiceException.internalError("Failed to create checkout session", e)
                    .code(ErrorCode.ThirdPartyError);
        }
        // 重定向到stripe checkout
        throw new RedirectException(ErrorCode.Redirect, session.getUrl());
    }

    private void checkSubscriptionStatus(User user, SessionCreateParams.Mode mode) {
        if (mode != SessionCreateParams.Mode.SUBSCRIPTION) {
            return;
        }
        UserPlanDO userPlanDO = userPlanService.queryUserPlan(user.getId());
        if (userPlanDO != null && !TimeUtil.isExpire(userPlanDO.getExpireTime())) {
            throw new RedirectException(ErrorCode.Redirect, host + PORTAL_PAGE);
        }
    }

    @GetMapping("/portal")
    @PrintLog
    public void createPortalSession() {
        User user = userApiService.getCurrentUser();
        if (StringUtils.isBlank(user.getOutCustomerId())) {
            throw ZeusServiceException.badRequest("current user has not use stripe before");
        }
        com.stripe.param.billingportal.SessionCreateParams params =
                com.stripe.param.billingportal.SessionCreateParams.builder()
                        .setCustomer(user.getOutCustomerId())
                        .setReturnUrl(host + HOME_PAGE)
                        .build();

        try {
            // 这个对象名和checkout的一模一样，是通过包路径区分的
            com.stripe.model.billingportal.Session session = com.stripe.model.billingportal.Session.create(params);
            // 重定向到stripe portal
            throw new RedirectException(ErrorCode.Redirect, session.getUrl());
        } catch (StripeException e) {
            throw ZeusServiceException.internalError("Failed to create portal session", e)
                    .code(ErrorCode.ThirdPartyError);
        }
    }
}
