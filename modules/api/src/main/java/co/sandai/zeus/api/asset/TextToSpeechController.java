package co.sandai.zeus.api.asset;

import co.sandai.zeus.api.asset.request.TextToSpeechRequest;
import co.sandai.zeus.api.asset.view.AssetView;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.voice.service.VoiceService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/assets")
public class TextToSpeechController {

    private final VoiceService voiceService;
    private final UserApiService userApiService;

    public TextToSpeechController(VoiceService voiceService, UserApiService userApiService) {
        this.voiceService = voiceService;
        this.userApiService = userApiService;
    }

    /**
     * Generate audio from text using ElevenLabs TTS
     *
     * @param request Text-to-speech request containing voiceId and text
     * @return Asset view with generated audio
     */
    @PostMapping("/text-to-speech")
    @PrintLog
    public AssetView textToSpeech(@RequestBody TextToSpeechRequest request) {
        User user = userApiService.getCurrentUser();

        // Use default orgId for now

        // Convert text to speech using provider API (with automatic selection based on language)
        Asset asset = voiceService.textToSpeech(
                request.getVoiceId(),
                request.getText(),
                user.getId(),
                null // Default orgId since it's not currently available
                );

        // Return asset view
        return AssetView.fromAsset(asset, false);
    }
}
