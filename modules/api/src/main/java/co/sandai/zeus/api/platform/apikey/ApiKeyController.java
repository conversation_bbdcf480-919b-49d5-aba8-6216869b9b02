package co.sandai.zeus.api.platform.apikey;

import co.sandai.zeus.api.platform.PlatformUserApiService;
import co.sandai.zeus.api.platform.apikey.view.ApiKeyRequest;
import co.sandai.zeus.api.platform.apikey.view.ApiKeyVo;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.domain.platform.apikey.ApiKeyService;
import co.sandai.zeus.domain.platform.apikey.dao.ApiKey;
import co.sandai.zeus.domain.platform.organization.dao.Organization;
import co.sandai.zeus.domain.user.dao.User;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/platform/api-keys")
public class ApiKeyController {

    @Autowired
    private PlatformUserApiService platformUserApiService;

    @Autowired
    private ApiKeyService apiKeyService;

    @Autowired
    private UserApiService userApiService;

    @GetMapping("")
    public List<ApiKeyVo> getApiKeyList() {
        Organization organization = platformUserApiService.getCurrentOrg();
        List<ApiKey> apiKeys = apiKeyService.getOrgApiKeys(organization.getId());
        return apiKeys.stream().map(ApiKeyVo::fromApiKey).toList();
    }

    @PostMapping("")
    public ApiKey createApiKey(@Valid @RequestBody ApiKeyRequest apiKeyRequest) {
        User user = userApiService.getCurrentUser();
        Organization organization = platformUserApiService.getCurrentOrg();
        return apiKeyService.createApiKey(user.getId(), organization.getId(), apiKeyRequest.getName());
    }

    @DeleteMapping("/{id}")
    public IDOnlyResponse deleteApiKey(@PathVariable Long id) {
        apiKeyService.deleteApiKeyById(id);
        return IDOnlyResponse.create(id);
    }
}
