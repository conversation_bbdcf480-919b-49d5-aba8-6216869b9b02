package co.sandai.zeus.api.infer;

import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.infer.InferDispatcherEntry;
import co.sandai.zeus.infer.dto.event.InferEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/infer")
public class InferEventController {

    @Autowired
    private InferDispatcherEntry inferDispatcherEntry;

    @PostMapping("/events")
    @PrintLog
    public void processInferEvent(@RequestBody InferEvent event) {
        inferDispatcherEntry.processInferEvent(event);
    }
}
