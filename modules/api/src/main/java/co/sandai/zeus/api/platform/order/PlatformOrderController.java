package co.sandai.zeus.api.platform.order;

import co.sandai.zeus.api.platform.PlatformUserApiService;
import co.sandai.zeus.api.platform.order.view.PlatformOrderVo;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.payment.dao.OrderMapper;
import co.sandai.zeus.domain.payment.dao.OrderWithUserDO;
import co.sandai.zeus.domain.platform.organization.dao.Organization;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/platform/orders")
public class PlatformOrderController {

    private final PlatformUserApiService platformUserApiService;
    private final OrderMapper orderMapper;

    public PlatformOrderController(PlatformUserApiService platformUserApiService, OrderMapper orderMapper) {
        this.platformUserApiService = platformUserApiService;
        this.orderMapper = orderMapper;
    }

    @GetMapping("")
    public ListResponseByPage<PlatformOrderVo> getPlatformOrders(
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        Organization org = platformUserApiService.getCurrentOrg();
        int total = orderMapper.countOrgOrder(org.getId());
        List<OrderWithUserDO> orders = orderMapper.getOrgOrderList(org.getId(), offset, limit);
        List<PlatformOrderVo> list =
                orders.stream().map(PlatformOrderVo::fromOrder).toList();
        return new ListResponseByPage<>(list, total, offset, limit);
    }
}
