package co.sandai.zeus.api.plan.view;

import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.plan.bo.UserPlanDetailBO;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.PriceModel;
import lombok.Data;

@Data
public class UserPlanVo {

    private String planCode;
    private String price;
    private String originalPrice;
    private String currency;
    private String memberShipName;
    private String intervalType;
    private String creditAmount;

    /**
     * 当为免费计划时，当前字段不返回
     */
    private String expireTime;
    /**
     * 当为免费计划时，当前字段不返回
     * 当订阅状态为  {@link UserPlanDO.SubscriptionStatusEnum} 以外的状态时，也不返回
     */
    private String status;

    public static UserPlanVo convertFromModel(UserPlanDetailBO userPlanDetailBO, Plan plan, PriceModel priceModel) {
        UserPlanVo userPlanVo = new UserPlanVo();
        userPlanVo.setPlanCode(plan.getPlanCode());
        userPlanVo.setPrice(priceModel.getPrice());
        userPlanVo.setOriginalPrice(priceModel.getOriginalPrice());
        userPlanVo.setCurrency(priceModel.getCurrency().getCurrencyCode());
        userPlanVo.setMemberShipName(plan.getMemberShip().getName());
        userPlanVo.setIntervalType(plan.getIntervalTypeEnum().getCode());
        userPlanVo.setCreditAmount(String.valueOf(plan.getMemberShip().getSubscriptionCreditNum()));

        if (userPlanDetailBO != null) {
            userPlanVo.setExpireTime(TimeUtil.format(userPlanDetailBO.getExpireTime()));
            if (userPlanDetailBO.getStatus() != null) {
                userPlanVo.setStatus(userPlanDetailBO.getStatus().name());
            }
        }
        return userPlanVo;
    }
}
