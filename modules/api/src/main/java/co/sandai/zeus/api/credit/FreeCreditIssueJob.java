package co.sandai.zeus.api.credit;

import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.enums.CreditTypeEnum;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处理免费credit的发放和renew
 * 每月一次.
 */
@Component
@Slf4j(topic = "schedulerx")
public class FreeCreditIssueJob extends JavaProcessor {

    @Resource
    private UserService userService;

    @Resource
    private UserCreditService userCreditService;

    @Resource
    private PlanConfigService planConfigService;

    public static final int BATCH_SIZE = 20;

    /**
     * 初期不考虑sharding，采用单机任务
     *
     * @param context
     * @return
     * @throws Exception
     */
    @Override
    @PrintLog
    public ProcessResult process(JobContext context) throws Exception {
        LogUtil.infof(log, "FreeCreditIssueJob process start");
        List<User> users = userService.getUsers(null, BATCH_SIZE);
        while (!users.isEmpty()) {
            String userIdsStr =
                    users.stream().map(User::getId).map(String::valueOf).collect(Collectors.joining(",", "[", "]"));
            LogUtil.infof(log, "FreeCreditIssueJob process, size={0}, userIds={1}", users.size(), userIdsStr);
            // batch更新
            List<Long> userIds = users.stream().map(User::getId).collect(Collectors.toList());
            userCreditService.batchRenewCredit(userIds, CreditTypeEnum.FREE, planConfigService.getFreeCreditAmount());

            Long lowerId = users.stream().mapToLong(User::getId).max().getAsLong();
            users = userService.getUsers(lowerId, BATCH_SIZE);
        }
        return new ProcessResult(true);
    }
}
