package co.sandai.zeus.api.share;

import co.sandai.zeus.api.share.view.ShareGenerationsRequest;
import co.sandai.zeus.api.share.view.ShareVO;
import co.sandai.zeus.api.task.generation.view.GenerationChunksRequestVO;
import co.sandai.zeus.api.task.service.GenerationApiService;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.share.dao.Share;
import co.sandai.zeus.domain.share.service.ShareService;
import co.sandai.zeus.domain.user.dao.User;
import com.alibaba.fastjson.JSON;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/share")
public class ShareController {

    private final ShareService shareService;
    private final UserApiService userApiService;
    private final AssetService assetService;
    private final GenerationApiService generationApiService;

    public ShareController(
            ShareService shareService,
            UserApiService userApiService,
            AssetService assetService,
            GenerationApiService generationApiService) {
        this.shareService = shareService;
        this.userApiService = userApiService;
        this.assetService = assetService;
        this.generationApiService = generationApiService;
    }

    @PostMapping("tasks/{taskId}")
    @PrintLog
    public Share shareTask(@PathVariable long taskId) {
        User user = userApiService.getCurrentUser();
        return shareService.shareTaskByTaskId(taskId, user.getId());
    }

    @DeleteMapping("tasks/{taskId}")
    @PrintLog
    public IDOnlyResponse cancelShare(@PathVariable long taskId) {
        shareService.cancelShare(taskId);
        return IDOnlyResponse.create(taskId);
    }

    @PostMapping("")
    @PrintLog
    public ShareVO shareGeneration(@RequestBody @Valid ShareGenerationsRequest data) {
        User user = userApiService.getCurrentUser();
        String content = JSON.toJSONString(data);
        List<Long> relatedGenerationIds = new ArrayList<>();
        if (Objects.nonNull(data.getChunks()) && !data.getChunks().isEmpty()) {
            for (GenerationChunksRequestVO chunk : data.getChunks()) {
                relatedGenerationIds.add(chunk.getGenerationId());
            }
        }
        if (Objects.nonNull(data.getAssetId()) && data.getAssetId() > 0) {
            Asset asset = assetService.getAssetById(data.getAssetId());
            if (Objects.isNull(asset)) {
                throw ZeusServiceException.notFound("asset not found");
            }
            if (asset.getTaskId() > 0) {
                relatedGenerationIds.add(asset.getTaskId());
            }
        }
        Share share = shareService.shareGenerations(content, user.getId(), relatedGenerationIds);
        return ShareVO.fromShare(share);
    }

    @PostMapping("{shareId}")
    @PrintLog
    public ShareVO getGenerationShare(@PathVariable long shareId) {
        Share share = shareService.getShareById(shareId);
        if (Objects.isNull(share)) {
            throw ZeusServiceException.notFound("share not found");
        }
        return ShareVO.fromShare(share);
    }

    @GetMapping("{shareId}/preview")
    @PrintLog
    public ResponseEntity<String> getGenerationSharePreview(@PathVariable long shareId) {
        Share share = shareService.getShareById(shareId);
        if (Objects.isNull(share)) {
            throw ZeusServiceException.notFound("share not found");
        }
        String shareContent = share.getContent();
        if (Objects.isNull(shareContent)) {
            throw ZeusServiceException.notFound("share content not found");
        }
        ShareGenerationsRequest generationsRequest =
                JSON.parseObject(share.getContent(), ShareGenerationsRequest.class);
        String m3u8Content = generationApiService.getGenerationPreviewM3u8Content(generationsRequest.getChunks());
        return ResponseEntity.ok()
                .header("Content-Type", "application/vnd.apple.mpegurl")
                .body(m3u8Content);
    }
}
