package co.sandai.zeus.api.task;

import co.sandai.zeus.api.task.generation.view.PromptEnhancementRequest;
import co.sandai.zeus.api.task.generation.view.PromptEnhancementResponse;
import co.sandai.zeus.api.task.generation.view.PromptSuggestion;
import co.sandai.zeus.api.task.generation.view.PromptSuggestionRequest;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.common.CropArea;
import co.sandai.zeus.infra.prompt.PromptService;
import co.sandai.zeus.infra.prompt.dto.PromptEnhancementResultDTO;
import co.sandai.zeus.infra.prompt.dto.PromptSuggestionDTO;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/prompt")
public class PromptController {
    @Autowired
    private PromptService promptService;

    @Autowired
    private AssetService assetService;

    @Autowired
    private UserApiService userApiService;

    @GetMapping("/versions")
    @PrintLog
    public ResponseEntity<List<String>> listEnhancementTypes() {
        List<String> versions = promptService.listEnhancementTypes();
        return ResponseEntity.ok(versions);
    }

    /**
     * Get prompt suggestions based on user input and/or image.
     *
     * @param request The suggestion request containing optional asset ID, prompt,
     *                suggestion count, and enhancement flag
     * @return A list of prompt suggestions
     */
    @PostMapping("/suggestions")
    @PrintLog
    public ResponseEntity<List<PromptSuggestion>> getSuggestions(@RequestBody PromptSuggestionRequest request) {
        // Set default values
        int suggestionCnt = request.getCnt() != null ? request.getCnt() : 3;
        String enhancementType = request.getEnhancementType() != null ? request.getEnhancementType() : "";
        String suggestionType = request.getSuggestionType() != null ? request.getSuggestionType() : "";
        long userId = userApiService.getCurrentUserId();

        // Resolve asset URL and type if an image ID is provided
        String prefixUrl = null;
        String prefixType = null;
        Asset asset = null;
        boolean needCrop = false;
        if (request.getAssetId() != null && request.getAssetId() > 0) {
            try {
                asset = assetService.getAssetById(request.getAssetId());
                if (asset != null) {
                    prefixType = asset.getMediaType().toString();
                    prefixUrl = assetService.getAssetOssPublicUrl(asset, 5, true);

                    // 进行crop
                    if (request.getCropArea() != null) {
                        request.getCropArea().checkParams(asset.getWidth(), asset.getHeight());
                        needCrop = request.getCropArea().needCrop(asset.getWidth(), asset.getHeight());
                    }
                }
            } catch (Exception e) {
                log.error("Failed to resolve asset ID: " + request.getAssetId(), e);
                throw new RuntimeException("Failed to resolve asset ID: " + request.getAssetId(), e);
            }
        }

        if (asset == null) {
            suggestionType = "t2v_simple_storyline_gpt";
            enhancementType = "t2v_simple_storylineenhanced_gpt";
        } else if (asset.getMediaType().isImage()) {
            suggestionType = "i2v_chain_sylviachain20250318_gpt";
        } else if (asset.getMediaType().isVideo()) {
            suggestionType = "v2v_chain_sylviachain20250318_gpt";
        }

        // Call the service to get suggestions
        List<PromptSuggestionDTO> suggestionDTOs = promptService.getSuggestions(
                request.getPrompt(),
                prefixUrl,
                prefixType,
                suggestionCnt,
                request.isEnableEnhancement(),
                enhancementType,
                suggestionType,
                needCrop ? CropArea.convertToMap(request.getCropArea()) : null);

        // Convert DTOs to view objects
        List<PromptSuggestion> suggestions = suggestionDTOs.stream()
                .map(dto -> PromptSuggestion.builder()
                        .prompt(dto.getPrompt())
                        .enhancedPrompt(dto.getEnhancedPrompt())
                        .build())
                .collect(Collectors.toList());

        return ResponseEntity.ok(suggestions);
    }

    /**
     * Enhance a prompt based on user input and/or image.
     *
     * @param request The enhancement request containing optional asset ID, prompt,
     *                and enhancement type
     * @return The enhanced prompt result
     */
    @PostMapping("/enhance")
    @PrintLog
    public ResponseEntity<PromptEnhancementResponse> enhancePrompt(@RequestBody PromptEnhancementRequest request) {
        // Resolve asset URL and type if an asset ID is provided
        String prefixUrl = null;
        String prefixType = null;

        if (request.getAssetId() != null && request.getAssetId() > 0) {
            try {
                // Get asset directly
                Asset asset = assetService.getAssetById(request.getAssetId());
                if (asset != null) {
                    prefixType = asset.getMediaType().toString();
                    prefixUrl = assetService.getAssetOssPublicUrl(asset, 5, true);
                }
            } catch (Exception e) {
                log.error("Failed to resolve asset ID: " + request.getAssetId(), e);
                throw new RuntimeException("Failed to resolve asset ID: " + request.getAssetId(), e);
            }
        }

        // Call the service to enhance the prompt
        PromptEnhancementResultDTO resultDTO = promptService.enhancePrompt(
                request.getPrompt(),
                prefixUrl,
                prefixType,
                false, // genFirstFrame hardcoded to false
                request.getEnhancementType());

        // Convert DTO to view object
        PromptEnhancementResponse response = PromptEnhancementResponse.builder()
                .generatedPrompt(resultDTO.getGeneratedPrompt())
                .generatedImagePrompt(resultDTO.getGeneratedImagePrompt())
                .build();

        return ResponseEntity.ok(response);
    }
}
