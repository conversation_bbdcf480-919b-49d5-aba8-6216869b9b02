package co.sandai.zeus.api.task.service;

import co.sandai.zeus.api.task.generation.view.ConditionVO;
import co.sandai.zeus.api.task.generation.view.ExtraArgs;
import co.sandai.zeus.api.task.generation.view.GenerationChunksRequestVO;
import co.sandai.zeus.api.task.generation.view.GenerationDetailResponseVO;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.helper.M3U8Helper;
import co.sandai.zeus.common.utils.StreamUtil;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.dao.UserCreditTransactionDO;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskChunkDO;
import co.sandai.zeus.domain.task.dao.TaskChunkStatus;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.dao.TaskType;
import co.sandai.zeus.domain.task.dao.mapper.TaskChunkMapper;
import co.sandai.zeus.domain.task.dao.mapper.TaskMapper;
import co.sandai.zeus.domain.task.service.GenerationService;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.infra.infer.InferClient;
import co.sandai.zeus.infra.infer.dto.InferJob;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.utils.StringUtils;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class GenerationApiService {

    private final TaskService taskService;
    private final AssetService assetService;
    private final InferClient inferClient;
    private final TaskMapper taskMapper;
    private final TaskChunkMapper taskChunkMapper;
    private final GenerationService generationService;
    private final UserCreditService userCreditService;
    private final UserApiService userApiService;

    public GenerationApiService(
            TaskService taskService,
            AssetService assetService,
            InferClient inferClient,
            TaskMapper taskMapper,
            TaskChunkMapper taskChunkMapper,
            GenerationService generationService,
            UserCreditService userCreditService,
            UserApiService userApiService) {
        this.taskService = taskService;
        this.assetService = assetService;
        this.inferClient = inferClient;
        this.taskMapper = taskMapper;
        this.taskChunkMapper = taskChunkMapper;
        this.generationService = generationService;
        this.userCreditService = userCreditService;
        this.userApiService = userApiService;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void submitTask(
            String enhancementType,
            Task task,
            List<Asset> sourceAssets,
            List<TaskChunkDO> outputChunks,
            String queue,
            boolean deductCredits)
            throws IOException {
        if (deductCredits) {
            // 扣减credits
            int creditAmount = GenerationService.creditStrategy(task, outputChunks);
            userCreditService.deductCredit(
                    userApiService.getCurrentUserId(),
                    null,
                    creditAmount,
                    UserCreditTransactionDO.ReasonTypeEnum.SPENT,
                    UserCreditTransactionDO.ReasonSubTypeEnum.VIDEO_GENERATION);
        }

        if (outputChunks.size() == 1) {
            task.setPrompt(outputChunks.getFirst().getPrompt());
            task.setDuration(outputChunks.getFirst().getDuration());
        } else {
            task.setPrompt("");
        }

        task = generationService.createTaskAndChunk(task, outputChunks);

        // 现在还没有用到source里的prompt
        taskService.submitTaskAsAthenaJob(task, sourceAssets, outputChunks, enhancementType, queue);
    }

    public List<GenerationChunksRequestVO> parseGenerationPreviewRequest(String request) {
        String decodedRequests = new String(Base64.getDecoder().decode(request), StandardCharsets.UTF_8);
        return JSON.parseArray(decodedRequests, GenerationChunksRequestVO.class);
    }

    public String getGenerationPreviewM3u8Content(List<GenerationChunksRequestVO> requests) {
        GenerationApiService.GenerationAssetsResult result = collectGenerationAssets(requests);
        List<String> chunkUrls = assetService.getAssetPublicUrls(result.assets(), 60 * 24); // 1 day

        // 提取每个资产的持续时间到列表中
        List<Float> durations = result.assets().stream().map(Asset::getDuration).collect(Collectors.toList());

        // 使用新的生成方法，传递持续时间列表和对应的URL
        // 默认持续时间为1秒，当持续时间为0或null时使用
        byte[] data = M3U8Helper.generateM3U8File(
                durations,
                chunkUrls,
                1.0f, // 默认持续时间为1秒
                result.isComplete());
        return new String(data, StandardCharsets.UTF_8);
    }

    public GenerationDetailResponseVO getGenerationDetail(long id, boolean chunks) {
        // Fetch generation details from TaskService
        Task task = taskService.getTaskById(id);
        if (task == null) {
            throw ZeusServiceException.notFound("Task not found");
        }
        boolean isSafe = task.isModerationPassed();
        GenerationDetailResponseVO response = new GenerationDetailResponseVO();
        response.setId(id);
        response.setModel(task.getModel());
        response.setStatus(task.getStatus() == null ? null : task.getStatus().name());
        response.setResultVideoId(task.getResultVideoId());
        response.setModerationPassed(isSafe);

        // Set extraArgs from the Task entity
        ExtraArgs extraArgs = new ExtraArgs();
        extraArgs.setEnablePromptEnhancement(task.isEnablePromptEnhancement());
        extraArgs.setTSchedulerFunc(task.getTSchedulerFunc());
        extraArgs.setTSchedulerArgs(task.getTSchedulerArgs());
        extraArgs.setCropArea(task.getCropArea());
        extraArgs.setExtraInferArgs(task.getExtraInferArgs());
        response.setExtraArgs(extraArgs);

        Asset resultVideoAsset = null;
        if (task.getResultVideoId() > 0) {
            resultVideoAsset = assetService.getAssetById(task.getResultVideoId());
            response.setWidth(resultVideoAsset.getWidth());
            response.setHeight(resultVideoAsset.getHeight());
            response.setDuration(resultVideoAsset.getDuration());
        }

        if (Objects.nonNull(resultVideoAsset)) {
            response.setResultVideoModerationStatus(resultVideoAsset.videoModerationStatus);
            if (isSafe) {
                response.setResultVideoURL(assetService.getAssetPublicUrl(resultVideoAsset, 60));
            }
        }

        if (task.getResultVideoPosterId() > 0 && isSafe) {
            response.setResultPosterURL(assetService.getAssetPublicUrlById(task.getResultVideoPosterId(), 60));
        }

        // Set create time regardless of task status
        response.setCreateTime(ZonedDateTime.ofInstant(task.getCreateTime().toInstant(), ZoneId.of("UTC")));

        // Actively sync task data with InferJob data if task is not in a final state
        InferJob inferJob = null;

        // Set source based on task type
        ConditionVO source = new ConditionVO();
        if (task.getSourceImageId() > 0) {
            source.setType("image");
            source.setContent(String.valueOf(task.getSourceImageId()));
        } else if (task.getSourceVideoId() > 0) {
            source.setType("video");
            source.setContent(String.valueOf(task.getSourceVideoId()));
        }
        response.setSource(source);
        if (true) {
            //        if (task.getStatus() != TaskStatus.Success
            //                && task.getStatus() != TaskStatus.Fail
            //                && task.getStatus() != TaskStatus.Canceled) {
            //            // Set default values for estimate times only for incomplete tasks
            //            ZonedDateTime defaultTime = ZonedDateTime.now(ZoneId.of("UTC")).plusHours(1);
            //            response.setEstimateStartTime(defaultTime);
            //            response.setEstimateCompleteTime(defaultTime.plusHours(1));

            try {
                inferJob = inferClient.getInferJob(task.getId());
                if (inferJob != null) {
                    // Set createTime from inferJob if available
                    ZonedDateTime createdAt = inferJob.getCreatedAt();
                    if (createdAt != null) {
                        response.setCreateTime(createdAt);
                    }

                    // Extract estimate times directly from InferJob using encapsulated methods
                    ZonedDateTime estimateStartTime = inferJob.getEstimateStartTime();
                    if (estimateStartTime != null) {
                        response.setEstimateStartTime(estimateStartTime);
                    }

                    ZonedDateTime estimateCompleteTime = inferJob.getEstimateCompleteTime();
                    if (estimateCompleteTime != null) {
                        response.setEstimateCompleteTime(estimateCompleteTime);
                    }
                }
            } catch (Exception e) {
                log.error("Error getting infer job details for taskId: " + task.getId(), e);
                // Continue processing without infer job details
            }
        }

        if (chunks) {
            response.setChunks(new ArrayList<>());
            List<TaskChunkDO> taskChunks = taskService.getTaskChunkByTaskId(id);
            if (taskChunks == null || taskChunks.isEmpty()) {
                GenerationDetailResponseVO.Chunk chunk = new GenerationDetailResponseVO.Chunk();
                chunk.setDuration(task.getDuration());
                chunk.setId(task.getId());
                chunk.setStatus(task.getStatus().name());
                chunk.setPrompt(task.getPrompt());
                List<ConditionVO> conditions = new ArrayList<>();

                // Add text condition for all task types
                ConditionVO textCondition = new ConditionVO();
                textCondition.setType("text");
                textCondition.setContent(task.getPrompt());
                conditions.add(textCondition);

                // For A2V tasks, add audio condition
                if (task.getType() == TaskType.A2V) {
                    ConditionVO audioCondition = new ConditionVO();
                    audioCondition.setType("audio");
                    String content = String.valueOf(task.getSourceAudioId());
                    audioCondition.setContent(content);
                    conditions.add(audioCondition);
                }

                chunk.setConditions(conditions);

                if (task.isEnablePromptEnhancement() && StringUtils.isEmpty(chunk.getPrompt())) {
                    if (inferJob == null) {
                        inferJob = inferClient.getInferJob(task.getId());
                    }
                    if (inferJob != null) {
                        task.setEnhancedPrompt(inferJob.getEnhancedPrompt());
                        taskMapper.updateEnhancedPrompt(task.getId(), inferJob.getEnhancedPrompt());
                    }
                }
                chunk.setEnhancedPrompt(task.getEnhancedPrompt());

                response.getChunks().add(chunk);
            } else {
                // Check if any chunks need enhanced prompts before making API call
                boolean needToFetchInferJob = false;
                if (task.isEnablePromptEnhancement() && inferJob == null) {
                    // Only fetch InferJob if there's at least one chunk with a null enhancedPrompt
                    for (TaskChunkDO chunk : taskChunks) {
                        if (chunk.getEnhancedPrompt() == null) {
                            needToFetchInferJob = true;
                            break;
                        }
                    }

                    // Only make the API call if necessary (fallback mechanism)
                    if (needToFetchInferJob) {
                        inferJob = inferClient.getInferJob(task.getId());

                        // If we retrieved inferJob successfully, update all chunks with missing enhanced prompts
                        if (inferJob != null
                                && inferJob.getSteps() != null
                                && !inferJob.getSteps().isEmpty()) {
                            for (TaskChunkDO chunk : taskChunks) {
                                if (chunk.getEnhancedPrompt() == null
                                        && chunk.getIndex() != null
                                        && chunk.getIndex()
                                                < inferJob.getSteps().size()) {
                                    InferJob.Step inferStep =
                                            inferJob.getSteps().get(chunk.getIndex());
                                    if (inferStep != null && StringUtils.isEmpty(chunk.getPrompt())) {
                                        chunk.setEnhancedPrompt(inferStep.getEnhancedPrompt());
                                        // Also persist to database
                                        taskChunkMapper.updateEnhancedPrompt(
                                                chunk.getId(), inferStep.getEnhancedPrompt());
                                    }
                                }
                            }
                        }
                    }
                }

                // Since we've already updated all TaskChunkDO objects with enhanced prompts above,
                // we can simply map them to response chunks without additional inferJob checks
                StreamUtil.of(taskChunks)
                        .map(step -> {
                            GenerationDetailResponseVO.Chunk chunk = new GenerationDetailResponseVO.Chunk();
                            chunk.setDuration(step.getDuration());
                            chunk.setId(step.getId());
                            chunk.setStatus(step.getStatus().name());
                            chunk.setPrompt(step.getPrompt());
                            chunk.setEnhancedPrompt(step.getEnhancedPrompt());
                            return chunk;
                        })
                        .forEach(response.getChunks()::add);
            }
        }
        return response;
    }

    public record GenerationAssetsResult(List<Asset> assets, boolean isComplete) {}

    public GenerationAssetsResult collectGenerationAssets(List<GenerationChunksRequestVO> requests) {
        List<Asset> allAssets = new ArrayList<>();
        boolean end = true;
        List<Long> generationIds = requests.stream()
                .map(GenerationChunksRequestVO::getGenerationId)
                .toList();
        if (!generationIds.isEmpty() && !taskService.allSafe(generationIds)) {
            throw ZeusServiceException.forbidden("Contains some unsafe videos.");
        }
        // for (GenerationChunksRequestVO request : requests) {
        for (int i = 0; i < requests.size(); i++) {
            GenerationChunksRequestVO request = requests.get(i);
            long taskId = request.getGenerationId();
            Task task = taskService.getTaskById(taskId);
            if (i == 0) {
                if (TaskType.ExtendDuration.equals(task.getType()) && task.getSourceVideoId() != 0) {
                    Asset sourceVideoAsset = assetService.getAssetById(task.getSourceVideoId());
                    switch (sourceVideoAsset.getSource()) {
                        case Upload -> {
                            log.info(
                                    "[collectGenerationAssets] [ExtendDuration] TaskId={} Source=Upload, isEnableInputVideoToTs={}",
                                    taskId,
                                    task.getExtraInferArgs().isEnableInputVideoToTs());
                            if (task.getExtraInferArgs().isEnableInputVideoToTs()) {
                                Asset inputTsAsset = assetService.getTsOfInputVideoByTaskId(taskId);
                                log.info(
                                        "[collectGenerationAssets] getInputTsByTaskId({}) -> {} assets",
                                        taskId,
                                        inputTsAsset != null);
                                if (inputTsAsset != null) {
                                    allAssets.add(inputTsAsset);
                                }
                            }
                        }
                        case Generate -> {
                            log.info(
                                    "[collectGenerationAssets] [ExtendDuration] TaskId={} Source=Generate, sourceVideoId={}",
                                    taskId,
                                    task.getSourceVideoId());
                            Task sourceTask = taskService.getTaskById(sourceVideoAsset.getTaskId());
                            if (sourceTask != null) {
                                List<Asset> continueChunkAssets =
                                        assetService.getContinueChunkAssetByTaskId(sourceTask.getId());
                                log.info(
                                        "[collectGenerationAssets] getContinueChunkAssetByTaskId({}) -> {} assets",
                                        sourceTask.getId(),
                                        continueChunkAssets.size());
                                if (continueChunkAssets.size() > 0) {
                                    allAssets.addAll(continueChunkAssets);
                                }
                            } else {
                                log.warn(
                                        "[collectGenerationAssets] sourceTask is null for sourceVideoId={}",
                                        task.getSourceVideoId());
                            }
                        }
                        default -> {
                            log.warn(
                                    "[collectGenerationAssets] [ExtendDuration] TaskId={} Source={} (not handled)",
                                    taskId,
                                    sourceVideoAsset.getSource());
                        }
                    }
                }
            }

            if (CollectionUtils.isEmpty(request.getChunkIds())) {
                List<Asset> assets = assetService.getContinueChunkAssetByTaskId(taskId);
                allAssets.addAll(assets);
                // Check if all tasks are completed
                if (end) {
                    end = TaskStatus.Success.equals(task.getStatus());
                }
            } else {
                for (Long chunkId : request.getChunkIds()) {
                    List<Asset> assets = assetService.getContinueChunkAssetByTaskChunk(chunkId);
                    allAssets.addAll(assets);
                }

                // Check if all task chunks are completed
                if (end) {
                    List<TaskChunkDO> taskChunks = taskService.getTaskChunkByIds(request.getChunkIds());
                    for (TaskChunkDO chunk : taskChunks) {
                        end = end && TaskChunkStatus.Success.equals(chunk.getStatus());
                        end = end && TaskChunkStatus.Success.equals(chunk.getStatus());
                    }
                }
            }
        }
        return new GenerationAssetsResult(allAssets, end);
    }
}
