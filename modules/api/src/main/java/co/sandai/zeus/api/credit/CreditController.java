package co.sandai.zeus.api.credit;

import static co.sandai.zeus.domain.credit.enums.CreditTypeEnum.BONUS;
import static co.sandai.zeus.domain.credit.enums.CreditTypeEnum.OPERATION;

import co.sandai.zeus.api.credit.service.CreditApiService;
import co.sandai.zeus.api.credit.view.CreditPackagePageVo;
import co.sandai.zeus.api.credit.view.UserCreditTransactionPageVo;
import co.sandai.zeus.api.credit.view.UserCreditVo;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.config.CreditConfigService;
import co.sandai.zeus.domain.credit.config.CreditPackage;
import co.sandai.zeus.domain.credit.dao.UserCreditTransactionDO;
import co.sandai.zeus.domain.credit.enums.CreditTypeEnum;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/credits")
public class CreditController {

    @Resource
    private CreditConfigService creditConfigService;

    @Resource
    private PlanConfigService planConfigService;

    @Resource
    private UserCreditService userCreditService;

    @Resource
    private UserApiService userApiService;

    @Resource
    private UserService userService;

    @Autowired
    private CreditApiService creditApiService;

    @GetMapping("/package")
    @PrintLog
    public CreditPackagePageVo getCredit() {

        List<CreditPackage> creditPackageList = creditConfigService.getCreditPackageList();
        List<CreditPackagePageVo.CreditPackageVo> list = creditPackageList.stream()
                .map(x -> CreditPackagePageVo.CreditPackageVo.convertFromModel(
                        x, planConfigService.getPriceModelOfCredit(x.getId())))
                .collect(Collectors.toList());
        CreditPackagePageVo creditPackagePageVo = new CreditPackagePageVo();
        creditPackagePageVo.setCreditPackageList(list);
        return creditPackagePageVo;
    }

    @GetMapping("/user")
    @PrintLog
    public UserCreditVo getUserCredit() {
        return creditApiService.getUserCredit();
    }

    @GetMapping("/transaction")
    @PrintLog
    public UserCreditTransactionPageVo getUserCreditTransaction(
            @RequestParam(value = "continuationToken", required = false) Long continuationToken,
            @RequestParam(value = "limit") Integer limit) {
        if (limit == null) {
            // 默认20条
            limit = 20;
        }
        User currentUser = userApiService.getCurrentUser();

        List<UserCreditTransactionDO> userCreditTransactionDOs =
                userCreditService.queryUserCreditTransaction(currentUser.getId(), continuationToken, limit);
        List<UserCreditTransactionPageVo.UserCreditTransactionVo> userCreditTransactionVos =
                userCreditTransactionDOs.stream()
                        .map(UserCreditTransactionPageVo.UserCreditTransactionVo::convertFromModel)
                        .collect(Collectors.toList());
        Optional<Long> optionalUpperId = userCreditTransactionDOs.stream()
                .map(UserCreditTransactionDO::getId)
                .max(Long::compareTo);

        UserCreditTransactionPageVo pageVo = new UserCreditTransactionPageVo();
        pageVo.setList(userCreditTransactionVos);
        optionalUpperId.ifPresent(aLong -> pageVo.setNextContinuationToken(String.valueOf(aLong)));
        return pageVo;
    }

    @PostMapping("/operate")
    @PrintLog
    public void issueCredit(
            @RequestParam(value = "userIds") List<String> userIds,
            @RequestParam(value = "amount") Integer amount,
            @RequestParam(value = "creditType") String creditType) {
        User currentUser = userApiService.getCurrentUser();
        if (!userService.isInAdminWhiteList(currentUser)) {
            throw ZeusServiceException.forbidden("no permission");
        }

        CreditTypeEnum creditTypeEnum = CreditTypeEnum.valueOf(creditType);
        if (creditTypeEnum == null || !Arrays.asList(OPERATION, BONUS).contains(creditTypeEnum)) {
            throw ZeusServiceException.badRequest("invalid credit type");
        }

        // todo panhong 2024/12/20 做个简单的防重，查下15分钟内有没有重复发放同金额、同userId的什么的
        List<Long> userIdLongs = userIds.stream().map(Long::parseLong).collect(Collectors.toList());
        userCreditService.issueOperateCredit(userIdLongs, amount, creditTypeEnum);
    }
}
