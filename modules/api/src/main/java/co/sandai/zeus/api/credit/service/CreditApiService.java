package co.sandai.zeus.api.credit.service;

import co.sandai.zeus.api.credit.view.UserCreditVo;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.dao.UserCreditDO;
import co.sandai.zeus.domain.plan.UserPlanService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.user.dao.User;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CreditApiService {

    @Resource
    private UserPlanService userPlanService;

    @Resource
    private UserCreditService userCreditService;

    @Autowired
    private UserApiService userApiService;

    @Transactional(isolation = Isolation.REPEATABLE_READ, rollbackFor = Throwable.class)
    public void renewYearlyPlanAndCredits(List<UserPlanDO> userPlans) {
        userPlanService.batchUpdateNextRenewTime(userPlans);
        // batch更新. 年计划应该很少，其实可以一条条执行，逻辑简单点
        userCreditService.batchRenewCredit(userPlans);
    }

    public UserCreditVo populateUserCreditVo(List<UserCreditDO> userCreditDOs) {
        if (userCreditDOs == null || userCreditDOs.isEmpty()) {
            return new UserCreditVo().setCreditAmount("0").setCreditDetailList(Collections.emptyList());
        }

        long sum = userCreditDOs.stream().mapToLong(UserCreditDO::getAmount).sum();
        List<UserCreditVo.CreditDetailVo> creditDetailList = userCreditDOs.stream()
                .map(UserCreditVo.CreditDetailVo::convertFromModel)
                .collect(Collectors.toList());

        UserCreditVo userCreditVo = new UserCreditVo();
        userCreditVo.setCreditAmount(String.valueOf(sum));
        userCreditVo.setCreditDetailList(creditDetailList);

        return userCreditVo;
    }

    public UserCreditVo getUserCredit() {
        User currentUser = userApiService.getCurrentUser();
        List<UserCreditDO> userCreditDOs = userCreditService.queryUserCredit(currentUser.getId(), null);
        return populateUserCreditVo(userCreditDOs);
    }
}
