package co.sandai.zeus.api.favorite;

import co.sandai.zeus.api.task.view.TaskView;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.favorite.service.TaskFavoriteService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.user.dao.User;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class FavoriteApiService {

    private final TaskFavoriteService taskFavoriteService;
    private final UserApiService userApiService;

    public FavoriteApiService(TaskFavoriteService taskFavoriteService, UserApiService userApiService) {
        this.taskFavoriteService = taskFavoriteService;
        this.userApiService = userApiService;
    }

    public ListResponseByPage<TaskView> favoriteTasks(int offset, int limit) {
        User user = userApiService.getCurrentUser();
        List<Task> tasks = taskFavoriteService.getFavoriteTasks(user.getId(), offset, limit);
        List<TaskView> taskViews = tasks.stream().map(TaskView::fromTask).toList();
        return ListResponseByPage.<TaskView>builder().total(0).list(taskViews).build();
    }
}
