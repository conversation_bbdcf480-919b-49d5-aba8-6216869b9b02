package co.sandai.zeus.api.live.view;

import co.sandai.zeus.api.task.generation.view.ConditionVO;
import co.sandai.zeus.domain.common.CropArea;
import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import jakarta.annotation.Nullable;
import java.util.List;
import lombok.Data;

/**
 * Request model for live generation.
 * If this is the first request, streamId will be null.
 * For subsequent requests, client should provide the streamId.
 */
@Data
public class LiveGenerateRequest {
    private Long streamId; // Null for first request, provided by client for subsequent requests
    private List<Step> steps;

    @Data
    public static class Step {
        private String prompt;
        private float duration;
    }

    @Nullable
    private ConditionVO source; // 只需要首次调用的时候需要，其他不需要

    public String model = "mvp-internal-test";

    public Boolean enablePromptEnhancement = false;
    public Boolean enableAutoGenFirstFrame = false;
    public String aspectRatio;
    public Long seed = 0L;
    public String tSchedulerFunc = "";
    public String tSchedulerArgs = "";
    public CropArea cropArea = null;
    public TaskExtraInferArgs extraInferArgs = null;
}
