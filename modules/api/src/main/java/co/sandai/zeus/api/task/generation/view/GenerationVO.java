package co.sandai.zeus.api.task.generation.view;

import co.sandai.zeus.config.SpringContext;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetVideoModerationStatus;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.infra.infer.InferClient;
import co.sandai.zeus.infra.infer.dto.InferJob;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class GenerationVO {
    private long id;
    private String status;
    private Integer progress;
    private List<Chunk> chunks;
    private ResultVO result = new ResultVO();
    private boolean isModerationPassed;
    private ZonedDateTime createTime;
    private ZonedDateTime estimateCompleteTime;
    private ZonedDateTime estimateStartTime;

    @Data
    public static class ResultVO {
        private String videoURL;
        private String posterURL;
        // private ResultAsset asset;
        private AssetVideoModerationStatus videoModerationStatus;
        private long videoId;
        private int width;
        private int height;
        private float duration;
    }

    @Data
    public static class Chunk {
        private float duration;
        private String status;
        private long id;
        private List<ConditionVO> conditions;
        private String prompt;
        private String enhancedPrompt;
    }

    /**
     * Convert a Task entity to a GenerationVO object
     * Similar to getGenerationDetail in GenerationApiService but for list view
     * @param task The Task entity to convert
     * @return A new GenerationVO object with data from the Task
     */
    public static GenerationVO fromTask(Task task) {
        if (task == null) {
            return null;
        }

        AssetService assetService = SpringContext.getBean(AssetService.class);
        int expiresInMinutes = 60;
        boolean isSafe = task.isModerationPassed();

        GenerationVO vo = new GenerationVO();
        vo.setId(task.getId());
        vo.setStatus(task.getStatus() != null ? task.getStatus().name() : null);
        vo.getResult().setVideoId(task.getResultVideoId());
        vo.setModerationPassed(isSafe);

        // Set create time regardless of task status
        if (task.getCreateTime() != null) {
            // Directly convert to ZonedDateTime without zone change since it's already in UTC
            vo.setCreateTime(task.getCreateTime().toInstant().atZone(ZoneId.of("UTC")));
        }

        // Get asset information if available
        Asset resultVideoAsset = null;
        if (task.getResultVideoId() > 0) {
            resultVideoAsset = assetService.getAssetById(task.getResultVideoId());
        }

        if (resultVideoAsset != null) {
            vo.getResult().setVideoModerationStatus(resultVideoAsset.videoModerationStatus);
            if (isSafe) {
                vo.getResult().setVideoURL(assetService.getAssetPublicUrl(resultVideoAsset, expiresInMinutes));
            }
            vo.getResult().setDuration(resultVideoAsset.getDuration());
            vo.getResult().setWidth(resultVideoAsset.getWidth());
            vo.getResult().setHeight(resultVideoAsset.getHeight());
        }

        if (task.getResultVideoPosterId() > 0 && isSafe) {
            vo.getResult()
                    .setPosterURL(assetService.getAssetPublicUrlById(task.getResultVideoPosterId(), expiresInMinutes));
        }

        // Calculate progress based on completed chunk count if available
        if (task.getChunkCount() > 0) {
            vo.setProgress((int) (task.getCompletedChunkCount() * 100.0 / task.getChunkCount()));
        }

        // For in-progress tasks, fetch estimate times from InferClient
        if (task.getStatus() == TaskStatus.Pending || task.getStatus() == TaskStatus.Running) {
            // Get InferClient instance to fetch job details
            InferClient inferClient = SpringContext.getBean(InferClient.class);
            try {
                // Fetch job details from InferClient
                InferJob inferJob = inferClient.getInferJob(task.getId());
                if (inferJob != null) {
                    // Set createTime from inferJob if available
                    ZonedDateTime createdAt = inferJob.getCreatedAt();
                    if (createdAt != null) {
                        vo.setCreateTime(createdAt);
                    }

                    // Extract estimate times directly from InferJob
                    ZonedDateTime estimateStartTime = inferJob.getEstimateStartTime();
                    if (estimateStartTime != null) {
                        vo.setEstimateStartTime(estimateStartTime);
                    }

                    ZonedDateTime estimateCompleteTime = inferJob.getEstimateCompleteTime();
                    if (estimateCompleteTime != null) {
                        vo.setEstimateCompleteTime(estimateCompleteTime);
                    }
                }
            } catch (Exception e) {
                log.error("Error getting infer job details for taskId: " + task.getId(), e);
            }
        }

        return vo;
    }
}
