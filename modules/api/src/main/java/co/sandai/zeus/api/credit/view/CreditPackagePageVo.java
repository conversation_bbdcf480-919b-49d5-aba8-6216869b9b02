package co.sandai.zeus.api.credit.view;

import co.sandai.zeus.domain.credit.config.CreditPackage;
import co.sandai.zeus.domain.plan.model.PriceModel;
import java.util.List;
import lombok.Data;

@Data
public class CreditPackagePageVo {

    private List<CreditPackageVo> creditPackageList;

    @Data
    public static class CreditPackageVo {

        private String amount;
        private String price;
        private String priceId;
        private String description;

        public static CreditPackageVo convertFromModel(CreditPackage creditPackage, PriceModel priceModel) {
            CreditPackageVo creditPackageVo = new CreditPackageVo();
            creditPackageVo.setAmount(String.valueOf(creditPackage.getAmount()));
            creditPackageVo.setPrice(priceModel.getPrice());
            creditPackageVo.setPriceId(priceModel.getOutPriceId());
            creditPackageVo.setDescription(creditPackage.getDescription());
            return creditPackageVo;
        }
    }
}
