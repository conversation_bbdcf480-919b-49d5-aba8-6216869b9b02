package co.sandai.zeus.api.live;

import co.sandai.zeus.api.live.service.LiveApiService;
import co.sandai.zeus.api.live.service.LiveStream;
import co.sandai.zeus.api.live.view.LiveGenerateRequest;
import co.sandai.zeus.api.live.view.LiveGenerateResponse;
import co.sandai.zeus.api.live.view.LiveModeStatus;
import co.sandai.zeus.api.live.view.LiveStatusResponse;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.auth.service.AuthService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.domain.user.dao.User;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/live")
public class LiveController {

    @Autowired
    private LiveApiService liveApiService;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private TaskService taskService;

    @Resource
    private AuthService authService;

    @Value("${zeus.live.quota-reset-minutes:1440}")
    private int quotaResetMinutes; // Default to 1 day (1440 minutes)

    /**
     * Live mode generation endpoint.
     * If this is the first request, it will return a unique live stream ID.
     * For subsequent requests, client should provide this live stream ID.
     * If a user's previous request is still being processed, they'll receive
     * a PROCESSING status.
     */
    @PostMapping("/generate")
    @PrintLog
    public ResponseEntity<LiveGenerateResponse> liveGenerate(@RequestBody LiveGenerateRequest request)
            throws IOException {
        User user = userApiService.getCurrentUser();
        if (!authService.checkAuth(user.getId(), "real_time_generation_feature_access")) {
            throw ZeusServiceException.forbidden(
                    "Sorry, this feature is exclusive to premium members. Please view membership benefits and upgrade now!");
        }
        LiveGenerateResponse response = new LiveGenerateResponse();
        LiveStream liveStream = liveApiService.getUserCurrentStreamId(user.getId());

        LiveStatusResponse status = getLiveStatus(liveStream);
        LiveModeStatus currentStatus = status.getStatus();

        response.setStreamId(status.getStreamId());
        response.setNextAvailableTime(status.getNextAvailableTime());
        response.setStreamEndTime(status.getStreamEndTime());
        response.setStreamStartTime(status.getStreamStartTime());
        response.setTaskIds(status.getTaskIds());
        response.setStatus(currentStatus);

        // AVAILABLE 可以提交首个任务，PROCESSING 只能提交后继任务
        if (currentStatus != LiveModeStatus.AVAILABLE
                && currentStatus != LiveModeStatus.PROCESSING
                && currentStatus != LiveModeStatus.STOPPED) {
            log.info("User {} cannot submit task: current status={}", user.getId(), currentStatus);
            response.setSubmitted(false);
            return ResponseEntity.status(HttpStatus.OK).body(response);
        }

        if (currentStatus == LiveModeStatus.STOPPED) {
            // 当前用户的stream已经结束，且用户在用户quotaResetMinutes时间内，则可以发起新的stream
            if (request.getStreamId() != null
                    || status.getStreamStartTime()
                            .plusMinutes(quotaResetMinutes)
                            .isAfter(LocalDateTime.now())) {
                log.warn("Invalid request: status is STOPED but streamId is provided: {}", request.getStreamId());
                response.setSubmitted(false);
                return ResponseEntity.status(HttpStatus.OK).body(response);
            }
        } else if (currentStatus == LiveModeStatus.AVAILABLE) {
            if (request.getStreamId() != null) {
                log.warn("Invalid request: status is AVAILABLE but streamId is provided: {}", request.getStreamId());
                response.setSubmitted(false);
                return ResponseEntity.status(HttpStatus.OK).body(response);
            }
        } else if (currentStatus == LiveModeStatus.PROCESSING) {
            if (request.getStreamId() == null) {
                log.warn(
                        "Live mode invalid request: status=PROCESSING but streamId is missing. userId={}, request={}",
                        user.getId(),
                        request);
                stopStream(liveStream.getId());
                currentStatus = LiveModeStatus.STOPPED; // 允许用户重新抢锁
            }
        }

        response.setNextAvailableTime(null);

        Task lastTask = null;
        if (currentStatus == LiveModeStatus.PROCESSING) {
            log.debug("Checking if last task is completed for stream: {}", liveStream.getId());
            lastTask = taskService.getTaskById(liveStream.getTaskIds().getLast());
            if (lastTask.getStatus() != TaskStatus.Success) {
                log.info("Last task completed for stream: {}", liveStream.getId());
                response.setSubmitted(false);
                response.setStreamId(liveStream.getId());
                return ResponseEntity.status(HttpStatus.OK).body(response);
            }
        }

        // If this is a new session request
        Task task;
        if (request.getStreamId() == null) {
            log.info("Creating new live stream for user: {}", user.getId());
            // 抢全局锁
            liveStream = liveApiService.tryCreateNewStream(user.getId());
            if (liveStream == null) {
                log.warn("Failed to create new stream for user {}: no resources available", user.getId());
                response.setStatus(LiveModeStatus.NO_RESOURCES);
                response.setNextAvailableTime(liveApiService.getNextAvailableTime());
                response.setSubmitted(false);
                return ResponseEntity.ok(response);
            }

            // TODO 抢个人锁
            // Create initial I2V task
            log.info("Creating initial task for stream: {}", liveStream.getId());
            task = liveApiService.createInitialTask(request);
        } else {
            // TODO 抢个人锁
            log.info("Creating continuation task for stream: {}", request.getStreamId());
            // Re-check if the stream still exists and validate the last task again within synchronized block
            liveStream = liveApiService.getUserCurrentStreamId(user.getId());
            if (liveStream == null || liveStream.getId() != (request.getStreamId())) {
                log.warn("Stream no longer valid during continuation task creation");
                response.setStatus(LiveModeStatus.PROCESSING);
                response.setSubmitted(false);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            log.info("Last task ID: {} for stream: {}", lastTask.getId(), request.getStreamId());
            task = liveApiService.createContinuationTask(request, lastTask, user.getId());
        }

        // Using atomic operation in LiveService.appendTask to prevent concurrency issues
        log.info("Appending task ID: {} to stream: {}", task.getId(), liveStream.getId());
        liveStream = liveApiService.appendTask(user.getId(), task.getId());
        response.setStatus(LiveModeStatus.PROCESSING);
        response.setSubmitted(true);
        response.setStreamId(liveStream.getId());
        response.setStreamEndTime(liveStream.getEndTime());
        response.setStreamStartTime(liveStream.getStartTime());
        response.setTaskIds(liveStream.getTaskIds());
        log.info(
                "Live generate request completed successfully: streamId={}, taskId={}",
                liveStream.getId(),
                task.getId());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/status")
    @PrintLog
    public ResponseEntity<LiveStatusResponse> getLiveStatus() {
        User currentUser = userApiService.getCurrentUser();
        LiveStream liveStream = liveApiService.getUserCurrentStreamId(currentUser.getId());
        log.debug("User {} current stream: {}", currentUser.getId(), liveStream != null ? liveStream.getId() : "null");
        return ResponseEntity.ok(getLiveStatus(liveStream));
    }

    private LiveStatusResponse getLiveStatus(LiveStream liveStream) {
        LiveStatusResponse response = new LiveStatusResponse();
        if (liveStream != null) {
            response.setStreamId(liveStream.getId());
            response.setStreamEndTime(liveStream.getEndTime());
            response.setStreamStartTime(liveStream.getStartTime());
            response.setTaskIds(liveStream.getTaskIds());
            if (!liveStream.isValid()) {
                log.info(
                        "Stream {} quota exhausted: current time is after end time {}",
                        liveStream.getId(),
                        liveStream.getEndTime());
                response.setStatus(LiveModeStatus.STOPPED);
            } else {
                log.debug("Stream {} is in PROCESSING status", liveStream.getId());
                response.setStatus(LiveModeStatus.PROCESSING);
            }
        } else {
            if (!liveApiService.areResourcesAvailable()) {
                log.info("No resources available for new live stream");
                response.setStatus(LiveModeStatus.NO_RESOURCES);
                response.setNextAvailableTime(liveApiService.getNextAvailableTime());
            } else {
                log.debug("Resources available for new live stream");
                response.setStatus(LiveModeStatus.AVAILABLE);
            }
        }

        return response;
    }

    /**
     * Preview generated content using the live stream ID.
     * Returns a combined m3u8 playlist with all tasks' TS files.
     */
    @GetMapping("/preview/{streamId}")
    public ResponseEntity<String> getPreview(@PathVariable Long streamId) {
        log.info("Preview request received for stream: {}", streamId);
        User user = userApiService.getCurrentUser();
        LiveStream liveStream = liveApiService.getUserCurrentStreamId(user.getId());
        log.debug("User {} current stream: {}", user.getId(), liveStream != null ? liveStream.getId() : "null");

        // Validate that this stream belongs to current user
        if (liveStream == null) {
            log.warn("User {} attempted to access stream {} but has no active stream", user.getId(), streamId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("{\"error\":\"Stream not found\",\"message\":\"No active stream found\",\"status\":404}");
        }

        if (liveStream.getId() != streamId) {
            log.warn("User {} attempted to access stream {} which doesn't belong to them", user.getId(), streamId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(
                            "{\"error\":\"Access denied\",\"message\":\"You don't have permission to access this stream\",\"status\":403}");
        }

        LocalDateTime streamEndTime = liveStream.getEndTime();
        // Determine if stream should end based on time comparison
        boolean shouldEnd = LocalDateTime.now().isAfter(streamEndTime);

        try {
            // Generate m3u8 content that combines all tasks in the stream
            log.info("Generating m3u8 content for stream: {}, shouldEnd: {}", streamId, shouldEnd);
            byte[] m3u8Content = liveApiService.generateStreamM3U8Content(liveStream.getTaskIds(), shouldEnd);
            log.debug("Generated m3u8 content size: {} bytes", m3u8Content.length);
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType("application/vnd.apple.mpegurl"))
                    .body(new String(m3u8Content, StandardCharsets.UTF_8));
        } catch (IllegalStateException e) {
            log.error("Failed to generate m3u8 content for stream {}: {}", streamId, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("No content available for this stream");
        }
    }

    @PostMapping("/streams/{streamId}/stop")
    public ResponseEntity<Boolean> stopStream(@PathVariable long streamId) {
        User user = userApiService.getCurrentUser();
        LiveStream liveStream = liveApiService.getUserCurrentStreamId(user.getId());
        if (liveStream == null || liveStream.getId() != streamId) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(false);
        }
        if (liveStream.getEndTime().isBefore(LocalDateTime.now())) {
            //            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            return ResponseEntity.ok(true); // 无脑成功
        }
        return ResponseEntity.ok(liveApiService.stopStream(user.getId(), liveStream));
    }
}
