package co.sandai.zeus.api.platform.credits.service;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.plan.UserPlanService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PlatformCreditApiService {

    @Resource
    private UserPlanService userPlanService;

    @Resource
    private UserCreditService userCreditService;

    @Autowired
    private UserApiService userApiService;
}
