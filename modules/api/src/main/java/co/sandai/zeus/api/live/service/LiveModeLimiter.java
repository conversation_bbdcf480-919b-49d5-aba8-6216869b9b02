package co.sandai.zeus.api.live.service;

import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RPermitExpirableSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LiveModeLimiter {
    private static final String RESOURCE_KEY = "GLOBAL_RESOURCE_LIMITER";
    private final RPermitExpirableSemaphore semaphore;
    private final int maxConcurrentUsers;

    public LiveModeLimiter(
            RedissonClient redissonClient, @Value("${zeus.live.max-concurrent-users:10}") int maxConcurrentUsers) {
        //        this.redissonClient = redissonClient;1
        this.semaphore = redissonClient.getPermitExpirableSemaphore(RESOURCE_KEY);
        // 原子初始化总许可数（仅首次生效）
        this.maxConcurrentUsers = maxConcurrentUsers;
        log.info("Creating LiveModeLimiter: {}", this.semaphore.trySetPermits(maxConcurrentUsers));
        log.info("availablePermits: {}", semaphore.availablePermits());
    }
    /**
     * 尝试获取资源（非阻塞）
     * @return 许可ID（成功） / null（失败）
     */
    public String tryAcquire() {
        try {
            return semaphore.tryAcquire(0, 1, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }

    /**
     * 显式释放资源
     * @param permitId 许可ID
     */
    public void release(String permitId) {
        if (permitId != null) {
            semaphore.tryRelease(permitId);
        }
    }

    /**
     * 获取当前资源使用数
     */
    public int getCurrentUsage() {
        //        log.info("set "+ this.semaphore.trySetPermits(maxConcurrentUsers));
        return maxConcurrentUsers - semaphore.availablePermits();
    }
}
