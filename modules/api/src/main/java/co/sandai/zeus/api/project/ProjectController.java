package co.sandai.zeus.api.project;

import co.sandai.zeus.api.project.view.CreateProjectRequest;
import co.sandai.zeus.api.project.view.ProjectView;
import co.sandai.zeus.api.project.view.UpdateProjectRequest;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.project.ProjectService;
import co.sandai.zeus.domain.project.dao.Project;
import co.sandai.zeus.domain.user.dao.User;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/projects")
public class ProjectController {

    private static final int MAX_CANVAS_JSON_LENGTH = 1024 * 1000; // 1MB

    private final ProjectService projectService;
    private final AssetService assetService;
    private final UserApiService userApiService;

    public ProjectController(ProjectService projectService, AssetService assetService, UserApiService userApiService) {
        this.projectService = projectService;
        this.assetService = assetService;
        this.userApiService = userApiService;
    }

    @GetMapping("")
    public ListResponseByPage<ProjectView> listProjects(
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        User currentUser = userApiService.getCurrentUser();
        int total = projectService.countProjectsByUserId(currentUser.getId());
        List<Project> projects = projectService.getProjectsByUserId(currentUser.getId(), offset, limit);
        List<ProjectView> projectViews = projects.stream()
                .map(project -> ProjectView.fromProject(project, assetService))
                .toList();
        return new ListResponseByPage<>(projectViews, total, offset, limit);
    }

    @GetMapping("/{id}")
    public ProjectView getProject(@PathVariable Long id) {
        User currentUser = userApiService.getCurrentUser();
        Project project = projectService.getProjectById(id);

        if (project == null) {
            throw new ZeusServiceException(ErrorCode.ProjectNotFound);
        }

        if (!Objects.equals(project.getUserId(), currentUser.getId())) {
            throw ZeusServiceException.forbidden("No permission to access this project");
        }

        return ProjectView.fromProject(project, assetService);
    }

    @PostMapping("")
    @PrintLog
    public IDOnlyResponse createProject(@RequestBody @Valid CreateProjectRequest request) {
        User currentUser = userApiService.getCurrentUser();
        String canvasJson = request.getCanvasJson();

        // Check canvas json length
        if (canvasJson != null && canvasJson.length() > MAX_CANVAS_JSON_LENGTH) {
            throw ZeusServiceException.badRequest(
                    String.format("Canvas JSON exceeds maximum length of %d KB", MAX_CANVAS_JSON_LENGTH / 1024));
        }

        Project project = new Project();
        project.setUserId(currentUser.getId());
        project.setCanvasJson(request.getCanvasJson());
        project.setTitle(request.getTitle());
        project.setPosterAssetId(request.getPosterAssetId());
        Long projectId = projectService.createProject(project);
        return IDOnlyResponse.create(projectId);
    }

    @PutMapping("/{id}")
    @PrintLog
    public IDOnlyResponse updateProject(@PathVariable Long id, @RequestBody @Valid UpdateProjectRequest request) {
        User currentUser = userApiService.getCurrentUser();
        Project existingProject = projectService.getProjectById(id);

        if (existingProject == null) {
            throw ZeusServiceException.notFound("Project not found");
        }

        if (!Objects.equals(existingProject.getUserId(), currentUser.getId())) {
            throw ZeusServiceException.forbidden("No permission to update this request");
        }

        if (request.getPosterAssetId() != null) {
            existingProject.setPosterAssetId(request.getPosterAssetId());
        }

        if (request.getTitle() != null) {
            existingProject.setTitle(request.getTitle());
        }

        projectService.updateProject(existingProject);

        return IDOnlyResponse.create(id);
    }

    @DeleteMapping("/{id}")
    @PrintLog
    public IDOnlyResponse deleteProject(@PathVariable Long id) {
        User currentUser = userApiService.getCurrentUser();
        Project project = projectService.getProjectById(id);

        if (project == null) {
            throw ZeusServiceException.notFound("Project not found");
        }

        if (!Objects.equals(project.getUserId(), currentUser.getId())) {
            throw ZeusServiceException.forbidden("No permission to delete this project");
        }

        projectService.deleteProject(id);
        return IDOnlyResponse.create(id);
    }

    @PatchMapping("/{id}/canvas")
    public IDOnlyResponse updateCanvasJson(@PathVariable Long id, @RequestBody String canvasJson) {
        User currentUser = userApiService.getCurrentUser();
        Project existingProject = projectService.getProjectById(id);

        if (existingProject == null) {
            throw ZeusServiceException.notFound("Project not found");
        }

        if (!Objects.equals(existingProject.getUserId(), currentUser.getId())) {
            throw ZeusServiceException.forbidden("No permission to update this project");
        }

        // Check canvas json length
        if (canvasJson != null && canvasJson.length() > MAX_CANVAS_JSON_LENGTH) {
            throw ZeusServiceException.badRequest(
                    String.format("Canvas JSON exceeds maximum length of %d KB", MAX_CANVAS_JSON_LENGTH / 1024));
        }

        projectService.updateCanvasJson(id, canvasJson);

        return IDOnlyResponse.create(id);
    }
}
