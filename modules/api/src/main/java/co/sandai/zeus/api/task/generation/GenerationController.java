package co.sandai.zeus.api.task.generation;

import co.sandai.zeus.api.task.generation.view.ChunkVO;
import co.sandai.zeus.api.task.generation.view.ConditionVO;
import co.sandai.zeus.api.task.generation.view.ExtraArgs;
import co.sandai.zeus.api.task.generation.view.GenerationChunksRequestVO;
import co.sandai.zeus.api.task.generation.view.GenerationDetailResponseVO;
import co.sandai.zeus.api.task.generation.view.GenerationRequestVO;
import co.sandai.zeus.api.task.generation.view.GenerationResponseVO;
import co.sandai.zeus.api.task.generation.view.GenerationVO;
import co.sandai.zeus.api.task.generation.view.ImageGenerationRequestVO;
import co.sandai.zeus.api.task.generation.view.ImageGenerationResponseVO;
import co.sandai.zeus.api.task.generation.view.PipelineDetailResponseVO;
import co.sandai.zeus.api.task.generation.view.PipelineRequestVO;
import co.sandai.zeus.api.task.generation.view.PipelineResponseVO;
import co.sandai.zeus.api.task.service.GenerationApiService;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.utils.StreamUtil;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.auth.service.AuthService;
import co.sandai.zeus.domain.common.WhiteListService;
import co.sandai.zeus.domain.task.dao.PipelineTask;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskChunkDO;
import co.sandai.zeus.domain.task.dao.TaskChunkStatus;
import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.dao.TaskType;
import co.sandai.zeus.domain.task.dao.mapper.TaskChunkMapper;
import co.sandai.zeus.domain.task.service.GenerationService;
import co.sandai.zeus.domain.task.service.PipelineService;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.infra.imagegen.ImageGenClient;
import co.sandai.zeus.infra.imagegen.dto.SizeValueObject;
import co.sandai.zeus.infra.imagegen.enums.BizSceneEnum;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

@RestController
@RequestMapping("/api/v1/generations")
@Slf4j
public class GenerationController {

    @Resource
    private TaskService taskService;

    @Resource
    private ImageGenClient imageGenClient;

    @Resource
    private UserApiService userApiService;

    @Autowired
    private AssetService assetService;

    @Resource
    private GenerationService generationService;

    @Autowired
    private GenerationApiService generationApiService;

    @Resource
    private PipelineService pipelineService;

    @Resource
    private IDGenerator idGenerator;

    @Resource
    private TaskChunkMapper taskChunkMapper;

    @Value("${zeus.download.secret}")
    private String downloadSecret;

    @Value("${zeus.generation.enable-watermark:false}")
    private boolean defaultEnableWatermark;

    @Value("${zeus.generation.enable-download-watermark:false}")
    private boolean enableDownloadWatermark;

    @Resource
    private WhiteListService whiteListService;

    @Resource
    private AuthService authService;

    @GetMapping("")
    @PrintLog
    public ListResponseByPage<GenerationVO> listGenerations(
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit,
            @RequestParam(value = "taskSources", defaultValue = "") TaskSourceEnum[] taskSources,
            @RequestParam(value = "taskTypes", defaultValue = "") TaskType[] taskTypes) {
        log.info("getTasks offset: {}, limit: {}, sources: {}, types: {}", offset, limit, taskSources, taskTypes);
        long userId = userApiService.getCurrentUser().getId();
        List<TaskSourceEnum> sourceList = taskSources.length > 0 ? List.of(taskSources) : null;
        List<TaskType> typeList = taskTypes.length > 0 ? List.of(taskTypes) : null;
        List<Task> tasks = taskService.getTasksByFilters(userId, offset, limit, sourceList, typeList);
        int taskCount = taskService.countTasksByFilters(userId, sourceList, typeList);
        // 使用并行流来并行化 fromTask 的处理
        List<GenerationVO> generationVOs =
                tasks.parallelStream().map(GenerationVO::fromTask).collect(Collectors.toList());

        return new ListResponseByPage<>(generationVOs, taskCount, offset, limit);
    }

    private String generateIdempotentName(List<GenerationChunksRequestVO> requests) {
        requests.sort(Comparator.comparing(GenerationChunksRequestVO::getGenerationId));
        for (GenerationChunksRequestVO req : requests) {
            if (req.getChunkIds() != null) {
                req.getChunkIds().sort(Long::compareTo);
            }
        }

        String requestStr = JSON.toJSONString(requests) + downloadSecret;
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(requestStr.getBytes(StandardCharsets.UTF_8));
            return Base64.getUrlEncoder().withoutPadding().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            log.error("Failed to generate hash", e);
            return UUID.randomUUID().toString();
        }
    }

    @PostMapping
    @ResponseBody
    @PrintLog
    public GenerationResponseVO generate(@RequestBody @Valid GenerationRequestVO request) throws IOException {
        boolean enableEnhancement = request.getExtraArgs().getEnablePromptEnhancement() != null
                && request.getExtraArgs().getEnablePromptEnhancement();
        Task task = new Task();
        TaskType taskType = decideTaskType(request.getSource(), request.getChunks());
        task.setType(taskType);
        task.setCropArea(request.getExtraArgs().getCropArea());

        TaskExtraInferArgs extraInferArgs = request.getExtraArgs().getExtraInferArgs();
        if (extraInferArgs == null) {
            extraInferArgs = new TaskExtraInferArgs();
        }

        User currentUser = userApiService.getCurrentUser();
        long userId = currentUser.getId();

        boolean superUser = whiteListService.isSuperUser(currentUser.getEmail());
        boolean removeWaterMark = authService.checkAuth(userId, "watermark_removal");
        if (!extraInferArgs.isEnableWatermark()) {
            if (defaultEnableWatermark) {
                // 仅在开启了水印的环境，做水印的判断
                // 如果端上没有设置水印参数，读取用户会员身份做水印配置
                extraInferArgs.setEnableWatermark(!superUser && !removeWaterMark);
            }
        }

        task.setExtraInferArgs(extraInferArgs);
        task.setModel(request.getModel());
        task.setAspectRatio(request.getAspectRatio());
        task.setSeed(request.getSeed());
        task.setTSchedulerFunc(request.getExtraArgs().getTSchedulerFunc());
        task.setTSchedulerArgs(request.getExtraArgs().getTSchedulerArgs());
        task.setUserId(userId);
        task.setEnablePromptEnhancement(enableEnhancement);
        if (request.getSource() != null) {
            task.setTaskSource(request.getTaskSource());
        } else {
            task.setTaskSource(TaskSourceEnum.FLEXIBLE);
        }
        task.setPrompt("");

        ConditionVO condition = request.getSource();
        Asset asset = assetService.getAssetById(Long.parseLong(condition.getContent()));
        if (asset == null) {
            throw ZeusServiceException.notFound("Sources Asset not found");
        }
        if (asset.getMediaType().isVideo() && !condition.getType().equals("video")) {
            throw ZeusServiceException.badRequest(
                    "Source type is video, but user input type is: " + condition.getType());
        }
        if (asset.getMediaType().isImage() && !condition.getType().equals("image")) {
            throw ZeusServiceException.badRequest(
                    "Source type is image, but user input type is: " + condition.getType());
        }

        List<Asset> sourceAssets = List.of(asset);

        List<TaskChunkDO> outputChunks = buildTaskChunks(request.getChunks());

        String enhancementType = Optional.of(request)
                .map(GenerationRequestVO::getExtraArgs)
                .map(ExtraArgs::getExtraInferArgs)
                .map(TaskExtraInferArgs::getEnhancementType)
                .orElse("");

        if (taskType == TaskType.A2V) {
            enhancementType = "human_chain_gpt";
        }

        task.checkParams(asset);
        String queue = generationService.decideQueue(userId, superUser);
        generationService.submitGeneration(
                userId, null, enhancementType, task, sourceAssets, outputChunks, queue, true);

        // 构建返回结果
        GenerationResponseVO response = new GenerationResponseVO();
        response.setId(task.getId());
        return response;
    }

    private List<TaskChunkDO> buildTaskChunks(@NotNull List<ChunkVO> chunks) {
        List<TaskChunkDO> outputChunks = new ArrayList<>();
        int chunkIndex = 0;
        for (ChunkVO chunkVO : chunks) {
            TaskChunkDO taskChunkDO = new TaskChunkDO();
            taskChunkDO.setId(idGenerator.getNextId());
            float chunkDuration = chunkVO.getDuration(); // 默认使用用户指定的duration

            for (ConditionVO condition : chunkVO.getConditions()) {
                if ("text".equalsIgnoreCase(condition.getType())) {
                    taskChunkDO.setPrompt(condition.getContent());
                }
                if ("audio".equalsIgnoreCase(condition.getType())) {
                    Asset audio = assetService.getAssetById(Long.parseLong(condition.getContent()));
                    if (!audio.getMediaType().isAudio()) {
                        throw ZeusServiceException.badRequest("Expect audio asset, but got "
                                + audio.getMediaType().name());
                    }
                    // 取音频时长和用户设置时长的最小值
                    chunkDuration = Math.min(audio.getDuration(), chunkVO.getDuration());
                    taskChunkDO.setAudioAssetId(Long.parseLong(condition.getContent()));
                }
            }
            taskChunkDO.setDuration(chunkDuration); // 使用计算出的duration
            taskChunkDO.setIndex(chunkIndex++);
            taskChunkDO.setStatus(TaskChunkStatus.Pending);
            outputChunks.add(taskChunkDO);
        }
        return outputChunks;
    }

    private TaskType decideTaskType(ConditionVO condition, List<ChunkVO> chunks) {
        if (condition == null) {
            return TaskType.T2V;
        } else {
            for (ChunkVO chunkVO : chunks) {
                if (chunkVO.getConditions().stream().anyMatch(c -> "audio".equals(c.getType()))) {
                    return TaskType.A2V;
                }
            }
            String type = condition.getType();
            if ("image".equals(type)) {
                return TaskType.I2V;
            } else if ("video".equals(type)) {
                return TaskType.ExtendDuration;
            }

            throw ZeusServiceException.badRequest("Unsupported condition type in source: " + type);
        }
    }

    @GetMapping("/{id}")
    @ResponseBody
    @PrintLog
    public GenerationDetailResponseVO getGenerationDetail(@PathVariable long id, @RequestParam boolean chunks) {
        return generationApiService.getGenerationDetail(id, chunks);
    }

    @PostMapping("/pipeline")
    @ResponseBody
    @PrintLog
    public PipelineResponseVO createPipeline(@RequestBody @Valid PipelineRequestVO request) throws IOException {
        // Generate pipeline ID
        Long pipelineId = idGenerator.getNextId();
        User currentUser = userApiService.getCurrentUser();
        boolean superUser = whiteListService.isSuperUser(currentUser.getEmail());
        long userId = currentUser.getId();
        boolean removeWaterMark = authService.checkAuth(userId, "watermark_removal");
        List<Long> taskIds = new ArrayList<>();

        // Create tasks and add them to the pipeline
        int creditAmount = 0;
        List<String> allPrompts = new ArrayList<>();
        for (int i = 0; i < request.getTasks().size(); i++) {
            GenerationRequestVO taskRequest = request.getTasks().get(i);

            TaskType taskType = decideTaskType(taskRequest.getSource(), taskRequest.getChunks());
            if (i > 0) {
                taskType = TaskType.ExtendDuration;
            }
            boolean enableEnhancement = taskRequest.getExtraArgs().getEnablePromptEnhancement() != null
                    && taskRequest.getExtraArgs().getEnablePromptEnhancement();

            // Create task using direct constructor and setters
            TaskExtraInferArgs extraInferArgs = taskRequest.getExtraArgs().getExtraInferArgs();
            if (extraInferArgs == null) {
                extraInferArgs = new TaskExtraInferArgs();
            }
            if (!extraInferArgs.isEnableWatermark()) {
                if (defaultEnableWatermark) {
                    // 仅在开启了水印的环境，做水印的判断
                    // 如果端上没有设置水印参数，读取用户会员身份做水印配置
                    extraInferArgs.setEnableWatermark(!superUser && !removeWaterMark);
                }
            }
            Task task = new Task();
            task.setType(taskType);
            task.setCropArea(taskRequest.getExtraArgs().getCropArea());
            task.setExtraInferArgs(extraInferArgs);
            task.setModel(taskRequest.getModel());
            task.setAspectRatio(taskRequest.getAspectRatio());
            task.setSeed(taskRequest.getSeed());
            task.setPrompt("");
            task.setUserId(userId);
            task.setEnablePromptEnhancement(enableEnhancement);
            task.setTSchedulerFunc(taskRequest.getExtraArgs().getTSchedulerFunc());
            task.setTSchedulerArgs(taskRequest.getExtraArgs().getTSchedulerArgs());
            task.setTaskSource(taskRequest.getTaskSource());
            task.setStatus(TaskStatus.Pending);

            List<TaskChunkDO> outputChunks = buildTaskChunks(taskRequest.getChunks());
            if (outputChunks.size() == 1) {
                task.setPrompt(outputChunks.getFirst().getPrompt());
                task.setDuration(outputChunks.getFirst().getDuration());
            } else {
                // Save task chunks
                for (TaskChunkDO chunk : outputChunks) {
                    chunk.setTaskId(task.getId());
                    taskChunkMapper.insert(chunk);
                }
            }

            // Save task to database but don't submit it yet
            taskService.createTask(task);

            // Add task to pipeline with sequential index
            pipelineService.createPipelineTask(pipelineId, task.getId(), i);
            taskIds.add(task.getId());

            creditAmount += GenerationService.creditStrategy(task, outputChunks);
            allPrompts.add(task.getPrompt());
        }

        // Process first task in pipeline using PipelineService
        if (!taskIds.isEmpty()) {
            Long firstTaskId = taskIds.getFirst();
            GenerationRequestVO firstTaskRequest = request.getTasks().getFirst();
            Task firstTask = taskService.getTaskById(firstTaskId);

            // For the first task, get the source asset if specified
            List<Asset> sourceAssets = new ArrayList<>();
            if (firstTaskRequest.getSource() != null) {
                Asset asset = assetService.getAssetById(
                        Long.parseLong(firstTaskRequest.getSource().getContent()));
                if (asset == null) {
                    throw ZeusServiceException.notFound("Source Asset not found");
                }
                sourceAssets.add(asset);
            }

            // Get task chunks for the first task
            List<TaskChunkDO> firstTaskChunks = buildTaskChunks(firstTaskRequest.getChunks());

            // Get enhancement type
            String enhancementType = Optional.of(firstTaskRequest)
                    .map(GenerationRequestVO::getExtraArgs)
                    .map(ExtraArgs::getExtraInferArgs)
                    .map(TaskExtraInferArgs::getEnhancementType)
                    .orElse("");

            String queue = generationService.decideQueue(userId, superUser);

            // Start the pipeline processing with the first task
            pipelineService.processFirstTime(
                    firstTask, sourceAssets, firstTaskChunks, enhancementType, queue, creditAmount, userId, allPrompts);
        }

        // Construct response
        PipelineResponseVO response = new PipelineResponseVO();
        response.setPipelineId(pipelineId);
        response.setTaskIds(taskIds);

        return response;
    }

    @GetMapping("/pipeline/{id}")
    @ResponseBody
    @PrintLog
    public PipelineDetailResponseVO getPipelineDetail(@PathVariable long id) {
        List<PipelineTask> pipelineTasks = pipelineService.getPipelineTasksByPipelineId(id);
        if (pipelineTasks.isEmpty()) {
            throw ZeusServiceException.notFound("Pipeline not found");
        }

        PipelineDetailResponseVO response = new PipelineDetailResponseVO();
        response.setPipelineId(id);

        // Get status from the latest task
        List<Long> taskIds = new ArrayList<>();
        for (PipelineTask pipelineTask : pipelineTasks) {
            taskIds.add(pipelineTask.getTaskId());
        }

        response.setTaskIds(taskIds);

        // Get the status of all tasks in the pipeline
        String status = TaskStatus.Pending.name();
        boolean hasRunning = false;
        boolean hasFailed = false;
        boolean allCompleted = true;

        for (Long taskId : taskIds) {
            Task task = taskService.getTaskById(taskId);
            if (task.getStatus() == TaskStatus.Running) {
                hasRunning = true;
            } else if (task.getStatus() == TaskStatus.Fail) {
                hasFailed = true;
                allCompleted = false;
            } else if (task.getStatus() != TaskStatus.Success) {
                allCompleted = false;
            }
        }

        // Set pipeline status based on tasks status
        if (hasFailed) {
            status = TaskStatus.Fail.name();
        } else if (hasRunning) {
            status = TaskStatus.Running.name();
        } else if (allCompleted) {
            status = TaskStatus.Success.name();
        }

        // Status already set in the conditional logic above

        response.setStatus(status);

        return response;
    }

    @GetMapping("/preview")
    @ResponseBody
    @PrintLog
    public ResponseEntity<String> getGenerationChunks(@RequestParam(name = "requests") @Valid String base64Requests) {
        List<GenerationChunksRequestVO> requests = generationApiService.parseGenerationPreviewRequest(base64Requests);
        return ResponseEntity.ok()
                .header("Content-Type", "application/vnd.apple.mpegurl")
                .body(generationApiService.getGenerationPreviewM3u8Content(requests));
    }

    @GetMapping("/streaming-download")
    @ResponseBody
    @PrintLog
    public ResponseEntity<StreamingResponseBody> streamingDownloadGenerationChunks(
            @RequestParam(name = "requests") @Valid String base64Requests,
            @RequestParam(name = "filename", required = false) String filename) {
        log.info("Starting streaming generation chunks download, base64 size: {} bytes", base64Requests.length());
        User user = userApiService.getCurrentUser();
        if (user == null) {
            throw new ZeusServiceException(HttpStatus.UNAUTHORIZED, ErrorCode.NotLogin);
        }
        try {
            String decodedRequests = new String(Base64.getDecoder().decode(base64Requests), StandardCharsets.UTF_8);
            List<GenerationChunksRequestVO> requests =
                    JSON.parseArray(decodedRequests, GenerationChunksRequestVO.class);
            log.info("decoded {} generation chunk in streaming download requests", requests.size());
            // 提前收集所有资源 - 在流式处理开始前完成
            GenerationApiService.GenerationAssetsResult result = generationApiService.collectGenerationAssets(requests);
            log.info(
                    "Collected {} assets for streaming download",
                    result.assets().size());

            if (result.assets().isEmpty()) {
                return ResponseEntity.status(HttpStatus.NO_CONTENT).body(outputStream -> {
                    try {
                        outputStream.write("{\"error\":\"No content available\",\"status\":204}".getBytes());
                    } catch (IOException e) {
                        log.error("Error writing no content response", e);
                    }
                });
            }

            User currentUser = userApiService.getCurrentUser();
            long userId = currentUser.getId();

            boolean superUser = whiteListService.isSuperUser(currentUser.getEmail());
            boolean removeWaterMark = authService.checkAuth(userId, "watermark_removal");
            boolean enableWatermark = enableDownloadWatermark && (!superUser && !removeWaterMark);
            // 创建流式响应
            StreamingResponseBody responseBody = outputStream -> {
                try {
                    // 执行实际的视频流传输
                    // 不要在MP4文件前添加任何字节，会导致文件格式损坏
                    outputStream.flush();
                    assetService.streamTsListToMp4(result.assets(), outputStream, enableWatermark);
                } catch (Exception e) {
                    log.error("Error streaming TS files to MP4: {}", e.getMessage(), e);
                    try {
                        // 尝试写入错误信息，但仅在输出流未关闭时
                        if (!Thread.currentThread().isInterrupted()) {
                            outputStream.write("{\"error\":\"Video streaming failed\"}".getBytes());
                        }
                    } catch (IOException ioe) {
                        // 忽略写入错误，可能客户端已断开连接
                        log.debug("Could not write error to stream, client may have disconnected");
                    }
                }
            };

            // 设置适当的视频流头信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.valueOf("video/mp4"));

            // 使用RFC5987协议对文件名进行编码以支持非ASCII字符
            String baseFilename = (filename != null
                    ? (filename.toLowerCase().endsWith(".mp4") ? filename : filename + ".mp4")
                    : generateIdempotentName(requests) + ".mp4");
            ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                    .filename(baseFilename, StandardCharsets.UTF_8)
                    .build();
            headers.setContentDisposition(contentDisposition);

            return ResponseEntity.ok().headers(headers).body(responseBody);

        } catch (ZeusServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("Pre-streaming preparation failed: {}", e.getMessage(), e);
            throw ZeusServiceException.internalError("Pre-streaming preparation failed", e);
        }
    }

    @PostMapping("/images")
    @ResponseBody
    @PrintLog
    public List<ImageGenerationResponseVO> generateImages(@RequestBody ImageGenerationRequestVO request) {
        List<String> urls = imageGenClient.generateImage(
                request.getPrompt(),
                new SizeValueObject().setWidth(request.getW()).setHeight(request.getH()),
                request.getCount(),
                BizSceneEnum.FLEXIBLE);

        List<ImageGenerationResponseVO> result = StreamUtil.of(urls)
                .map(url -> {
                    ImageGenerationResponseVO response = new ImageGenerationResponseVO();
                    response.setUrl(url);
                    return response;
                })
                .toList();
        return result;
    }
}
