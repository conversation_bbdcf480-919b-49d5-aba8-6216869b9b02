package co.sandai.zeus.api.task.generation.view;

import co.sandai.zeus.domain.asset.dao.AssetVideoModerationStatus;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.Data;

@Data
public class GenerationDetailResponseVO {
    private long id;
    private String model;
    private String status;
    private Integer progress;
    private List<Chunk> chunks;
    private String resultVideoURL;
    private String resultPosterURL;
    private int width;
    private int height;
    private float duration;
    private AssetVideoModerationStatus resultVideoModerationStatus;
    private long resultVideoId;
    private boolean isModerationPassed;
    private ZonedDateTime createTime;
    private ZonedDateTime estimateCompleteTime;
    private ZonedDateTime estimateStartTime;

    private ConditionVO source;
    private ExtraArgs extraArgs;

    @Data
    public static class Chunk {
        private float duration;
        private String status;
        private long id;
        private List<ConditionVO> conditions;
        private String prompt;
        private String enhancedPrompt;
    }
}
