package co.sandai.zeus.api.payment.webhook.eventhandler;

import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.payment.PaymentService;
import co.sandai.zeus.domain.payment.dao.OrderDO;
import co.sandai.zeus.domain.plan.UserPlanService;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.enums.IntervalTypeEnum;
import co.sandai.zeus.domain.plan.model.enums.PlanStatusEnum;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import com.stripe.model.*;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class InvoicePaidEventHandler implements EventHandler {

    @Resource
    private PaymentService paymentService;

    @Resource
    private PlanConfigService planConfigService;

    @Resource
    private UserService userService;

    @Resource
    private UserPlanService userPlanService;

    @Resource
    private UserCreditService userCreditService;

    @Override
    public String getEventKey() {
        return "invoice.paid";
    }

    @Override
    public void handle(StripeObject eventData) {
        Invoice invoice = (Invoice) eventData;
        String billingReason = invoice.getBillingReason();
        if (StringUtils.isBlank(billingReason)) {
            LogUtil.errorf(log, "billingReason is empty, invoiceId: {0}", invoice.getId());
            return;
        }
        BillReasonEnum billReasonEnum = BillReasonEnum.fromCode(billingReason);
        if (billReasonEnum == null) {
            LogUtil.errorf(log, "unknown billingReason: {0}, invoiceId: {1}", billingReason, invoice.getId());
            return;
        }
        User user = userService.getUserByOutCustomerId(invoice.getCustomer());

        log.info(
                "handle strip invoice.paid event, invoiceId: {}, userId: {}, billingReason: {}",
                invoice.getId(),
                user.getId(),
                billingReason);

        switch (billReasonEnum) {
            case SUBSCRIPTION_CREATE:
                paymentService.syncSubscriptionOrder(invoice, OrderDO.FromEnum.SUBSCRIBE, true);
                break;
            case SUBSCRIPTION_CYCLE:
            case SUBSCRIPTION_UPDATE:
                Price currentPrice = invoice.getLines().getData().stream()
                        // 过滤掉是proration的项目
                        .filter(x -> x.getAmount() > 0L)
                        .findFirst()
                        .orElseThrow()
                        .getPrice();
                String currentPriceId = currentPrice.getId();
                Plan currentPlan = planConfigService.getPlanByPriceId(currentPriceId, PlanStatusEnum.ACTIVE);
                changePlan(invoice, user, currentPlan);
                break;
            case MANUAL:
                paymentService.syncOneTimeOrder(invoice, true);
                break;
            default:
                LogUtil.warnf(log, "unknown billingReason: {0}, invoiceId: {1}", billingReason, invoice.getId());
                return;
        }
    }

    @AllArgsConstructor
    enum BillReasonEnum {
        SUBSCRIPTION_CREATE("subscription_create"),
        SUBSCRIPTION_CYCLE("subscription_cycle"),
        SUBSCRIPTION_UPDATE("subscription_update"),
        SUBSCRIPTION("subscription"),
        MANUAL("manual");

        private final String code;

        public static BillReasonEnum fromCode(String value) {
            for (BillReasonEnum e : BillReasonEnum.values()) {
                if (e.code.equalsIgnoreCase(value)) {
                    return e;
                }
            }
            return null;
        }
    }

    /**
     * 计划变更
     */
    private void changePlan(Invoice invoice, User user, Plan currentPlan) {
        UserPlanDO oldUserPlan = userPlanService.queryUserPlan(user.getId());
        Plan oldPlan = planConfigService.getPlanByCode(oldUserPlan.getPlanCode(), null);

        // case1: 订阅周期不变，升级。 此时会有订单立刻需要支付， 立即收到invoice.paid
        if (oldPlan.getIntervalTypeEnum() == currentPlan.getIntervalTypeEnum()
                && oldPlan.getPlanGrade() < currentPlan.getPlanGrade()) {
            // 校验支付结果、落单
            if (paymentService.syncSubscriptionOrder(invoice, OrderDO.FromEnum.UPGRADE, false)) {
                // 更新userPlan
                userPlanService.changeSubscription(user.getId(), currentPlan, TimeUtil.from(invoice.getCreated()));
                // 发放差值credit
                userCreditService.issueSubscriptionDiffCredit(user.getId(), currentPlan, oldPlan);
            } else {
                LogUtil.errorf(log, "syncOrder failed, invoiceId: {0}", invoice.getId());
            }
            return;
        }
        // case2：订阅周期变化，月切年 + 会员等级平级、升级。 此时也有订单立即支付，立即收到invoice.paid
        if (oldPlan.getIntervalTypeEnum() == IntervalTypeEnum.MONTHLY
                && currentPlan.getIntervalTypeEnum() == IntervalTypeEnum.YEARLY
                && oldPlan.getPlanGrade() <= currentPlan.getPlanGrade()) {
            // 校验支付结果、落单
            if (paymentService.syncSubscriptionOrder(invoice, OrderDO.FromEnum.UPGRADE, false)) {
                // 更新userPlan
                userPlanService.changeSubscription(user.getId(), currentPlan, TimeUtil.from(invoice.getCreated()));
                // 发放第一笔credit
                userCreditService.issueNewSubscriptionCredit(
                        user.getId(), currentPlan, TimeUtil.from(invoice.getCreated()));
            } else {
                LogUtil.errorf(log, "syncOrder failed, invoiceId: {0}", invoice.getId());
            }
            return;
        }
        // 剩下的case都是在切换billing cycle的时候才会收到invoice.paid
        if (paymentService.syncSubscriptionOrder(invoice, OrderDO.FromEnum.RENEW, false)) {
            // 更新userPlan
            userPlanService.changeSubscription(user.getId(), currentPlan, TimeUtil.from(invoice.getCreated()));
            // renew credit
            userCreditService.renewSubscriptionCredit(user.getId(), currentPlan, TimeUtil.from(invoice.getCreated()));
        }
    }
}
