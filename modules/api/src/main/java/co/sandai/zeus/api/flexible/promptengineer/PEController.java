package co.sandai.zeus.api.flexible.promptengineer;

import co.sandai.zeus.api.flexible.objects.view.RunCoziWorkflowRequest;
import co.sandai.zeus.infra.prompt.CozeTemplateService;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/prompt-engineer")
@Slf4j
public class PEController {

    @Resource
    private CozeTemplateService cozeTemplateService;

    @PostMapping("/run-coze-workflow")
    public JSONObject runCozeWorkflow(@RequestBody RunCoziWorkflowRequest data) {
        CozeTemplateService.CozeResponse res =
                cozeTemplateService.runWorkflow(data.getParameters(), data.getWorkflowId(), data.getApplicationId());
        return JSONObject.parseObject(res.getData());
    }
}
