package co.sandai.zeus.api.live.service;

import co.sandai.zeus.api.live.view.LiveGenerateRequest;
import co.sandai.zeus.api.task.generation.view.ConditionVO;
import co.sandai.zeus.api.task.service.GenerationApiService;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.helper.M3U8Helper;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.task.dao.*;
import co.sandai.zeus.domain.task.service.GenerationService;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.infra.IDGenerator;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LiveApiService {

    @Resource
    private UserApiService userApiService;

    @Resource
    private GenerationApiService generationApiService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private AssetService assetService;

    @Value("${zeus.live.max-concurrent-users:10}")
    private int maxConcurrentUsers;

    @Value("${zeus.live.user-daily-limit-seconds:60}")
    private int userDailyLimitSeconds;

    @Autowired
    RedisUserStreamManager redisUserStreamManager;

    @Autowired
    LiveModeLimiter liveModeLimiter;

    @Autowired
    private IDGenerator idGenerator;

    @Autowired
    private GenerationService generationService;

    /**
     * Check if system resources are available for live mode
     * @return true if resources are available, false otherwise
     */
    public boolean areResourcesAvailable() {
        return liveModeLimiter.getCurrentUsage() < maxConcurrentUsers;
    }

    /**
     * Get the user's current stream ID if they are in a session
     * @param userId User ID
     * @return The current stream ID or null if not in a session
     */
    public LiveStream getUserCurrentStreamId(long userId) {
        LiveStream stream = redisUserStreamManager.getUserStream(userId);

        if (stream != null) {
            //            printStreamTasks(stream); // TODO for debug
        } else {
            log.info("No active stream found for userId: {}", userId);
        }

        return stream;
    }

    public LiveStream tryCreateNewStream(long userId) {
        String permitId = liveModeLimiter.tryAcquire();
        if (permitId == null) {
            return null;
        }
        return redisUserStreamManager.createStream(userId, userDailyLimitSeconds, permitId);
    }

    public Task createInitialTask(LiveGenerateRequest request) throws IOException {
        Task task = new Task();
        TaskType taskType = TaskType.I2V;
        task.setType(taskType);
        task.setCropArea(request.getCropArea());

        TaskExtraInferArgs extraInferArgs = request.getExtraInferArgs();
        if (extraInferArgs == null) {
            extraInferArgs = new TaskExtraInferArgs();
        }
        task.setExtraInferArgs(extraInferArgs);
        task.setModel(request.getModel());
        task.setAspectRatio(request.getAspectRatio());
        task.setSeed(request.getSeed());
        task.setTSchedulerFunc(request.getTSchedulerFunc());
        task.setTSchedulerArgs(request.getTSchedulerArgs());
        task.setUserId(userApiService.getCurrentUser().getId());
        task.setTaskSource(TaskSourceEnum.LIVE);
        task.setEnablePromptEnhancement(request.getEnablePromptEnhancement());
        task.setPrompt("");

        ConditionVO condition = request.getSource();
        Asset asset = assetService.getAssetById(Long.parseLong(condition.getContent()));
        if (asset == null) {
            throw ZeusServiceException.notFound("Sources Asset not found");
        }
        List<Asset> sourceAssets = List.of(asset);

        List<TaskChunkDO> outputChunks = new ArrayList<>();
        for (int i = 0; i < request.getSteps().size(); i++) {
            LiveGenerateRequest.Step step = request.getSteps().get(i);
            TaskChunkDO chunk = new TaskChunkDO();
            chunk.setTaskId(task.getId());
            chunk.setId(idGenerator.getNextId());
            chunk.setDuration(step.getDuration());
            chunk.setPrompt(step.getPrompt());
            chunk.setStatus(TaskChunkStatus.Pending);
            chunk.setIndex(i);
            outputChunks.add(chunk);
        }

        String enhancementType = Optional.of(request)
                .map(LiveGenerateRequest::getExtraInferArgs)
                .map(TaskExtraInferArgs::getEnhancementType)
                .orElse("");

        task.checkParams(asset);

        generationService.submitGeneration(
                task.getUserId(), null, enhancementType, task, sourceAssets, outputChunks, "", false);

        return task;
    }

    /**
     * Create continuation task for live mode
     * @param request Request details
     * @param userId User ID
     * @return Created task
     */
    public Task createContinuationTask(LiveGenerateRequest request, Task lastTask, long userId) throws IOException {
        Asset lastVideo;
        if (lastTask.getResultVideoId() > 0) {
            lastVideo = assetService.getAssetById(lastTask.getResultVideoId());
        } else {
            // Previous task doesn't have a valid result video yet
            if (lastTask.getStatus() == TaskStatus.Fail || lastTask.getStatus() == TaskStatus.Canceled) {
                // Task is completed but no valid result video - something went wrong
                throw new IllegalStateException("Previous task is completed but has no valid result video");
            } else {
                // Task is still processing
                throw new IllegalStateException("Previous task is still processing");
            }
        }

        Task task = new Task();
        TaskType taskType = TaskType.ExtendDuration;
        task.setType(taskType);
        task.setCropArea(request.getCropArea());
        boolean enableEnhancement = request.getEnablePromptEnhancement();

        TaskExtraInferArgs extraInferArgs = request.getExtraInferArgs();
        if (extraInferArgs == null) {
            extraInferArgs = new TaskExtraInferArgs();
        }
        if (!extraInferArgs.isEnableWatermark()) {
            extraInferArgs.setEnableWatermark(request.getEnablePromptEnhancement()); // 如果端上没有显示设置水印参数，则走配置
        }
        task.setExtraInferArgs(extraInferArgs);
        task.setModel(request.getModel());
        task.setAspectRatio(request.getAspectRatio());
        task.setSeed(request.getSeed());
        task.setTSchedulerFunc(request.getTSchedulerFunc());
        task.setTSchedulerArgs(request.getTSchedulerArgs());
        task.setUserId(userApiService.getCurrentUser().getId());
        task.setTaskSource(TaskSourceEnum.LIVE);
        task.setEnablePromptEnhancement(request.getEnablePromptEnhancement());
        task.setPrompt("");

        List<Asset> sourceAssets = List.of(lastVideo);

        List<TaskChunkDO> outputChunks = new ArrayList<>();
        for (int i = 0; i < request.getSteps().size(); i++) {
            LiveGenerateRequest.Step step = request.getSteps().get(i);
            TaskChunkDO chunk = new TaskChunkDO();
            chunk.setTaskId(task.getId());
            chunk.setId(idGenerator.getNextId());
            chunk.setDuration(step.getDuration());
            chunk.setPrompt(step.getPrompt());
            chunk.setStatus(TaskChunkStatus.Pending);
            chunk.setIndex(i);
            outputChunks.add(chunk);
        }

        String enhancementType = Optional.of(request)
                .map(LiveGenerateRequest::getExtraInferArgs)
                .map(TaskExtraInferArgs::getEnhancementType)
                .orElse("");

        task.checkParams(lastVideo);

        generationApiService.submitTask(enhancementType, task, sourceAssets, outputChunks, "", false);

        return task;
    }

    public byte[] generateStreamM3U8Content(List<Long> taskIds, boolean shouldEnd) {
        List<Asset> allAssets = new ArrayList<>();
        boolean allTasksComplete = true;

        // Collect assets from all tasks
        for (Long taskId : taskIds) {
            Task task = taskService.getTaskById(taskId);
            if (task == null) {
                continue;
            }

            // Check if this task is complete
            allTasksComplete = allTasksComplete && TaskStatus.Success.equals(task.getStatus());

            // Get assets for this task
            List<Asset> taskAssets = assetService.getContinueChunkAssetByTaskId(taskId);
            allAssets.addAll(taskAssets);
        }

        // Get public URLs for all assets
        List<String> chunkUrls = assetService.getAssetPublicUrls(allAssets, 60 * 24); // 1 day
        double chunkDuration = taskService.getChunkDuration();
        long targetDuration = (long) chunkDuration;

        // Generate M3U8 file
        allTasksComplete = allTasksComplete && shouldEnd;
        return M3U8Helper.generateM3U8File(chunkUrls, chunkDuration, targetDuration, allTasksComplete);
    }

    public LiveStream appendTask(long userId, long taskId) {
        return redisUserStreamManager.addTaskToStream(userId, taskId);
    }

    public boolean stopStream(long userId, LiveStream stream) {
        redisUserStreamManager.stopStream(userId, stream);
        this.liveModeLimiter.release(stream.getPermitId());
        return true;
    }

    public LocalDateTime getNextAvailableTime() {
        return redisUserStreamManager.getNextAvailableTime();
    }
}
