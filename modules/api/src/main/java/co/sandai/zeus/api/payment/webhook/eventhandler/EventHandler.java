package co.sandai.zeus.api.payment.webhook.eventhandler;

import com.stripe.model.Event;
import com.stripe.model.StripeObject;

/**
 * 处理webhook事件的handler
 * 注意幂等性设计
 */
public interface EventHandler {

    String getEventKey();

    default void handle(StripeObject eventData) {}

    default void handle(StripeObject eventData, Event event) {}

    default boolean needWholeEventData() {
        return false;
    }
}
