package co.sandai.zeus.api.user;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.api.user.view.UserProfile;
import co.sandai.zeus.api.user.view.UserProfileUpdateRequest;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.domain.user.dao.User;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/user/profile")
public class ProfileController {

    public final UserApiService userApiService;

    public ProfileController(UserApiService userApiService) {
        this.userApiService = userApiService;
    }

    @GetMapping("")
    @PrintLog
    public UserProfile currentUserProfile() {
        User user = this.userApiService.getCurrentUser();
        return UserProfile.fromUser(user);
    }

    @PutMapping("")
    @PrintLog
    public IDOnlyResponse updateUserProfile(@RequestBody UserProfileUpdateRequest data) {
        long userId = userApiService.updateProfile(data);
        return IDOnlyResponse.create(userId);
    }
}
