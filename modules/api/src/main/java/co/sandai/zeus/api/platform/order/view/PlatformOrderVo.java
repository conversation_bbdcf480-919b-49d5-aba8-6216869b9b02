package co.sandai.zeus.api.platform.order.view;

import co.sandai.zeus.api.user.view.UserProfile;
import co.sandai.zeus.domain.payment.dao.OrderWithUserDO;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PlatformOrderVo {
    private long id;
    private long amount;
    private UserProfile payer;
    private long creditAmount;
    private LocalDateTime createTime;

    public static PlatformOrderVo fromOrder(OrderWithUserDO order) {
        return PlatformOrderVo.builder()
                .id(order.getId())
                .createTime(order.getCreateTime())
                .amount(order.getAmount())
                .payer(UserProfile.fromUser(order.getUser()))
                .build();
    }
}
