package co.sandai.zeus.api.project.view;

import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.project.dao.Project;
import java.util.Date;
import lombok.Data;

@Data
public class ProjectView {
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 画布JSON数据
     */
    private String canvasJson;

    /**
     * 项目标题
     */
    private String title;

    /**
     * 项目封面URL
     */
    private String posterUrl;

    /**
     * 封面 asset id
     */
    private Long posterAssetId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public static ProjectView fromProject(Project project, AssetService assetService) {
        if (project == null) {
            return null;
        }

        ProjectView vo = new ProjectView();
        vo.setId(project.getId());
        vo.setUserId(project.getUserId());
        vo.setCanvasJson(project.getCanvasJson());
        vo.setTitle(project.getTitle());
        vo.setPosterAssetId(project.getPosterAssetId());

        // Convert Timestamp to Date for timestamps
        if (project.getCreateTime() != null) {
            vo.setCreateTime(new Date(project.getCreateTime().getTime()));
        }
        if (project.getUpdateTime() != null) {
            vo.setUpdateTime(new Date(project.getUpdateTime().getTime()));
        }

        // Set poster URL using AssetService
        if (project.getPosterAssetId() != null) {
            vo.setPosterUrl(assetService.getAssetPublicUrlById(project.getPosterAssetId(), 60));
        }

        return vo;
    }
}
