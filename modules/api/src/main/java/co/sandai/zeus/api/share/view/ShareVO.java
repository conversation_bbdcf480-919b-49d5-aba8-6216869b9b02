package co.sandai.zeus.api.share.view;

import co.sandai.zeus.config.SpringContext;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.share.dao.Share;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.service.TaskService;
import com.alibaba.fastjson.JSON;
import java.util.Objects;
import lombok.Builder;

@Builder
public class ShareVO {
    public long id;
    public long taskId;
    public ShareGenerationsRequest content;
    public String posterUrl;

    public static ShareVO fromShare(Share share) {
        String content = share.getContent();
        ShareGenerationsRequest parsedContent = null;
        AssetService assetService = SpringContext.getBean(AssetService.class);
        TaskService taskService = SpringContext.getBean(TaskService.class);

        // 获取分享的视频的 poster
        String posterUrl = "";
        if (Objects.nonNull(content) && !content.isEmpty()) {
            parsedContent = JSON.parseObject(content, ShareGenerationsRequest.class);

            if (parsedContent.getAssetId() > 0) {
                Asset asset = assetService.getAssetById(parsedContent.getAssetId());
                long posterAssetId = asset.getPosterAssetId();
                if (posterAssetId > 0) {
                    posterUrl = assetService.getAssetPublicUrlById(posterAssetId, 60);
                }
            }

            if (!parsedContent.getChunks().isEmpty()) {
                long firstTaskId = parsedContent.getChunks().getFirst().getGenerationId();
                Task firstTask = taskService.getTaskById(firstTaskId);
                if (Objects.nonNull(firstTask)) {
                    posterUrl = assetService.getAssetPublicUrlById(firstTask.getResultVideoPosterId(), 60);
                }
            }
        }
        return ShareVO.builder()
                .content(parsedContent)
                .id(share.getId())
                .posterUrl(posterUrl)
                .taskId(share.getTaskId())
                .build();
    }
}
