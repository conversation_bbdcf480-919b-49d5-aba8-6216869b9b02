package co.sandai.zeus.api.operation;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.api.user.view.TempUserInfo;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/operation")
public class OperationController {

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private UserService userService;

    @GetMapping("/create-temp-user-account")
    @PrintLog
    public List<TempUserInfo> createTempUserAccount(@RequestParam(value = "count", defaultValue = "1") int count) {
        User user = userApiService.getCurrentUser();
        if (!userService.isInAdminWhiteList(user)) {
            throw ZeusServiceException.forbidden("forbidden");
        }
        return userApiService.signupTempUsers(count);
    }
}
