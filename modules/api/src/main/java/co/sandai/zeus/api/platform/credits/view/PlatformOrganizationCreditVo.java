package co.sandai.zeus.api.platform.credits.view;

import co.sandai.zeus.api.credit.view.UserCreditVo;
import co.sandai.zeus.domain.credit.dao.UserCreditDailyUsageDO;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@NoArgsConstructor
@Accessors(chain = true)
public class PlatformOrganizationCreditVo extends UserCreditVo {
    List<UserCreditDailyUsageDO> usage;
}
