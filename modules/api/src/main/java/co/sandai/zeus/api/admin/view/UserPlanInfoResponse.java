package co.sandai.zeus.api.admin.view;

import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.user.dao.User;
import lombok.Data;

@Data
public class UserPlanInfoResponse {

    private Long userId;
    private String email;
    private String nickname;
    private String currentPlanCode;
    private String currentPlanName;
    private String expireTime;
    private String nextRenewTime;
    private Boolean hasActivePlan;

    public static UserPlanInfoResponse fromUser(User user, UserPlanDO userPlan, Plan plan) {
        UserPlanInfoResponse response = new UserPlanInfoResponse();
        response.setUserId(user.getId());
        response.setEmail(user.getEmail());
        response.setNickname(user.getNickname());

        if (userPlan != null && plan != null) {
            response.setCurrentPlanCode(userPlan.getPlanCode());
            response.setCurrentPlanName(plan.getName());
            response.setExpireTime(TimeUtil.format(userPlan.getExpireTime()));
            response.setNextRenewTime(TimeUtil.format(userPlan.getNextRenewTime()));
            response.setHasActivePlan(!TimeUtil.isExpire(userPlan.getExpireTime()));
        } else {
            response.setCurrentPlanCode("free_plan");
            response.setCurrentPlanName("Free");
            response.setExpireTime(null);
            response.setNextRenewTime(null);
            response.setHasActivePlan(false);
        }

        return response;
    }
}
