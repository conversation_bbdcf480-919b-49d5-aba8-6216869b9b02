package co.sandai.zeus.api.voice;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.api.voice.request.UpdateVoiceRequest;
import co.sandai.zeus.api.voice.view.VoiceView;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.common.vo.ListResponse;
import co.sandai.zeus.domain.auth.service.AuthService;
import co.sandai.zeus.domain.common.WhiteListService;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.voice.dao.Voice;
import co.sandai.zeus.domain.voice.service.VoiceService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping("/api/v1/voices")
public class VoiceController {

    @Autowired
    private VoiceService voiceService;

    @Autowired
    private UserApiService userApiService;

    @Resource
    private AuthService authService;

    @Autowired
    private WhiteListService whiteListService;

    /**
     * Get list of all available voices including provider voices and user-created voices
     * All voices are now read from the database, including both provider voices and user-created voices
     * This endpoint is now public and supports both authenticated and non-authenticated users
     *
     * @return Combined list of voice data from database
     */
    @GetMapping("")
    @PrintLog
    public ListResponse<VoiceView> getVoices() {
        // Get current user (may be null for non-authenticated users)
        User currentUser = userApiService.getCurrentUser();

        // Get all voices from the database, including provider voices and user-created voices
        // For non-authenticated users, use userId = 0 to get only provider voices
        long userId = currentUser != null ? currentUser.getId() : 0L;
        List<Voice> allVoices = voiceService.getAllVoices(userId);

        // Convert to view objects
        List<VoiceView> voiceViews =
                allVoices.stream().map(VoiceView::fromVoice).collect(Collectors.toList());
        return new ListResponse<>(voiceViews);
    }

    /**
     * Administrative endpoint to synchronize provider voices with the database
     * This will fetch the latest voices from all providers, compare with what's in the database,
     * add new voices, and mark removed voices as inactive
     *
     * @return Status information about the synchronization process
     */
    @PostMapping("/sync-provider-voices")
    @PrintLog
    // In production, this endpoint should be secured with admin-level permissions
    public Map<String, Object> syncProviderVoices(
            @RequestParam(value = "updateExisting", defaultValue = "false") boolean updateExisting,
            @RequestParam(value = "provider", defaultValue = "minimax") String provider) {
        // Call service to perform the sync
        User currentUser = userApiService.getCurrentUser();
        boolean superUser = whiteListService.isSuperUser(currentUser.getEmail());
        if (!superUser) {
            throw ZeusServiceException.forbidden("Only super users can sync provider voices");
        }

        Map<String, Integer> syncResults = voiceService.syncProviderVoices(updateExisting, provider);

        // Return sync results
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("syncResults", syncResults);
        response.put("timestamp", new Date());
        return response;
    }

    // Removed /list and /my-voices endpoints as they are now combined in the root endpoint

    /**
     * Create a new voice
     *
     * @param name Voice name
     * @param description Voice description
     * @param file Audio files for voice cloning
     * @return Created voice data
     */
    @PostMapping("")
    // @PrintLog
    public VoiceView createVoice(
            @RequestParam("file") MultipartFile file,
            @RequestParam("name") String name,
            @RequestParam("description") String description)
            throws IOException {

        User currentUser = userApiService.getCurrentUser();
        long userId = currentUser.getId();
        long orgId = 0;
        Voice createdVoice = voiceService.createVoice(name, description, List.of(file), userId, orgId);
        return VoiceView.fromVoice(createdVoice);
    }

    /**
     * Update voice details
     *
     * @param voiceId Voice ID
     * @param request Update request containing name, description, and gender
     * @return Updated voice data
     */
    @PutMapping("/{voiceId}")
    @PrintLog
    public VoiceView updateVoice(
            @PathVariable("voiceId") Long voiceId, @Valid @RequestBody UpdateVoiceRequest request) {

        User currentUser = userApiService.getCurrentUser();
        Voice updatedVoice = voiceService.updateVoice(
                voiceId, request.getName(), request.getDescription(), request.getGender(), currentUser);
        return VoiceView.fromVoice(updatedVoice);
    }

    /**
     * Update voice gender only
     *
     * @param voiceId Voice ID
     * @param gender New gender value (male or female)
     * @return Updated voice data
     */
    @PatchMapping("/{voiceId}/gender")
    @PrintLog
    public VoiceView updateVoiceGender(
            @PathVariable("voiceId") Long voiceId,
            @RequestParam("gender")
                    @Pattern(regexp = "^(male|female)$", message = "Gender must be either 'male' or 'female'")
                    String gender) {

        User currentUser = userApiService.getCurrentUser();
        Voice updatedVoice = voiceService.updateVoiceGender(voiceId, gender, currentUser);
        return VoiceView.fromVoice(updatedVoice);
    }

    /**
     * Delete a voice by ID
     * Currently only implemented for ElevenLabs provider
     *
     * @param voiceId Internal ID of the voice to delete
     * @return Response with success or error message
     */
    @DeleteMapping("/{voiceId}")
    @PrintLog
    public IDOnlyResponse deleteVoice(@PathVariable("voiceId") Long voiceId) {
        User currentUser = userApiService.getCurrentUser();
        boolean success = voiceService.deleteVoice(voiceId, currentUser.getId());

        if (!success) {
            log.warn("Failed to delete voice with ID: {}", voiceId);
        }

        return IDOnlyResponse.create(voiceId);
    }

    /**
     * Detect the language of a text string using Google Cloud Translation API
     * This endpoint is only available in non-production environments
     *
     * @param text The text to analyze for language detection
     * @return Map containing the detected language code
     */
    @PostMapping("/detect-language")
    @PrintLog
    @ConditionalOnExpression("!${zeus.env}'.equalsIgnoreCase('prod')")
    public Map<String, Object> detectLanguage(@RequestParam("text") String text) {
        String detectedLanguage = voiceService.detectLanguage(text);

        Map<String, Object> response = new LinkedHashMap<>();
        response.put("text", text.length() > 100 ? text.substring(0, 100) + "..." : text);
        response.put("detectedLanguage", detectedLanguage);
        response.put("timestamp", new Date());

        return response;
    }
}
