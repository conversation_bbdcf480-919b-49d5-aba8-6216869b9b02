package co.sandai.zeus.api.user;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.api.user.view.*;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.EmptySuccessResponse;
import co.sandai.zeus.domain.user.service.UserService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/user")
public class SignupController {

    public final UserApiService userApiService;

    public SignupController(UserApiService userApiService, UserService userService) {
        this.userApiService = userApiService;
    }

    @PostMapping("/login")
    @PrintLog
    public UserProfile login(@Valid @RequestBody UserEmailSignInRequest data, HttpServletResponse response) {
        return userApiService.signInWithEmail(data, response);
    }

    @PostMapping("/signup")
    @PrintLog
    public UserProfile signup(@Valid @RequestBody UserEmailSignupRequest data, HttpServletResponse response) {
        return userApiService.signupWithEmail(data, response);
    }

    @PostMapping("/send-reset-password-email")
    public EmptySuccessResponse sendResetPasswordEmail(@Valid @RequestBody SendResetPasswordEmailRequest data) {
        userApiService.sendResetPasswordEmail(data);
        return EmptySuccessResponse.builder().build();
    }

    @PutMapping("/reset-password")
    public EmptySuccessResponse resetPassword(@Valid @RequestBody UserResetPasswordRequest data) {
        userApiService.resetPassword(data);
        return EmptySuccessResponse.builder().build();
    }

    @PostMapping("/verify-email")
    @PrintLog
    public UserProfile verifyEmail(@Valid @RequestBody UserEmailVerifyRequest data, HttpServletResponse response) {
        return userApiService.verifyEmail(data, response);
    }

    @PostMapping("/send-verify-email")
    @PrintLog
    public UserProfile sendVerifyEmail() {
        return userApiService.sendVerifyEmail();
    }
}
