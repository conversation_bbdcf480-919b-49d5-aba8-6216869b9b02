package co.sandai.zeus.api.auth;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.dao.UserSource;
import co.sandai.zeus.domain.user.service.UserService;
import co.sandai.zeus.infra.oauth2.OAuth2Service;
import co.sandai.zeus.infra.oauth2.OAuth2UserInfo;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * Manual OAuth2 implementation to replace Spring Security OAuth2
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class OAuth2Controller {

    @Autowired
    private OAuth2Service oAuth2Service;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private UserService userService;

    @Value("${zeus.auth.login-success-url:/app}")
    private String loginSuccessPage;

    @Value("${zeus.auth.cookie.name}")
    private String cookieName;

    @Value("${zeus.auth.logout-success-url:/}")
    private String logoutSuccessPage;

    @Value("${zeus.auth.login-error-url:/app/login?error=oauth}")
    private String loginErrorPage;

    /**
     * Initiate OAuth2 login for Google
     */
    @GetMapping("/oauth2/google")
    public void googleLogin(HttpServletResponse response) throws IOException {
        String authUrl = oAuth2Service.getGoogleAuthorizationUrl();
        response.sendRedirect(authUrl);
    }

    /**
     * Initiate OAuth2 login for Discord
     */
    @GetMapping("/oauth2/discord")
    public void discordLogin(HttpServletResponse response) throws IOException {
        String authUrl = oAuth2Service.getDiscordAuthorizationUrl();
        response.sendRedirect(authUrl);
    }

    /**
     * Handle OAuth2 callback for Google
     */
    @GetMapping("/oauth2/callback/google")
    public void googleCallback(
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "error", required = false) String error,
            HttpServletResponse response)
            throws IOException {

        // Handle OAuth2 error responses (user denied access, etc.)
        if (error != null) {
            log.warn("Google OAuth2 authorization failed: error={}", error);
            response.sendRedirect(loginErrorPage);
            return;
        }

        // Handle missing code parameter
        if (code == null || code.trim().isEmpty()) {
            log.warn("Google OAuth2 callback received without code parameter");
            response.sendRedirect(loginErrorPage);
            return;
        }

        try {
            OAuth2UserInfo userInfo = oAuth2Service.handleGoogleCallback(code);
            ProcessOAuth2UserRet ret = processOAuth2User(userInfo, UserSource.Google);
            Cookie cookie = userApiService.signIn(ret.user);
            response.addCookie(cookie);
            response.sendRedirect(buildLoginSuccessUrl(ret));
        } catch (Exception e) {
            log.error("Google OAuth2 callback failed", e);
            response.sendRedirect(loginErrorPage);
        }
    }

    /**
     * Handle OAuth2 callback for Discord
     */
    @GetMapping("/oauth2/callback/discord")
    public void discordCallback(
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "error", required = false) String error,
            HttpServletResponse response)
            throws IOException {

        // Handle OAuth2 error responses (user denied access, etc.)
        if (error != null) {
            log.warn("Discord OAuth2 authorization failed: error={}", error);
            response.sendRedirect(loginErrorPage);
            return;
        }

        // Handle missing code parameter
        if (code == null || code.trim().isEmpty()) {
            log.warn("Discord OAuth2 callback received without code parameter");
            response.sendRedirect(loginErrorPage);
            return;
        }

        try {
            OAuth2UserInfo userInfo = oAuth2Service.handleDiscordCallback(code);
            ProcessOAuth2UserRet ret = processOAuth2User(userInfo, UserSource.Discord);
            Cookie cookie = userApiService.signIn(ret.user);
            response.addCookie(cookie);

            response.sendRedirect(loginSuccessPage);
        } catch (Exception e) {
            log.error("Discord OAuth2 callback failed", e);
            response.sendRedirect(loginErrorPage);
        }
    }

    /**
     * Handle logout
     */
    @GetMapping("/logout")
    public void logout(HttpServletResponse response) throws IOException {
        // Clear the authentication cookie
        Cookie cookie = new Cookie(cookieName, "");
        cookie.setMaxAge(0);
        cookie.setPath("/");
        response.addCookie(cookie);
        response.sendRedirect(logoutSuccessPage);
    }

    @Data
    @Builder
    static class ProcessOAuth2UserRet {
        private boolean newUser;
        private User user;
    }

    private ProcessOAuth2UserRet processOAuth2User(OAuth2UserInfo userInfo, UserSource source) {
        String email = userInfo.getEmail();
        String nickname = userInfo.getName();
        String avatarUrl = userInfo.getAvatarUrl();

        User user = userService.getUserByEmail(email);
        boolean isNewUser = Objects.isNull(user);
        if (isNewUser) {
            // Create new user
            user = userApiService.joinOAuthUser(email, source, nickname, avatarUrl);
        } else {
            // Update avatar if needed
            userApiService.updateUserAvatarIfNeeded(user, avatarUrl);
        }

        return ProcessOAuth2UserRet.builder().newUser(isNewUser).user(user).build();
    }

    private String buildLoginSuccessUrl(ProcessOAuth2UserRet ret) {
        return loginSuccessPage;
    }
}
