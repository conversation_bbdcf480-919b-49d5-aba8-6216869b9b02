package co.sandai.zeus.api.asset.view;

import co.sandai.zeus.api.task.generation.view.GenerationDetailResponseVO;
import co.sandai.zeus.api.task.service.GenerationApiService;
import co.sandai.zeus.config.SpringContext;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetMediaType;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.favorite.service.AssetFavoriteService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Builder
@Getter
@Setter
@Slf4j
public class AssetView {
    public long id;
    public int width;
    public int height;
    public float duration;
    public String url;
    public String previewUrl;
    public AssetMediaType mediaType;
    public AssetSource source;
    public long generationId;
    public GenerationDetailResponseVO generation;
    public String filename;

    public boolean favorite;
    public Long voiceId;

    public static AssetView fromAsset(Asset asset, boolean includeGenerationDetail) {
        if (asset == null) {
            return null;
        }
        List<AssetView> views = fromAssets(List.of(asset), 0L, includeGenerationDetail);
        return views.isEmpty() ? null : views.getFirst();
    }

    public static List<AssetView> fromAssets(List<Asset> assets, long userId, boolean includeGenerationDetail) {
        if (assets == null || assets.isEmpty()) {
            return List.of();
        }

        AssetService assetService = SpringContext.getBean(AssetService.class);
        GenerationApiService generationApiService = SpringContext.getBean(GenerationApiService.class);

        // 1. 批量获取所有需要的封面资源
        List<Long> posterAssetIds = assets.stream()
                .map(Asset::getPosterAssetId)
                .filter(posterAssetId -> posterAssetId > 0)
                .distinct()
                .toList();

        final Map<Long, Asset> posterAssetsMap;
        if (!posterAssetIds.isEmpty()) {
            List<Asset> posters = assetService.getAssetsByIds(posterAssetIds);
            posterAssetsMap = posters.stream().collect(Collectors.toMap(Asset::getId, asset -> asset));
        } else {
            posterAssetsMap = new HashMap<>();
        }

        // 2. 收集所有需要获取URL的资源
        List<Asset> allAssets = new ArrayList<>(assets);
        allAssets.addAll(posterAssetsMap.values());

        // 3. 获取所有资源的URL
        Map<Long, String> assetUrlMap = assetService.getAssetPublicUrlByIds(allAssets, 60);

        // 4. 获取生成详情
        Map<Long, GenerationDetailResponseVO> generationDetailResponseVOMap = new HashMap<>();
        if (includeGenerationDetail) {
            for (Asset asset : assets) {
                if (asset.getTaskId() == 0) {
                    continue;
                }
                try {
                    GenerationDetailResponseVO detail =
                            generationApiService.getGenerationDetail(asset.getTaskId(), true);
                    generationDetailResponseVOMap.put(asset.getId(), detail);
                } catch (Exception e) {
                    log.warn("Failed to get generation detail for asset {}: {}", asset.getId(), e.getMessage());
                }
            }
        }

        // 5. 获取收藏状态
        final Map<Long, Boolean> favoriteStatusMap;
        if (userId > 0) {
            AssetFavoriteService assetFavoriteService = SpringContext.getBean(AssetFavoriteService.class);
            List<Long> assetIds = assets.stream().map(asset -> asset.id).toList();
            favoriteStatusMap = assetFavoriteService.getFavoriteAssetStatusMap(userId, assetIds);
        } else {
            favoriteStatusMap = new HashMap<>();
        }

        // 6. 构建AssetView列表
        return assets.stream()
                .map(asset -> AssetView.builder()
                        .id(asset.id)
                        .mediaType(asset.mediaType)
                        .source(asset.source)
                        .width(asset.width)
                        .height(asset.height)
                        .generationId(asset.getTaskId())
                        .duration(asset.getDuration())
                        .filename(asset.getFilename())
                        .url(assetUrlMap.getOrDefault(asset.id, ""))
                        .generation(generationDetailResponseVOMap.getOrDefault(asset.id, null))
                        .previewUrl(
                                asset.getPosterAssetId() > 0 && posterAssetsMap.containsKey(asset.getPosterAssetId())
                                        ? assetUrlMap.getOrDefault(asset.getPosterAssetId(), "")
                                        : null)
                        .favorite(userId > 0 && favoriteStatusMap.getOrDefault(asset.id, false))
                        .build())
                .toList();
    }
}
