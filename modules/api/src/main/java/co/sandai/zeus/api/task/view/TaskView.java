package co.sandai.zeus.api.task.view;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.config.SpringContext;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.common.CropArea;
import co.sandai.zeus.domain.favorite.service.TaskFavoriteService;
import co.sandai.zeus.domain.share.service.ShareService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.dao.TaskType;
import co.sandai.zeus.domain.user.dao.User;
import java.util.Date;
import java.util.Objects;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TaskView {
    public long id;
    public long userId;
    public long duration;
    public long totalDuration;
    public long sourceVideoId;
    public long sourceImageId;
    public long resultVideoId;
    public long sourceVideoStartTimestamp;
    public long sourceVideoEndTimestamp;
    public long seed;
    public int width;
    public int height;
    public int expectedChunkCount;
    public int completedChunkCount;
    public boolean enablePromptEnhancement;
    public boolean enableAutoGenFirstFrame;
    public boolean isFavorite;
    public boolean isPublic;
    public String prompt;
    public String model;
    public String aspectRatio;
    public String underlyingModel;
    public String resultVideoUrl;
    public String resultVideoPosterUrl;
    public String sourceVideoUrl;
    public String sourceVideoPosterUrl;
    public String sourceImageUrl;
    public String enhancedPrompt;
    public TaskStatus status;
    public TaskType type;
    public CropArea cropArea;
    public Date createTime;

    // TODO: 生产环境中不应该返回，应该作为 Debug 信息的一部分
    public String firstFramePrompt;
    public String tSchedulerFunc;
    public String tSchedulerArgs;
    public TaskExtraInferArgs extraInferArgs;

    // TODO: DB/网络操作有点多，可能会影响列表返回的性能，按需优化
    public static TaskView fromTask(Task task) {
        UserApiService userApiService = SpringContext.getBean(UserApiService.class);
        AssetService assetService = SpringContext.getBean(AssetService.class);
        TaskFavoriteService taskFavoriteService = SpringContext.getBean(TaskFavoriteService.class);
        ShareService shareService = SpringContext.getBean(ShareService.class);

        int expiresInMinutes = 60;

        String posterUrl = assetService.getAssetPublicUrlById(task.getResultVideoPosterId(), expiresInMinutes);

        boolean isFavorite = false;
        boolean isPublic = false;

        User user = userApiService.getCurrentUser();
        if (Objects.nonNull(user)) {
            isFavorite = taskFavoriteService.isFavoriteTask(user.getId(), task.getId());
        }

        isPublic = shareService.isTaskSharedTask(task);

        String resultVideoUrl = "";
        if (task.getResultVideoId() > 0) {
            resultVideoUrl = assetService.getAssetPublicUrlById(task.getResultVideoId(), expiresInMinutes);
        }

        String sourceImageUrl = "";
        if (task.getSourceImageId() > 0) {
            sourceImageUrl = assetService.getAssetPublicUrlById(task.getSourceImageId(), expiresInMinutes);
        }

        String sourceVideoUrl = "";
        if (task.getSourceVideoId() > 0) {
            sourceVideoUrl = assetService.getAssetPublicUrlById(task.getSourceVideoId(), expiresInMinutes);
        }
        return TaskView.builder()
                .id(task.getId())
                .userId(task.getUserId())
                .resultVideoPosterUrl(posterUrl)
                .resultVideoUrl(resultVideoUrl)
                .prompt(task.getPrompt())
                .status(task.getStatus())
                .tSchedulerArgs(task.getTSchedulerArgs())
                .tSchedulerFunc(task.getTSchedulerFunc())
                .aspectRatio(task.getAspectRatio())
                .seed(task.getSeed())
                .model(task.getModel())
                .underlyingModel(task.getUnderlyingModel())
                .duration((long) task.getDuration())
                .type(task.getType())
                .sourceImageUrl(sourceImageUrl)
                .sourceImageId(task.getSourceImageId())
                .sourceVideoUrl(sourceVideoUrl)
                .sourceVideoId(task.getSourceVideoId())
                .resultVideoId(task.getResultVideoId())
                .expectedChunkCount(task.getChunkCount())
                .completedChunkCount(task.getCompletedChunkCount())
                .sourceVideoStartTimestamp(task.getSourceVideoStartTimestamp())
                .sourceVideoEndTimestamp(task.getSourceVideoEndTimestamp())
                .cropArea(task.getCropArea())
                .extraInferArgs(task.getExtraInferArgs())
                .width(task.getWidth())
                .height(task.getHeight())
                .createTime(task.getCreateTime())
                .totalDuration(task.getTotalDuration())
                .isFavorite(isFavorite)
                .isPublic(isPublic)
                .enablePromptEnhancement(task.isEnablePromptEnhancement())
                .enableAutoGenFirstFrame(task.isEnableAutoGenFirstFrame())
                .enhancedPrompt(task.getEnhancedPrompt())
                .firstFramePrompt(task.getFirstFramePrompt())
                .build();
    }
}
