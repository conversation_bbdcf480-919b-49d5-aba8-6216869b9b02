package co.sandai.zeus.api.asset;

import co.sandai.zeus.api.asset.view.AssetView;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.helper.FilePathHelper;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.asset.dto.AssetFilterParam;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.infra.Green;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/v1/assets")
public class AssetController {
    @Autowired
    private AssetService assetService;

    @Autowired
    private UserApiService userApiService;

    @GetMapping("")
    @PrintLog
    public ListResponseByPage<AssetView> listAssets(
            @RequestParam(value = "sources", defaultValue = "") AssetFilterParam.AssetFilterParamSource[] sources,
            @RequestParam(value = "types", defaultValue = "") AssetFilterParam.AssetFilterParamType[] types,
            @RequestParam(value = "search", defaultValue = "") String search,
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        User user = userApiService.getCurrentUser();
        // 支持非登录用户访问，使用userId = 0
        long userId = user != null ? user.getId() : 0L;

        // 非登录用户的访问限制
        if (user == null) {
            // 如果没有指定sources，默认查询Featured资产
            if (sources.length == 0) {
                sources =
                        new AssetFilterParam.AssetFilterParamSource[] {AssetFilterParam.AssetFilterParamSource.Featured
                        };
            } else {
                // 如果指定了sources，只允许Featured和HumanFeatured
                for (AssetFilterParam.AssetFilterParamSource source : sources) {
                    if (source != AssetFilterParam.AssetFilterParamSource.Featured
                            && source != AssetFilterParam.AssetFilterParamSource.HumanFeatured) {
                        throw ZeusServiceException.forbidden("Anonymous users can only access Featured assets");
                    }
                }
            }
        }

        AssetFilterParam param =
                AssetFilterParam.builder().types(types).sources(sources).build();
        List<Asset> assets = assetService.filterUserManageableAssets(userId, param, offset, limit);
        List<AssetView> assetViews = AssetView.fromAssets(assets, userId, false);

        int total = 0;
        if (!assets.isEmpty()) {
            total = assetService.countUserManageableAssets(userId, param);
        }

        return new ListResponseByPage<>(assetViews, total, offset, limit);
    }

    @GetMapping("/mget")
    @PrintLog
    public Map<Long, AssetView> mgetAssets(@RequestParam("ids") List<Long> ids) {
        // 开放接口，限制下数量
        if (ids.size() > 10) {
            throw ZeusServiceException.badRequest("Can not get assets with more than 10 ids");
        }
        User user = userApiService.getCurrentUser();
        List<Asset> assets = assetService.getAssetsByIds(ids);
        long userId = Objects.nonNull(user) ? user.getId() : 0;
        List<AssetView> assetViews = AssetView.fromAssets(assets, userId, true);
        return assetViews.stream().collect(Collectors.toMap(view -> view.id, view -> view));
    }

    @PostMapping("")
    @PrintLog
    public AssetView uploadAsset(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "fileName", defaultValue = "") String fileName,
            @RequestParam(value = "source", defaultValue = "Upload") AssetSource source,
            @RequestParam(value = "isArtifacts", defaultValue = "true") boolean isArtifacts)
            throws IOException {
        User user = userApiService.getCurrentUser();
        String originalFilename = fileName;
        if (originalFilename == null || originalFilename.isEmpty()) {
            originalFilename = file.getOriginalFilename();
        }
        String ext = null;
        if (originalFilename != null) {
            ext = FilePathHelper.getFileExtName(originalFilename);
        }
        if (ext == null) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters);
        }

        InputStream inputStream = file.getInputStream();
        byte[] data = inputStream.readAllBytes();
        Asset asset = assetService.addUploadAsset(data, user.getId(), 0L, ext, originalFilename, source);
        return AssetView.fromAsset(asset, false);
    }

    @DeleteMapping("/{id}")
    @PrintLog
    public IDOnlyResponse deleteAssetById(@PathVariable("id") Long id) {
        User user = userApiService.getCurrentUser();
        Asset asset = assetService.getAssetById(id);
        if (asset == null) {
            throw ZeusServiceException.notFound("Asset not found");
        }

        if (!Objects.equals(asset.getUserId(), user.getId())) {
            throw ZeusServiceException.forbidden("You are not allowed to delete this asset");
        }

        assetService.deleteAssetById(id);
        return IDOnlyResponse.create(id);
    }

    @PostMapping("/video-moderation-callback")
    @PrintLog
    public IDOnlyResponse videoModerationCallback(Green.ModerateVideoCallbackRequest request) {
        Asset asset = assetService.parseVideoModerationResult(request);
        return IDOnlyResponse.create(asset.getId());
    }
}
