package co.sandai.zeus.api.credit;

import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.credit.UserCreditService;
import co.sandai.zeus.domain.credit.dao.UserCreditDO;
import co.sandai.zeus.domain.credit.enums.CreditTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处理subscription\purchased\bonus\operation类型的expire
 *
 * 每小时执行一次
 */
@Component
@Slf4j(topic = "schedulerx")
public class CreditExpireJob extends JavaProcessor {

    @Resource
    private UserCreditService userCreditService;

    public static final int BATCH_SIZE = 20;

    @Override
    @PrintLog
    public ProcessResult process(JobContext context) throws Exception {
        LogUtil.infof(log, "CreditExpireJob process start");
        List<CreditTypeEnum> creditTypeEnums = Arrays.asList(
                CreditTypeEnum.SUBSCRIPTION, CreditTypeEnum.PURCHASED, CreditTypeEnum.BONUS, CreditTypeEnum.OPERATION);
        // todo panhong 2025/4/11  对于年付费的credit，可能会有重复expire的情况（发生概率很低，除非定时任务执行超过半小时），这里需要把事务隔离级别&锁调整下
        List<UserCreditDO> userCredits = userCreditService.queryExpiringUserCredit(null, BATCH_SIZE, creditTypeEnums);
        while (!userCredits.isEmpty()) {
            LogUtil.infof(
                    log,
                    "CreditExpireJob process, size={0}, userCredits={1}",
                    userCredits.size(),
                    JSON.toJSONString(userCredits));
            userCreditService.batchExpireCredit(userCredits);

            Long lowerId =
                    userCredits.stream().mapToLong(UserCreditDO::getId).max().getAsLong();
            userCredits = userCreditService.queryExpiringUserCredit(lowerId, BATCH_SIZE, creditTypeEnums);
        }
        return new ProcessResult(true, "success");
    }
}
