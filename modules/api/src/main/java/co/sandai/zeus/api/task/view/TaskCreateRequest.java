package co.sandai.zeus.api.task.view;

import co.sandai.zeus.domain.common.CropArea;
import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

public class TaskCreateRequest {
    public String prompt = "";

    @Min(value = 3, message = "duration should be not less than 3")
    @NotNull(message = "duration is required")
    public Long duration;

    public String model = "mvp-internal-test";

    public Long sourceVideoId;
    public Long sourceVideoStartTimestamp = 0L;
    public Long sourceVideoEndTimestamp = 0L;

    public Boolean enablePromptEnhancement = false;
    public Boolean enableAutoGenFirstFrame = false;

    public Long sourceImageId;
    public String aspectRatio;
    public Long seed = 0L;
    public String tSchedulerFunc = "";
    public String tSchedulerArgs = "";
    public CropArea cropArea = null;
    public TaskExtraInferArgs extraInferArgs = null;
}
