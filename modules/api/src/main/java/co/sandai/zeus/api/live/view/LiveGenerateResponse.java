package co.sandai.zeus.api.live.view;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * Response model for live generation.
 */
@Data
public class LiveGenerateResponse {
    private Long streamId;
    private LiveModeStatus status; // User's current status for live mode

    @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
    private LocalDateTime nextAvailableTime; // When user can use live mode again if quota is exhausted

    private Boolean submitted; // Whether the task has been truly submitted

    @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
    private LocalDateTime streamEndTime;

    @JsonSerialize(using = UtcLocalDateTimeSerializer.class)
    private LocalDateTime streamStartTime; // When the stream started (first task start time)

    private List<Long> taskIds;
}
