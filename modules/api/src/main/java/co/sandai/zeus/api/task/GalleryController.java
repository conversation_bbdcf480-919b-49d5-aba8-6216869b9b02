package co.sandai.zeus.api.task;

import co.sandai.zeus.api.task.view.TaskView;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.share.service.ShareService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import co.sandai.zeus.domain.task.dao.TaskStatus;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/gallery")
public class GalleryController {

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private UserService userService;

    @GetMapping("tasks")
    @PrintLog
    public ListResponseByPage<TaskView> getTasks(
            @RequestParam(value = "all", defaultValue = "1") boolean all,
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        User user = userApiService.getCurrentUser();

        if (all) {
            if (Objects.isNull(user) || !userService.isInAdminWhiteList(user)) {
                throw ZeusServiceException.forbidden("have not permission");
            }
            int taskCount = taskService.getTaskCountByStatus(TaskStatus.Success);
            List<Task> tasks = taskService.getTasksByStatus(
                    TaskStatus.Success, offset, limit, Arrays.asList(TaskSourceEnum.NORMAL));
            return new ListResponseByPage<>(
                    tasks.stream().map(TaskView::fromTask).toList(), taskCount, offset, limit);
        }

        int taskCount = taskService.getTaskCountByStatus(TaskStatus.Success);
        List<Task> tasks =
                taskService.getTasksByStatus(TaskStatus.Success, offset, limit, Arrays.asList(TaskSourceEnum.NORMAL));
        return new ListResponseByPage<>(tasks.stream().map(TaskView::fromTask).toList(), taskCount, offset, limit);
    }
}
