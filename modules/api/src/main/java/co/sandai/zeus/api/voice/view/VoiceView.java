package co.sandai.zeus.api.voice.view;

import co.sandai.zeus.domain.voice.dao.Voice;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VoiceView {
    private Long id;
    private String name;
    private String description;
    // private String language;
    // private String previewUrl;
    private List<Language> languages;
    private boolean isCloned;
    private String gender; // Voice gender

    /**
     * Create VoiceView directly from Voice entity
     * @param voice Voice entity
     * @return VoiceView
     */
    public static VoiceView fromVoice(Voice voice) {
        if (voice == null) {
            return null;
        }

        List<Language> parsedLanguages =
                parseVerifiedLanguages(voice.getLanguage(), voice.getPreviewUrl(), voice.getVerifiedLanguages());

        return VoiceView.builder()
                .id(voice.getId())
                .name(voice.getName())
                .description(voice.getDescription())
                .languages(parsedLanguages)
                .isCloned(voice.isCloned())
                .gender(voice.getGender())
                .build();
    }

    /**
     * Parses verified languages JSON string into a list of VerifiedLanguage objects
     * Deduplicates entries based on language and preview URL while preserving order
     *
     * @param verifiedLanguagesJson JSON string from Voice entity
     * @return List of deduplicated VerifiedLanguage objects
     */
    private static List<Language> parseVerifiedLanguages(
            String mainLanguage, String mainPreviewUrl, String verifiedLanguagesJson) {

        List<Language> result = new ArrayList<>();
        Set<String> uniqueKeys = new HashSet<>();
        if (mainPreviewUrl != null) {
            result.add(new Language(mainLanguage, mainPreviewUrl));
            uniqueKeys.add(mainLanguage + "_" + mainPreviewUrl);
        }
        if (verifiedLanguagesJson == null || verifiedLanguagesJson.isEmpty()) {
            return result;
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(verifiedLanguagesJson);

            if (rootNode.isArray()) {
                for (JsonNode node : rootNode) {
                    String language = node.path("language").asText();
                    String previewUrl = node.path("preview_url").asText();

                    // Create unique key for deduplication
                    String uniqueKey = language + "_" + previewUrl;

                    // Only add if this combination hasn't been seen yet
                    if (!uniqueKeys.contains(uniqueKey) && !language.isEmpty() && !previewUrl.isEmpty()) {
                        uniqueKeys.add(uniqueKey);
                        result.add(new Language(language, previewUrl));
                    }
                }
            }
        } catch (JsonProcessingException e) {
            // Log error or handle exception as appropriate
            System.err.println("Error parsing verified languages: " + e.getMessage());
        }

        return result;
    }

    /**
     * Represents a language supported by a voice with its preview URL
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Language {
        private String language;
        private String previewUrl;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Language that = (Language) o;
            return Objects.equals(language, that.language) && Objects.equals(previewUrl, that.previewUrl);
        }

        @Override
        public int hashCode() {
            return Objects.hash(language, previewUrl);
        }
    }
}
