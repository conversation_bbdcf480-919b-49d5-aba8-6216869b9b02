package co.sandai.zeus.api.report;

import co.sandai.zeus.api.report.request.ReportCreateRequest;
import co.sandai.zeus.api.report.view.ReportTagsVo;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.report.config.ReportReasonConfig;
import co.sandai.zeus.domain.report.dao.ReportDO;
import co.sandai.zeus.domain.report.service.ReportService;
import co.sandai.zeus.domain.user.dao.User;
import jakarta.validation.Valid;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/reports")
public class ReportController {

    @Autowired
    private ReportReasonConfig reportReasonConfig;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private ReportService reportService;

    @GetMapping("/tags")
    @PrintLog
    public ReportTagsVo getReportReasons() {
        ReportTagsVo vo = new ReportTagsVo();
        vo.setCategories(reportReasonConfig.getCategories().stream()
                .map(category -> {
                    ReportTagsVo.ReasonCategoryVo categoryVo = new ReportTagsVo.ReasonCategoryVo();
                    //                    categoryVo.setType(category.getType());
                    categoryVo.setName(category.getName());
                    categoryVo.setTags(category.getTags().stream()
                            .map(reason -> {
                                ReportTagsVo.TagVo reasonVo = new ReportTagsVo.TagVo();
                                reasonVo.setCode(reason.getCode());
                                reasonVo.setDescription(reason.getDescription());
                                return reasonVo;
                            })
                            .collect(Collectors.toList()));
                    return categoryVo;
                })
                .collect(Collectors.toList()));
        return vo;
    }

    @PostMapping("")
    @PrintLog
    public void createReport(@Valid @RequestBody ReportCreateRequest request) {
        User currentUser = userApiService.getCurrentUser();

        ReportDO report = ReportDO.builder()
                .tags(String.join(",", request.getTags()))
                .comment(request.getComment())
                .targetId(request.getTargetId())
                .targetType(request.getTargetType())
                .userId(currentUser.getId())
                .build();
        reportService.createReport(report);
    }
}
