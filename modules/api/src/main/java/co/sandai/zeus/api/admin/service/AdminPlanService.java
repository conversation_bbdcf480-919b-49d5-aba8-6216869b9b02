package co.sandai.zeus.api.admin.service;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.payment.PaySuccessEvent;
import co.sandai.zeus.domain.payment.dao.OrderDO;
import co.sandai.zeus.domain.payment.dao.OrderDao;
import co.sandai.zeus.domain.plan.UserPlanService;
import co.sandai.zeus.domain.plan.config.PlanConfigService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import co.sandai.zeus.domain.plan.model.Plan;
import co.sandai.zeus.domain.plan.model.PriceModel;
import co.sandai.zeus.domain.plan.model.enums.PlanStatusEnum;
import co.sandai.zeus.domain.user.dao.User;
import co.sandai.zeus.domain.user.service.UserService;
import co.sandai.zeus.infra.IDGenerator;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class AdminPlanService {

    @Autowired
    private PlanConfigService planConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private OrderDao orderDao;

    @Autowired
    private IDGenerator idGenerator;

    @Autowired
    private UserPlanService userPlanService;

    public static class CurrentPlanInfo {
        public String planCode;
        public String planName;
        public String planType;
        public String startTime;
        public String expireTime;

        public CurrentPlanInfo(String planCode, String planName, String planType, String startTime, String expireTime) {
            this.planCode = planCode;
            this.planName = planName;
            this.planType = planType;
            this.startTime = startTime;
            this.expireTime = expireTime;
        }
    }

    public static class GrantMembershipResult {
        public boolean success;
        public String message;
        public Long orderId;
        public Long userId;
        public String planCode;
        public String currentPlanCode;
        public String currentPlanExpireTime;
        public String grantTime;

        public GrantMembershipResult(
                boolean success,
                String message,
                Long userId,
                String planCode,
                String currentPlanCode,
                String currentPlanExpireTime,
                String grantTime) {
            this.success = success;
            this.message = message;

            this.userId = userId;
            this.planCode = planCode;
            this.currentPlanCode = currentPlanCode;
            this.currentPlanExpireTime = currentPlanExpireTime;
            this.grantTime = grantTime;
        }

        public static GrantMembershipResult success(
                Long userId, String planCode, String currentPlanCode, String currentPlanExpireTime, String grantTime) {
            return new GrantMembershipResult(
                    true, "success", userId, planCode, currentPlanCode, currentPlanExpireTime, grantTime);
        }

        public static GrantMembershipResult fail(
                String message,
                Long userId,
                String planCode,
                String currentPlanCode,
                String currentPlanExpireTime,
                String grantTime) {
            return new GrantMembershipResult(
                    false, message, userId, planCode, currentPlanCode, currentPlanExpireTime, grantTime);
        }
    }

    /**
     * 管理员免费给用户分配会员计划
     * 通过发布PaySuccessEvent事件来复用现有的计划分配逻辑
     *
     * 特性：
     * - 智能幂等：如果用户当前没有有效计划，允许分配；如果有有效计划，需要force=true才能强制分配
     * - 适用场景：用户补偿、一次性奖励、会员续期等
     * - 平台来源：使用ADMIN标识，区分Stripe付费
     *
     * @param userId 目标用户ID
     * @param planCode 计划代码
     * @param force 是否强制分配（true=即使用户有有效计划也强制分配，false=智能幂等）
     */
    @Transactional(rollbackFor = Exception.class)
    public GrantMembershipResult grantFreeMembership(Long userId, String planCode) {
        // 验证用户是否存在
        User user = userService.getUserById(userId);
        if (user == null) {
            throw ZeusServiceException.badRequest("User not found with ID: " + userId);
        }

        // 验证计划是否存在且活跃
        Plan plan = planConfigService.getPlanByCode(planCode, PlanStatusEnum.ACTIVE);
        if (plan == null) {
            throw ZeusServiceException.badRequest("Plan not found or inactive: " + planCode);
        }

        // 获取该计划对应的价格模型，用于构建事件
        PriceModel priceModel = planConfigService.getPriceModelOfPlan(planCode, "USD");
        if (priceModel == null) {
            throw ZeusServiceException.badRequest("Price model not found for plan: " + planCode);
        }

        LocalDateTime now = TimeUtil.utcTime();

        // 只允许free plan用户
        UserPlanDO currentPlan = userPlanService.queryNotExpiredUserPlan(userId);
        String currentPlanCode = currentPlan == null ? null : currentPlan.getPlanCode();
        String currentPlanExpireTime =
                currentPlan == null ? null : currentPlan.getExpireTime().toString();
        String grantTime = now.toString();
        if (currentPlan != null) {
            Plan currentPlanMeta = planConfigService.getPlanByCode(currentPlan.getPlanCode(), PlanStatusEnum.ACTIVE);
            String planName = currentPlanMeta != null ? currentPlanMeta.getName() : "";
            String planType = currentPlanMeta != null && currentPlanMeta.getIntervalTypeEnum() != null
                    ? currentPlanMeta.getIntervalTypeEnum().name()
                    : "";
            String startTime = currentPlan.getCreateTime() != null
                    ? currentPlan.getCreateTime().toString()
                    : "";
            String planDetail = String.format(
                    "当前已有plan: code=%s, name=%s, type=%s, start=%s, end=%s",
                    currentPlan.getPlanCode(),
                    planName,
                    planType,
                    startTime,
                    currentPlan.getExpireTime() != null
                            ? currentPlan.getExpireTime().toString()
                            : "");
            log.info("User {} already has paid plan {}, skipping admin grant", userId, currentPlan.getPlanCode());
            return GrantMembershipResult.fail(
                    "用户当前不是免费计划，无法分配。" + planDetail,
                    userId,
                    planCode,
                    currentPlanCode,
                    currentPlanExpireTime,
                    grantTime);
        }

        // 生成requestId：同一用户同一计划每月只能分配一次
        String requestId = "admin_grant_" + userId + "_" + planCode + "_"
                + now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMM"));

        log.info(
                "Successfully granted free membership: userId={}, planCode={}, requestId={}",
                userId,
                planCode,
                requestId);

        PaySuccessEvent paySuccessEvent = new PaySuccessEvent(this);
        paySuccessEvent.setPriceId(priceModel.getOutPriceId());
        paySuccessEvent.setFrom(OrderDO.FromEnum.ADMIN_GRANT);
        paySuccessEvent.setUserId(userId);
        paySuccessEvent.setPayTime(now);
        paySuccessEvent.setRequestId(requestId);
        applicationEventPublisher.publishEvent(paySuccessEvent);
        return GrantMembershipResult.success(userId, planCode, currentPlanCode, currentPlanExpireTime, grantTime);
    }
}
