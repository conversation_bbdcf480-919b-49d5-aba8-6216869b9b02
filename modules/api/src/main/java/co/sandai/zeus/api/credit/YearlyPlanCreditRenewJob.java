package co.sandai.zeus.api.credit;

import co.sandai.zeus.api.credit.service.CreditApiService;
import co.sandai.zeus.common.log.LogUtil;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.plan.UserPlanService;
import co.sandai.zeus.domain.plan.dao.UserPlanDO;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 每小时执行一次
 */
@Component
@Slf4j(topic = "schedulerx")
public class YearlyPlanCreditRenewJob extends JavaProcessor {

    @Resource
    private UserPlanService userPlanService;

    @Resource
    private CreditApiService creditApiService;

    private static final int BATCH_SIZE = 20;

    /**
     * @param context
     * @return
     * @throws Exception
     */
    @Override
    @PrintLog
    public ProcessResult process(JobContext context) throws Exception {
        LogUtil.infof(log, "YearlyPlanCreditRenewJob process start");
        List<UserPlanDO> userPlans = userPlanService.queryYearlyRenewPlan(null, BATCH_SIZE);
        while (!userPlans.isEmpty()) {
            String userIdsStr = userPlans.stream()
                    .map(UserPlanDO::getUserId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(",", "[", "]"));
            LogUtil.infof(log, "YearlyPlanCreditRenewJob process, size={0}, userIds={1}", userPlans.size(), userIdsStr);
            creditApiService.renewYearlyPlanAndCredits(userPlans);

            Long lowerId = userPlans.stream().mapToLong(UserPlanDO::getId).max().getAsLong();
            userPlans = userPlanService.queryYearlyRenewPlan(lowerId, BATCH_SIZE);
        }
        return new ProcessResult(true);
    }
}
