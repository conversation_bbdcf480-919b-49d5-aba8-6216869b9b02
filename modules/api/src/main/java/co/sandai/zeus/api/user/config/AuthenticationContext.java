package co.sandai.zeus.api.user.config;

import co.sandai.zeus.domain.user.dao.User;

/**
 * Custom authentication context to replace Spring Security's SecurityContextHolder
 */
public class AuthenticationContext {

    private static final ThreadLocal<User> currentUser = new ThreadLocal<>();

    /**
     * Set the current authenticated user for this thread
     */
    public static void setCurrentUser(User user) {
        currentUser.set(user);
    }

    /**
     * Get the current authenticated user for this thread
     */
    public static User getCurrentUser() {
        return currentUser.get();
    }

    /**
     * Check if there is an authenticated user for this thread
     */
    public static boolean isAuthenticated() {
        return currentUser.get() != null;
    }

    /**
     * Clear the authentication context for this thread
     */
    public static void clear() {
        currentUser.remove();
    }
}
