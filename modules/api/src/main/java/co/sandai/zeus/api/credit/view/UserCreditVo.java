package co.sandai.zeus.api.credit.view;

import co.sandai.zeus.common.utils.TimeUtil;
import co.sandai.zeus.domain.credit.dao.UserCreditDO;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserCreditVo {

    private String creditAmount;

    private List<CreditDetailVo> creditDetailList;

    @Data
    public static class CreditDetailVo {

        private String creditAmount;

        private String creditType;

        /**
         * 到期时间，UTC时区
         */
        private String expireTime;

        public static CreditDetailVo convertFromModel(UserCreditDO userCreditDO) {
            CreditDetailVo creditDetailVo = new CreditDetailVo();
            creditDetailVo.setCreditAmount(String.valueOf(userCreditDO.getAmount()));
            creditDetailVo.setCreditType(userCreditDO.getCreditType());
            creditDetailVo.setExpireTime(TimeUtil.format(userCreditDO.getExpireTime()));
            return creditDetailVo;
        }
    }
}
