package co.sandai.zeus.api.platform;

import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.domain.platform.organization.OrganizationService;
import co.sandai.zeus.domain.platform.organization.dao.Organization;
import co.sandai.zeus.domain.user.dao.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PlatformUserApiService {
    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private UserApiService userApiService;

    public Organization getCurrentOrg() {
        User user = userApiService.getCurrentUser();
        Organization organization = organizationService.getOrCreatePersonalOrganization(user.getId());
        assert organization != null;
        return organization;
    }
}
