package co.sandai.zeus.api.favorite;

import co.sandai.zeus.api.asset.view.AssetView;
import co.sandai.zeus.api.favorite.view.AddFavoriteRequest;
import co.sandai.zeus.api.task.view.TaskView;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dto.AssetFilterParam;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.favorite.service.AssetFavoriteService;
import co.sandai.zeus.domain.favorite.service.TaskFavoriteService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.domain.user.dao.User;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/favorites")
public class FavoriteController {

    private final FavoriteApiService favoriteApiService;
    private final TaskFavoriteService taskFavoriteService;
    private final UserApiService userApiService;
    private final AssetService assetService;
    private final TaskService taskService;
    private final AssetFavoriteService assetFavoriteService;

    public FavoriteController(
            FavoriteApiService favoriteApiService,
            TaskFavoriteService taskFavoriteService,
            UserApiService userApiService,
            AssetService assetService,
            TaskService taskService,
            AssetFavoriteService assetFavoriteService) {
        this.favoriteApiService = favoriteApiService;
        this.taskFavoriteService = taskFavoriteService;
        this.userApiService = userApiService;
        this.assetService = assetService;
        this.taskService = taskService;
        this.assetFavoriteService = assetFavoriteService;
    }

    // 原有接口 - 获取收藏任务列表
    @Deprecated(since = "2025-02", forRemoval = false)
    @GetMapping("")
    @PrintLog
    public ListResponseByPage<TaskView> favoriteTasks(
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        return favoriteApiService.favoriteTasks(offset, limit);
    }

    // 原有接口 - 添加任务到收藏
    @Deprecated(since = "2025-02", forRemoval = false)
    @PostMapping("")
    @PrintLog
    public IDOnlyResponse addFavorite(@Valid @RequestBody AddFavoriteRequest request) {
        User user = userApiService.getCurrentUser();
        long id = taskFavoriteService.addFavoriteTask(user.getId(), request.taskId);
        return IDOnlyResponse.create(id);
    }

    @DeleteMapping("/tasks/{taskId}")
    @PrintLog
    public IDOnlyResponse deleteFavoriteTask(@PathVariable long taskId) {
        User user = userApiService.getCurrentUser();
        taskFavoriteService.deleteFavoriteTask(user.getId(), taskId);
        return IDOnlyResponse.create(taskId);
    }

    // 新接口 - 获取收藏任务列表（新路径）
    @GetMapping("/tasks")
    @PrintLog
    public ListResponseByPage<TaskView> getFavoriteTasks(
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        return favoriteApiService.favoriteTasks(offset, limit);
    }

    // 新接口 - 添加任务到收藏（保持与原有接口相同的请求格式）
    @PostMapping("/tasks/")
    @PrintLog
    public IDOnlyResponse addFavoriteTask(@RequestParam("taskId") long taskId) {
        User user = userApiService.getCurrentUser();
        Task task = taskService.getTaskById(taskId);
        if (task == null) {
            throw ZeusServiceException.notFound("Task not found");
        }
        long id = taskFavoriteService.addFavoriteTask(user.getId(), taskId);
        return IDOnlyResponse.create(id);
    }

    // 新增资产收藏相关接口
    @GetMapping("/assets")
    @PrintLog
    public ListResponseByPage<AssetView> getFavoriteAssets(
            @RequestParam(value = "types", defaultValue = "") AssetFilterParam.AssetFilterParamType[] types,
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        User user = userApiService.getCurrentUser();
        AssetFilterParam param = AssetFilterParam.builder().types(types).build();
        List<Asset> assets = assetFavoriteService.getUserFavoriteAssets(user.getId(), param, limit, offset);
        int total = assetFavoriteService.getFavoriteAssetsCount(user.getId(), param);
        List<AssetView> assetViews = AssetView.fromAssets(assets, user.getId(), false);
        assetViews.forEach(view -> view.setFavorite(true));
        return new ListResponseByPage<>(assetViews, total, offset, limit);
    }

    @PostMapping("/assets/")
    @PrintLog
    public IDOnlyResponse addFavoriteAsset(@RequestParam("assetId") long assetId) {
        User user = userApiService.getCurrentUser();
        Asset asset = assetService.getAssetById(assetId);
        if (asset == null) {
            throw ZeusServiceException.notFound("Asset not found");
        }

        long id = assetFavoriteService.addAssetFavorite(user.getId(), assetId, asset.getMediaType());
        return IDOnlyResponse.create(id);
    }

    @DeleteMapping("/assets/{assetId}")
    @PrintLog
    public IDOnlyResponse deleteFavoriteAsset(@PathVariable long assetId) {
        User user = userApiService.getCurrentUser();
        Asset asset = assetService.getAssetById(assetId);
        if (asset == null) {
            throw ZeusServiceException.notFound("Asset not found");
        }

        assetFavoriteService.removeAssetFavorite(user.getId(), assetId);
        return IDOnlyResponse.create(assetId);
    }
}
