package co.sandai.zeus.api.platform.credits;

import co.sandai.zeus.api.credit.service.CreditApiService;
import co.sandai.zeus.api.credit.view.UserCreditTransactionPageVo;
import co.sandai.zeus.api.credit.view.UserCreditVo;
import co.sandai.zeus.api.platform.PlatformUserApiService;
import co.sandai.zeus.api.platform.credits.view.PlatformOrganizationCreditVo;
import co.sandai.zeus.common.vo.ListResponseByPage;
import co.sandai.zeus.domain.credit.dao.UserCreditDO;
import co.sandai.zeus.domain.credit.dao.UserCreditDailyUsageDO;
import co.sandai.zeus.domain.credit.dao.UserCreditTransactionDO;
import co.sandai.zeus.domain.platform.credit.PlatformCreditService;
import co.sandai.zeus.domain.platform.credit.dto.PlatformCreditPackage;
import co.sandai.zeus.domain.platform.organization.dao.Organization;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/platform/credits")
public class PlatformCreditController {

    @Autowired
    private PlatformCreditService platformCreditService;

    @Autowired
    private PlatformUserApiService platformUserApiService;

    @Autowired
    private CreditApiService creditApiService;

    @GetMapping("/usage")
    public PlatformOrganizationCreditVo getPlatformCreditUsage() {
        Organization org = platformUserApiService.getCurrentOrg();
        List<UserCreditDO> userCreditDOs = platformCreditService.getOrgCredits(org.getId());
        UserCreditVo userCreditVo = creditApiService.populateUserCreditVo(userCreditDOs);
        List<UserCreditDailyUsageDO> usages = platformCreditService.getOrgUsage(org.getId(), 30);
        PlatformOrganizationCreditVo res = new PlatformOrganizationCreditVo();
        res.setUsage(usages)
                .setCreditAmount(userCreditVo.getCreditAmount())
                .setCreditDetailList(userCreditVo.getCreditDetailList());
        return res;
    }

    @GetMapping("/packages")
    public List<PlatformCreditPackage> getPlatformCreditPackages() {
        return platformCreditService.getPlatformCreditPackageConfig().getCreditPackages();
    }

    @GetMapping("/transactions")
    public ListResponseByPage<UserCreditTransactionPageVo.UserCreditTransactionVo> getPlatformCreditTransaction(
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        Organization org = platformUserApiService.getCurrentOrg();
        List<UserCreditTransactionDO> transactionDOList =
                platformCreditService.getOrgCreditTransactions(org.getId(), offset, limit);
        int total = platformCreditService.countOrgCreditTransactions(org.getId());
        List<UserCreditTransactionPageVo.UserCreditTransactionVo> list = transactionDOList.stream()
                .map(UserCreditTransactionPageVo.UserCreditTransactionVo::convertFromModel)
                .toList();
        return new ListResponseByPage<>(list, total, offset, limit);
    }
}
