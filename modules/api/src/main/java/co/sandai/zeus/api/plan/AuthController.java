package co.sandai.zeus.api.plan;

import co.sandai.zeus.api.plan.view.AuthVo;
import co.sandai.zeus.api.user.service.UserApiService;
import co.sandai.zeus.common.log.digest.PrintLog;
import co.sandai.zeus.domain.auth.service.AuthService;
import co.sandai.zeus.domain.plan.UserPlanAuthService;
import co.sandai.zeus.domain.user.dao.User;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/auth")
public class AuthController {

    @Resource
    private UserApiService userApiService;

    @Resource
    private UserPlanAuthService userPlanAuthService;

    @Resource
    private AuthService authService;

    @GetMapping("/user/get")
    @PrintLog
    // todo panhong 2025/4/14  这里要把白名单的逻辑也补上； 要不这里接一下配置中心？？
    public AuthVo queryUserAuth() {
        User currentUser = userApiService.getCurrentUser();
        long userId = currentUser.getId();
        List<String> codes = userPlanAuthService.queryAuthCodes(userId);
        AuthVo vo = new AuthVo();
        vo.setCodes(codes);

        return vo;
    }

    @PostMapping("/check")
    @PrintLog
    public CheckAuthVo checkAuth(@RequestParam String authCode) {
        Long userId = userApiService.getCurrentUserId();
        boolean b = authService.checkAuth(userId, authCode);

        return new CheckAuthVo().setResult(b);
    }
}
