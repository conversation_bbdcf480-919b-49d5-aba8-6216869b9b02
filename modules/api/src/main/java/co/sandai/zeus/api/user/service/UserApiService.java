package co.sandai.zeus.api.user.service;

import co.sandai.zeus.api.user.config.AuthenticationContext;
import co.sandai.zeus.api.user.view.*;
import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.principal.PrincipalContext;
import co.sandai.zeus.common.principal.PrincipalTypeEnum;
import co.sandai.zeus.config.SystemConfig;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.common.WhiteListService;
import co.sandai.zeus.domain.user.dao.*;
import co.sandai.zeus.domain.user.service.SessionService;
import co.sandai.zeus.domain.user.service.UserService;
import co.sandai.zeus.infra.captcha.CaptchaService;
import co.sandai.zeus.infra.stripe.StripeApiClient;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserApiService implements PrincipalContext {
    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private AssetService assetService;

    @Autowired
    private SessionService sessionService;

    @Autowired
    private CaptchaService captchaService;

    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Autowired
    private WhiteListService whiteListService;

    public User joinOAuthUser(String email, UserSource source, String nickname, String avatarUrl) {
        User user = new User();
        user.setEmail(email);
        user.setSource(source);
        user.setNickname(nickname);
        user.setPassword("");
        user.setEmailVerified(true);
        if (!userService.isInLoginWhiteList(user)) {
            throw new ZeusServiceException(
                    HttpStatus.FORBIDDEN,
                    ErrorCode.NotPermitted,
                    "Access to this system is currently restricted. Your account is not authorized to log in at this time. If you believe this is an error, please contact support for assistance.");
        }
        User insertedUser = userService.addUser(user);
        if (StringUtils.isNotBlank(avatarUrl)) {
            Asset avatar =
                    assetService.addAssetByWebUrl(avatarUrl, user.getId(), 0L, "jpeg", AssetSource.AvatarDownload);
            userService.updateUserAvatarId(user.getId(), avatar.getId());
        }
        log.info("new user User joined, email: {}, source: {}", user.getEmail(), source);
        return insertedUser;
    }

    public List<TempUserInfo> signupTempUsers(int count) {
        List<TempUserInfo> users = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            String randomSuffix = UUID.randomUUID().toString().substring(0, 6);
            String nickName = String.format("SU-%s", randomSuffix);
            String tempEmail = String.format("<EMAIL>", randomSuffix);
            String password = UUID.randomUUID().toString();
            User user = new User();
            user.setEmail(tempEmail);
            user.setSource(UserSource.TempBatchCreate);
            user.setNickname(nickName);
            user.setPassword(passwordEncoder.encode(password));
            user.setEmailVerified(true);
            userService.addUser(user);
            users.add(TempUserInfo.builder().email(tempEmail).password(password).build());
        }
        return users;
    }

    public void sendResetPasswordEmail(SendResetPasswordEmailRequest data) {
        String email = data.getEmail();
        User existedUser = userService.getUserByEmail(email);
        if (Objects.isNull(existedUser)) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "user not exist");
        }
        userService.sendResetPasswordEmail(existedUser);
    }

    public void resetPassword(UserResetPasswordRequest data) {
        String verifyCode = data.getCode();
        String email = userService.getResetPasswordEmailByCode(verifyCode);

        User user = userService.getUserByEmail(email);
        if (Objects.isNull(user)) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "user not found");
        }

        if (!data.getPassword().equals(data.getConfirmPassword())) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "password does not match confirm password");
        }

        userService.updateUserPassword(user.getId(), passwordEncoder.encode(data.getPassword()));
    }

    public UserProfile signupWithEmail(UserEmailSignupRequest userEmailSignupRequest, HttpServletResponse response) {
        if (systemConfig.isDisableEmailLogin()) {
            throw ZeusServiceException.forbidden("can not signup with email");
        }

        String email = userEmailSignupRequest.getEmail();
        String password = userEmailSignupRequest.getPassword();
        String confirmPassword = userEmailSignupRequest.getConfirmPassword();
        String nickname = userEmailSignupRequest.getDisplayName();

        checkBlacklist(email);

        checkCaptcha(userEmailSignupRequest.getCaptchaVerifyParam());

        if (StringUtils.isNotBlank(password)
                && StringUtils.isNotBlank(confirmPassword)
                && !password.equals(confirmPassword)) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "password and confirm password do not match");
        }

        User existedUser = userService.getUserByEmail(email);
        if (Objects.nonNull(existedUser)) {
            throw new ZeusServiceException(ErrorCode.InvalidParameters, "email already exists");
        }

        User user = new User();
        user.setEmail(email);
        user.setSource(UserSource.EmailSignup);
        user.setNickname(nickname);
        user.setPassword(passwordEncoder.encode(password));

        if (!userService.isInLoginWhiteList(user)) {
            throw new ZeusServiceException(
                    HttpStatus.FORBIDDEN,
                    ErrorCode.NotPermitted,
                    "Access to this system is currently restricted. Your account is not authorized to log in at this time. If you believe this is an error, please contact support for assistance.");
        }
        userService.addUser(user);
        log.info("new user User signup, email: {}, source: {}", user.getEmail(), UserSource.EmailSignup);

        userService.sendEmailVerificationEmail(user);

        return UserProfile.fromUser(user);
    }

    private void checkCaptcha(String captchaVerifyParam) {
        if (systemConfig.isDev()) return;
        if (!captchaService.checkCaptcha(captchaVerifyParam)) {
            throw new ZeusServiceException(
                    HttpStatus.UNPROCESSABLE_ENTITY, ErrorCode.NotPermitted, "captcha verify failed");
        }
    }

    private void checkBlacklist(String email) {
        Set<String> registerEmailSuffixBlackList = whiteListService.getAndRefresh("REGISTER_EMAIL_SUFFIX_BLACK_LIST");
        // 获取email后缀
        String emailSuffix = email.substring(email.indexOf("@") + 1);
        if (registerEmailSuffixBlackList.contains(emailSuffix)) {
            throw new ZeusServiceException(
                    HttpStatus.FORBIDDEN,
                    ErrorCode.NotPermitted,
                    "Access to this system is currently restricted. Your account is not authorized to log in at this time. If you believe this is an error, please contact support for assistance.");
        }
    }

    public UserProfile signInWithEmail(UserEmailSignInRequest userEmailSignInRequest, HttpServletResponse response) {
        String email = userEmailSignInRequest.getEmail();
        String password = userEmailSignInRequest.getPassword();

        if (systemConfig.isDisableEmailLogin()) {
            throw ZeusServiceException.forbidden("can not sign in with email");
        }

        User user = userService.getUserByEmail(email);

        checkBlacklist(email);

        checkCaptcha(userEmailSignInRequest.getCaptchaVerifyParam());

        if (Objects.isNull(user)) {
            throw ZeusServiceException.forbidden("user does not exist");
        }

        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw ZeusServiceException.forbidden("invalid email or password");
        }

        if (!userService.isInLoginWhiteList(user)) {
            throw new ZeusServiceException(
                    HttpStatus.FORBIDDEN,
                    ErrorCode.NotPermitted,
                    "Access to this system is currently restricted. Your account is not authorized to log in at this time. If you believe this is an error, please contact support for assistance.");
        }

        if (!user.isEmailVerified()) {
            sendVerifyEmail(user);
            throw new ZeusServiceException(
                    HttpStatus.FORBIDDEN,
                    ErrorCode.EmailNotVerified,
                    "A verification email has been sent to your address. Please check your inbox and verify your email to continue.");
        }

        Cookie cookie = signIn(user);
        response.addCookie(cookie);
        return UserProfile.fromUser(user);
    }

    public Cookie signIn(User user) {
        Session session = sessionService.saveSession(user, systemConfig.getExpireIn());
        Cookie cookie = new Cookie(systemConfig.getCookieName(), session.getToken());
        cookie.setHttpOnly(systemConfig.isHttpOnly());
        cookie.setMaxAge(systemConfig.getExpireIn());
        cookie.setPath("/");
        return cookie;
    }

    public UserProfile sendVerifyEmail() {
        User user = getCurrentUser();
        userService.sendEmailVerificationEmail(user);
        return UserProfile.fromUser(user);
    }

    public void sendVerifyEmail(User user) {
        userService.sendEmailVerificationEmail(user);
    }

    public UserProfile verifyEmail(UserEmailVerifyRequest userEmailVerifyRequest, HttpServletResponse response) {
        String verifyCode = userEmailVerifyRequest.getVerificationCode();
        User user = userService.verifyEmail(verifyCode);
        Cookie cookie = signIn(user);
        response.addCookie(cookie);
        return UserProfile.fromUser(user);
    }

    public long updateProfile(UserProfileUpdateRequest userProfileUpdateRequest) {
        User currentUser = getCurrentUser();
        if (Objects.isNull(userProfileUpdateRequest.getAvatarId())) {
            userProfileUpdateRequest.setAvatarId(currentUser.getAvatarId());
        }
        currentUser.setAvatarId(userProfileUpdateRequest.getAvatarId());
        currentUser.setDescription(userProfileUpdateRequest.getDescription());
        currentUser.setNickname(userProfileUpdateRequest.getNickname());
        userService.updateUser(currentUser);
        return currentUser.getId();
    }

    // OAuth2 authentication methods for manual OAuth2 implementation
    // Note: The joinOAuthUser method is already defined above as a private method

    public void updateUserAvatarIfNeeded(User user, String avatarUrl) {
        if (!Objects.isNull(user) && user.getAvatarId() == 0 && !Objects.isNull(avatarUrl)) {
            Asset avatar =
                    assetService.addAssetByWebUrl(avatarUrl, user.getId(), 0L, "jpeg", AssetSource.AvatarDownload);
            userService.updateUserAvatarId(user.getId(), avatar.getId());
            log.info("update user: {} avatar to: {}", user.getId(), avatar.getId());
        }
    }

    public User getCurrentUser() {
        User user = AuthenticationContext.getCurrentUser();
        if (user == null) {
            return null;
        }
        checkBlacklist(user.getEmail());
        return user;
    }

    public Long getCurrentUserId() {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return null;
        } else {
            return currentUser.getId();
        }
    }

    public User createOuterCustomer(User user) throws StripeException {
        if (StringUtils.isNotBlank(user.getOutCustomerId())) {
            // 已经有了，不需要创建
            return user;
        }
        Customer outCustomer = StripeApiClient.createOutCustomer(user.getNickname(), user.getEmail());
        user.setOutCustomerId(outCustomer.getId());
        user.setOutCustomerType(OutCustomerTypeEnum.STRIPE.name());

        userMapper.updateOutCustomer(user.getId(), user.getOutCustomerId(), user.getOutCustomerType());
        return user;
    }

    @Override
    public Long getPrincipalId() {
        return this.getCurrentUserId();
    }

    @Override
    public PrincipalTypeEnum getPrincipalType() {
        return PrincipalTypeEnum.User;
    }
}
