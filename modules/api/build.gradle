tasks.named('test') {
    useJUnitPlatform()
}

configurations.configureEach {
    exclude group: 'ch.qos.logback'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    exclude group: 'log4j', module: 'log4j' // 排除 Log4j 1.x
    exclude group: 'commons-logging', module: 'commons-logging'
}

dependencies {
    implementation project(':modules:config')
    implementation project(':modules:domain')
    implementation project(':modules:common')
    implementation project(':modules:infer')
    implementation project(':modules:infra')

    // Add Lombok explicitly
    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'

    // Add Redisson dependencies
    implementation 'org.redisson:redisson-spring-boot-starter:3.40.0'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    // Add Jackson JSR310 module for Java 8 date/time support
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // Spring Security Core for password encoding (without web security)
    implementation 'org.springframework.security:spring-security-core'


    implementation 'org.springframework.boot:spring-boot-starter-validation:3.3.4'
    implementation ('org.springframework.boot:spring-boot-starter-web') {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    // Add DevTools for hot reload capabilities
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    implementation 'org.apache.commons:commons-lang3:3.17.0'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0'
    implementation ('org.springframework.boot:spring-boot-starter-log4j2') {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    }
    // Add log4j2 dependencies explicitly
    implementation 'org.apache.logging.log4j:log4j-api:2.23.1'
    implementation 'org.apache.logging.log4j:log4j-core:2.23.1'
    // Make sure slf4j bindings are consistent - use only slf4j-api without implementation
    implementation 'org.slf4j:slf4j-api'
    // Removed log4j-slf4j-impl to avoid conflicts

    implementation "com.stripe:stripe-java:28.0.0"

    // 各种工具
    implementation 'com.alibaba:fastjson:1.2.79'

    // 中间件依赖
    implementation('com.aliyun.schedulerx:schedulerx2-spring-boot-starter:1.12.2') {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-api'
        exclude group: 'org.apache.logging.log4j', module: 'log4j-core'
        exclude group: 'log4j', module: 'log4j'
    }
    implementation 'org.springframework.boot:spring-boot-starter-aop'

    implementation 'org.springframework.boot:spring-boot-starter-actuator'
}