package co.sandai.zeus.openapi.config;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import co.sandai.zeus.openapi.annotation.CodeSample;
import co.sandai.zeus.openapi.annotation.CodeSamples;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.Paths;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

class OpenApiCodeSamplesCustomizerTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private RequestMappingHandlerMapping handlerMapping;

    private OpenApiCodeSamplesCustomizer customizer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        customizer = new OpenApiCodeSamplesCustomizer(applicationContext);
    }

    @Test
    void testCustomizeAddsCodeSamples() throws Exception {
        // Create a test method with @CodeSamples annotation
        Method testMethod = TestController.class.getMethod("testMethod");

        // Mock the handler mapping
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = new HashMap<>();
        HandlerMethod handlerMethod = new HandlerMethod(new TestController(), testMethod);
        RequestMappingInfo mappingInfo = RequestMappingInfo.paths("/test").build();
        handlerMethods.put(mappingInfo, handlerMethod);

        when(applicationContext.getBean(RequestMappingHandlerMapping.class)).thenReturn(handlerMapping);
        when(handlerMapping.getHandlerMethods()).thenReturn(handlerMethods);

        // Create OpenAPI with a test operation
        OpenAPI openAPI = new OpenAPI();
        Paths paths = new Paths();
        PathItem pathItem = new PathItem();
        Operation operation = new Operation();
        operation.setSummary("Test operation");
        pathItem.setPost(operation);
        paths.addPathItem("/test", pathItem);
        openAPI.setPaths(paths);

        // Apply customization
        customizer.customise(openAPI);

        // Verify x-codeSamples extension was added
        assertNotNull(operation.getExtensions());
        assertTrue(operation.getExtensions().containsKey("x-codeSamples"));

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> codeSamples =
                (List<Map<String, Object>>) operation.getExtensions().get("x-codeSamples");
        assertEquals(1, codeSamples.size());

        Map<String, Object> sample = codeSamples.get(0);
        assertEquals("curl", sample.get("lang"));
        assertEquals("cURL", sample.get("label"));
        assertEquals("curl -X POST /test", sample.get("source"));
    }

    // Test controller class with @CodeSamples annotation
    static class TestController {
        @CodeSamples({@CodeSample(lang = "curl", label = "cURL", source = "curl -X POST /test")})
        @io.swagger.v3.oas.annotations.Operation(summary = "Test operation")
        public void testMethod() {
            // Test method
        }
    }
}
