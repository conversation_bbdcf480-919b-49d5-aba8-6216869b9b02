package co.sandai.zeus.openapi.common;

import java.util.Objects;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

public class RequestContext {

    public static void setAttribute(String key, Object value) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            attributes.setAttribute(key, value, RequestAttributes.SCOPE_REQUEST);
        }
    }

    public static Object getAttribute(String key) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getAttribute(key, RequestAttributes.SCOPE_REQUEST);
        }
        return null;
    }

    public static void setOrganizationId(Long organizationId) {
        setAttribute("organizationId", organizationId);
    }

    public static Long getOrganizationId() {
        Long orgId = (Long) getAttribute("organizationId");
        assert Objects.nonNull(orgId);
        return orgId;
    }
}
