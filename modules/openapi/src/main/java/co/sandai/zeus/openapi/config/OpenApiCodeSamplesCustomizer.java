package co.sandai.zeus.openapi.config;

import co.sandai.zeus.openapi.annotation.CodeSample;
import co.sandai.zeus.openapi.annotation.CodeSamples;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import java.lang.reflect.Method;
import java.util.*;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

/**
 * OpenAPI customizer that processes @CodeSamples annotations and adds x-codeSamples extensions
 * to the OpenAPI specification.
 */
@Component
public class OpenApiCodeSamplesCustomizer implements OpenApiCustomizer {

    private final ApplicationContext applicationContext;

    public OpenApiCodeSamplesCustomizer(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void customise(OpenAPI openApi) {
        if (openApi.getPaths() == null) {
            return;
        }

        // Get all request mappings to find methods with @CodeSamples annotations
        Map<Method, RequestMappingInfo> methodMappings = getMethodMappings();

        // Process each path and operation
        openApi.getPaths().forEach((path, pathItem) -> {
            processPathItem(pathItem, methodMappings);
        });
    }

    private Map<Method, RequestMappingInfo> getMethodMappings() {
        Map<Method, RequestMappingInfo> methodMappings = new HashMap<>();

        RequestMappingHandlerMapping handlerMapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = handlerMapping.getHandlerMethods();

        handlerMethods.forEach((mappingInfo, handlerMethod) -> {
            methodMappings.put(handlerMethod.getMethod(), mappingInfo);
        });

        return methodMappings;
    }

    private void processPathItem(PathItem pathItem, Map<Method, RequestMappingInfo> methodMappings) {
        processOperation(pathItem.getGet(), methodMappings);
        processOperation(pathItem.getPost(), methodMappings);
        processOperation(pathItem.getPut(), methodMappings);
        processOperation(pathItem.getDelete(), methodMappings);
        processOperation(pathItem.getPatch(), methodMappings);
        processOperation(pathItem.getHead(), methodMappings);
        processOperation(pathItem.getOptions(), methodMappings);
        processOperation(pathItem.getTrace(), methodMappings);
    }

    private void processOperation(Operation operation, Map<Method, RequestMappingInfo> methodMappings) {
        if (operation == null) {
            return;
        }

        // Find the method that corresponds to this operation
        Method method = findMethodForOperation(operation, methodMappings);
        if (method == null) {
            return;
        }

        // Check if the method has @CodeSamples annotation
        CodeSamples codeSamples = method.getAnnotation(CodeSamples.class);
        if (codeSamples == null) {
            return;
        }

        // Convert @CodeSamples to x-codeSamples extension
        List<Map<String, Object>> codeSamplesList = new ArrayList<>();
        for (CodeSample sample : codeSamples.value()) {
            Map<String, Object> sampleMap = new HashMap<>();
            sampleMap.put("lang", sample.lang());
            sampleMap.put("label", sample.label());
            sampleMap.put("source", sample.source());
            codeSamplesList.add(sampleMap);
        }

        // Add x-codeSamples extension to the operation
        if (operation.getExtensions() == null) {
            operation.setExtensions(new HashMap<>());
        }
        operation.getExtensions().put("x-codeSamples", codeSamplesList);
    }

    private Method findMethodForOperation(Operation operation, Map<Method, RequestMappingInfo> methodMappings) {
        String operationId = operation.getOperationId();
        String summary = operation.getSummary();

        for (Map.Entry<Method, RequestMappingInfo> entry : methodMappings.entrySet()) {
            Method method = entry.getKey();

            // Try to match by operation ID first
            if (method.getName().equals(operationId)) {
                return method;
            }

            // Try to match by summary
            if (summary != null) {
                io.swagger.v3.oas.annotations.Operation opAnnotation =
                        method.getAnnotation(io.swagger.v3.oas.annotations.Operation.class);
                if (opAnnotation != null && summary.equals(opAnnotation.summary())) {
                    return method;
                }
            }

            // Try to match by method name patterns
            if (operationId != null) {
                // Convert camelCase operationId to method name patterns
                String methodName = method.getName();
                if (operationId.toLowerCase().contains(methodName.toLowerCase())
                        || methodName.toLowerCase().contains(operationId.toLowerCase())) {
                    return method;
                }
            }
        }

        return null;
    }
}
