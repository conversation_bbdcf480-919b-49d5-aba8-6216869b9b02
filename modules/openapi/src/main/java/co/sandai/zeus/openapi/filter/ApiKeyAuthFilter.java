package co.sandai.zeus.openapi.filter;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.vo.ErrorResponse;
import co.sandai.zeus.openapi.common.RequestContext;
import co.sandai.zeus.openapi.service.apikey.OpenApiKeyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;
import org.springframework.web.filter.OncePerRequestFilter;

public class ApiKeyAuthFilter extends OncePerRequestFilter {
    private final OpenApiKeyService openApiKeyService;

    public ApiKeyAuthFilter(OpenApiKeyService openApiKeyService) {
        this.openApiKeyService = openApiKeyService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String authHeader = request.getHeader("Authorization");

        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String apiKey = authHeader.substring(7); // 提取 API Key
            Long orgId = openApiKeyService.validateApiKey(apiKey);

            if (Objects.nonNull(orgId)) {
                RequestContext.setOrganizationId(orgId);
                filterChain.doFilter(request, response);
                return;
            }
        }

        ObjectMapper objectMapper = new ObjectMapper();

        ErrorResponse error = ErrorResponse.builder().code(ErrorCode.NotLogin).build();
        error.setMessage("Unauthorized");
        String jsonString = objectMapper.writeValueAsString(error);

        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(jsonString);
    }
}
