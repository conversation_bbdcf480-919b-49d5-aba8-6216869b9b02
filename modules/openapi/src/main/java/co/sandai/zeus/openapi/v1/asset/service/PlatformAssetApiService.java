package co.sandai.zeus.openapi.v1.asset.service;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.openapi.common.RateLimitHelper;
import org.springframework.stereotype.Service;

@Service
public class PlatformAssetApiService {

    private final AssetService assetService;
    private final RateLimitHelper rateLimitHelper;

    public PlatformAssetApiService(AssetService assetService, RateLimitHelper rateLimitHelper) {
        this.assetService = assetService;
        this.rateLimitHelper = rateLimitHelper;
    }

    public Asset getAssetById(Long id) {
        Long orgId = rateLimitHelper.checkRateLimit("get-asset");
        Asset asset = assetService.getAssetById(id);
        if (asset == null) {
            throw ZeusServiceException.notFound("Asset not found");
        }

        if (asset.getOrgId() != orgId) {
            throw ZeusServiceException.forbidden("You have no permission to access this asset");
        }
        return asset;
    }
}
