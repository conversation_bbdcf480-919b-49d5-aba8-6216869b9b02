package co.sandai.zeus.openapi;

import co.sandai.zeus.openapi.filter.ApiKeyAuthFilter;
import co.sandai.zeus.openapi.service.apikey.OpenApiKeyService;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"co.sandai.zeus"})
@EnableScheduling
@ServletComponentScan
@OpenAPIDefinition(
        info = @Info(title = "Magi OpenAPI", version = "1.0", description = "API Documentation for Magi"),
        security = @SecurityRequirement(name = "api-key"))
@SecurityScheme(
        name = "api-key",
        type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER,
        bearerFormat = "bearer",
        paramName = "Authorization")
public class ZeusOpenApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZeusOpenApiApplication.class, args);
    }

    @Autowired
    OpenApiKeyService openApiKeyService;

    @Bean
    public FilterRegistrationBean<ApiKeyAuthFilter> apiKeyAuthFilter() {
        FilterRegistrationBean<ApiKeyAuthFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ApiKeyAuthFilter(openApiKeyService));
        registrationBean.addUrlPatterns("/v1/*"); // 指定需要过滤的 URL 模式
        return registrationBean;
    }
}
