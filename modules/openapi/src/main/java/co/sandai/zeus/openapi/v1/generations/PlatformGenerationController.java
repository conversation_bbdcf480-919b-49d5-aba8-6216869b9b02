package co.sandai.zeus.openapi.v1.generations;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.domain.task.dao.Task;
import co.sandai.zeus.domain.task.dao.TaskChunkDO;
import co.sandai.zeus.domain.task.dao.TaskExtraInferArgs;
import co.sandai.zeus.domain.task.dao.TaskSourceEnum;
import co.sandai.zeus.domain.task.service.GenerationService;
import co.sandai.zeus.domain.task.service.TaskService;
import co.sandai.zeus.infra.IDGenerator;
import co.sandai.zeus.openapi.annotation.CodeSample;
import co.sandai.zeus.openapi.annotation.CodeSamples;
import co.sandai.zeus.openapi.common.RateLimitHelper;
import co.sandai.zeus.openapi.common.RequestContext;
import co.sandai.zeus.openapi.v1.generations.view.GenerationRequestVo;
import co.sandai.zeus.openapi.v1.generations.view.GenerationResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@Tag(name = "Generations")
@RestController
@RequestMapping("/v1/generations")
public class PlatformGenerationController {

    private final AssetService assetService;
    private final IDGenerator iDGenerator;
    private final GenerationService generationService;
    private final TaskService taskService;
    private final RateLimitHelper rateLimitHelper;

    @Value("${zeus.model:magi-v2-distill-fp8}")
    private String model;

    public PlatformGenerationController(
            AssetService assetService,
            IDGenerator iDGenerator,
            GenerationService generationService,
            TaskService taskService,
            RateLimitHelper rateLimitHelper) {
        this.assetService = assetService;
        this.iDGenerator = iDGenerator;
        this.generationService = generationService;
        this.taskService = taskService;
        this.rateLimitHelper = rateLimitHelper;
    }

    @Operation(summary = "Submit video generation")
    @CodeSamples({
        @CodeSample(
                lang = "curl",
                label = "cURL",
                source =
                        """
                curl -X POST "https://api.sand.ai/v1/generations" \\
                  -H "Authorization: Bearer YOUR_API_KEY" \\
                  -H "Content-Type: application/json" \\
                  -d '{
                    "seed": 12345,
                    "aspectRatio": "16:9",
                    "source": {
                      "type": "asset",
                      "content": 123
                    },
                    "chunks": [{
                      "duration": 5,
                      "conditions": [{
                        "type": "text",
                        "content": "A beautiful sunset over mountains"
                      }],
                      "enablePromptEnhancement": true
                    }]
                  }'
                """),
        @CodeSample(
                lang = "javascript",
                label = "JavaScript",
                source =
                        """
                const requestBody = {
                  seed: 12345,
                  aspectRatio: "16:9",
                  source: {
                    type: "asset",
                    content: 123
                  },
                  chunks: [{
                    duration: 5,
                    conditions: [{
                      type: "text",
                      content: "A beautiful sunset over mountains"
                    }],
                    enablePromptEnhancement: true
                  }]
                };

                fetch('https://api.sand.ai/v1/generations', {
                  method: 'POST',
                  headers: {
                    'Authorization': 'Bearer YOUR_API_KEY',
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(requestBody)
                })
                .then(response => response.json())
                .then(data => console.log(data));
                """),
        @CodeSample(
                lang = "python",
                label = "Python",
                source =
                        """
                import requests
                import json

                url = "https://api.sand.ai/v1/generations"
                headers = {
                    "Authorization": "Bearer YOUR_API_KEY",
                    "Content-Type": "application/json"
                }
                data = {
                    "seed": 12345,
                    "aspectRatio": "16:9",
                    "source": {
                        "type": "asset",
                        "content": 123
                    },
                    "chunks": [{
                        "duration": 5,
                        "conditions": [{
                            "type": "text",
                            "content": "A beautiful sunset over mountains"
                        }],
                        "enablePromptEnhancement": True
                    }]
                }

                response = requests.post(url, headers=headers, json=data)
                print(response.json())
                """)
    })
    @PostMapping
    public IDOnlyResponse generate(@RequestBody @Valid GenerationRequestVo request) throws IOException {
        rateLimitHelper.checkRateLimit("submit-generation");
        request.validate();
        Task task = populateTask(request);

        GenerationRequestVo.Condition source = request.getSource();

        // TODO: support external url
        Asset asset = assetService.getAssetById((Long) source.getContent());
        if (asset == null) {
            throw ZeusServiceException.notFound("Sources Asset not found");
        }

        task.checkParams(asset);
        String queue = "normal";
        String enhancementType = "";
        List<Asset> sourceAssets = List.of(asset);
        List<TaskChunkDO> outputChunks = request.getTaskChunks(iDGenerator);
        generationService.submitGeneration(
                null, task.getOrgId(), enhancementType, task, sourceAssets, outputChunks, queue, true);

        return IDOnlyResponse.create(task.getId());
    }

    @Operation(
            summary = "Get video generation status",
            description = "Get the status and the result video of a video generation task.")
    @CodeSamples({
        @CodeSample(
                lang = "curl",
                label = "cURL",
                source =
                        """
                curl -X GET "https://api.sand.ai/v1/generations/123" \\
                  -H "Authorization: Bearer YOUR_API_KEY"
                """),
        @CodeSample(
                lang = "javascript",
                label = "JavaScript",
                source =
                        """
                fetch('https://api.sand.ai/v1/generations/123', {
                  method: 'GET',
                  headers: {
                    'Authorization': 'Bearer YOUR_API_KEY'
                  }
                })
                .then(response => response.json())
                .then(data => console.log(data));
                """),
        @CodeSample(
                lang = "python",
                label = "Python",
                source =
                        """
                import requests

                url = "https://api.sand.ai/v1/generations/123"
                headers = {
                    "Authorization": "Bearer YOUR_API_KEY"
                }

                response = requests.get(url, headers=headers)
                print(response.json())
                """)
    })
    @GetMapping("/{id}")
    public GenerationResponseVo getGenerationDetail(@PathVariable long id) {
        rateLimitHelper.checkRateLimit("get-generation-detail");
        Task task = taskService.getTaskById(id);
        if (task == null) {
            throw ZeusServiceException.notFound("Task not found");
        }

        GenerationResponseVo response = new GenerationResponseVo();
        response.setId(id);
        response.setStatus(task.getStatus());
        response.setCreateTime(task.getCreateTime());
        response.setResultVideoId(task.getResultVideoId());

        Asset resultVideoAsset = null;
        if (task.getResultVideoId() > 0) {
            resultVideoAsset = assetService.getAssetById(task.getResultVideoId());
        }

        if (Objects.nonNull(resultVideoAsset)) {
            response.setResultVideoURL(assetService.getAssetPublicUrl(resultVideoAsset, 60));
        }

        return response;
    }

    private Task populateTask(GenerationRequestVo request) {
        TaskExtraInferArgs extraInferArgs = new TaskExtraInferArgs();
        extraInferArgs.setEnableWatermark(false);
        extraInferArgs.setResolution("720p");
        extraInferArgs.setVaeModel("2x");
        extraInferArgs.setNSampleSteps(32);

        Task task = new Task();
        task.setType(request.getTaskType());
        task.setExtraInferArgs(extraInferArgs);
        task.setModel(model);
        task.setAspectRatio(request.getAspectRatio());
        task.setSeed(request.getSeed());
        task.setTSchedulerFunc("sd3");
        task.setTSchedulerArgs("");
        task.setUserId(0L);
        task.setOrgId(RequestContext.getOrganizationId());
        task.setEnablePromptEnhancement(request.isPeEnabled());
        task.setTaskSource(TaskSourceEnum.API_PLATFORM);
        task.setPrompt("");
        return task;
    }
}
