package co.sandai.zeus.openapi.annotation;

/**
 * Represents a single code sample for API documentation.
 * Used within the @CodeSamples annotation to define individual code examples.
 */
public @interface CodeSample {
    /**
     * The programming language of the code sample (e.g., "curl", "javascript", "python", "java")
     */
    String lang();

    /**
     * A human-readable label for the code sample (e.g., "cURL", "JavaScript", "Python")
     */
    String label();

    /**
     * The actual source code for the sample
     */
    String source();
}
