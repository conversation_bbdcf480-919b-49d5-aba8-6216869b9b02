package co.sandai.zeus.openapi.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to define code samples for OpenAPI operations.
 * This will be converted to x-codeSamples extension in the OpenAPI specification.
 * Usage:
 * @CodeSamples({
 *     @CodeSample(lang = "curl", label = "cURL", source = "curl -X POST ..."),
 *     @CodeSample(lang = "javascript", label = "JavaScript", source = "fetch('/api/endpoint', ...)")
 * })
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CodeSamples {
    /**
     * Array of code samples for this operation
     */
    CodeSample[] value();
}
