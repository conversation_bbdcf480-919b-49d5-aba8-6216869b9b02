package co.sandai.zeus.openapi.service.apikey;

import co.sandai.zeus.common.principal.PrincipalContext;
import co.sandai.zeus.common.principal.PrincipalTypeEnum;
import co.sandai.zeus.domain.platform.apikey.ApiKeyService;
import co.sandai.zeus.domain.platform.apikey.dao.ApiKey;
import java.util.Objects;
import org.springframework.stereotype.Service;

@Service
public class OpenApiKeyService implements PrincipalContext {
    private final ApiKeyService apiKeyService;

    public OpenApiKeyService(ApiKeyService apiKeyService) {
        this.apiKeyService = apiKeyService;
    }

    @Override
    public Long getPrincipalId() {
        return 0L;
    }

    @Override
    public PrincipalTypeEnum getPrincipalType() {
        return PrincipalTypeEnum.ApiKey;
    }

    public Long validateApiKey(String apiKeyValue) {
        ApiKey apiKey = apiKeyService.getApiKeyByValue(apiKeyValue);
        if (Objects.isNull(apiKey)) {
            return null;
        }
        return apiKey.getOrgId();
    }
}
