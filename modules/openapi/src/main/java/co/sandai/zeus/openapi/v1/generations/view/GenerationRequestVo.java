package co.sandai.zeus.openapi.v1.generations.view;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.domain.task.dao.*;
import co.sandai.zeus.infra.IDGenerator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class GenerationRequestVo {
    private long seed;
    private String aspectRatio;

    @NotNull
    private Condition source;

    @NotNull
    private List<Chunk> chunks;

    @Data
    public static class Chunk {
        private int duration;
        private List<Condition> conditions;
        private boolean enablePromptEnhancement;
    }

    @Data
    public static class Condition {
        String type;
        Object content;
    }

    public void validate() {
        if (chunks.size() != 1) {
            throw ZeusServiceException.badRequest("chunks size must be 1");
        }
    }

    @JsonIgnore
    public boolean isPeEnabled() {
        return chunks.getFirst().enablePromptEnhancement;
    }

    @JsonIgnore
    public TaskType getTaskType() {
        if (source.type.equals("image")) {
            return TaskType.I2V;
        }
        if (source.type.equals("video")) {
            return TaskType.ExtendDuration;
        }
        throw ZeusServiceException.badRequest("source.type can only be 'image' or 'video'");
    }

    @JsonIgnore
    public List<TaskChunkDO> getTaskChunks(IDGenerator idGenerator) {
        List<TaskChunkDO> outputChunks = new ArrayList<>();
        int chunkIndex = 0;
        for (Chunk chunkVO : chunks) {
            TaskChunkDO taskChunkDO = new TaskChunkDO();
            taskChunkDO.setId(idGenerator.getNextId());
            for (GenerationRequestVo.Condition condition : chunkVO.getConditions()) {
                if ("text".equalsIgnoreCase(condition.getType())) {
                    taskChunkDO.setPrompt((String) condition.getContent());
                    break;
                }
            }
            taskChunkDO.setDuration((float) chunkVO.getDuration());
            taskChunkDO.setIndex(chunkIndex++);
            taskChunkDO.setStatus(TaskChunkStatus.Pending);
            outputChunks.add(taskChunkDO);
        }
        return outputChunks;
    }
}
