package co.sandai.zeus.openapi.common;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.infra.RateLimiter;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class RateLimitHelper {
    private final RateLimiter rateLimiter;

    @Value("${zeus.openapi.rate-limit:20}")
    private int rateLimit;

    /**
     * The time window in milliseconds used for rate limiting.
     * This value defines the duration over which the rate limit is applied.
     * By default, it is set to 60,000 milliseconds (60 seconds).
     */
    private int rateLimitWindow = 60 * 1000; // 60s

    /**
     * Checks the rate limit for a specific API based on the organization ID and API name.
     * Constructs a unique key using the API name and organization ID, then verifies if the request
     * is allowed within the defined rate limit and time window. If the rate limit is exceeded,
     * a {@code ZeusServiceException} with a "Too Many Requests" status is thrown.
     *
     * @param apiName the name of the API being checked for rate limiting
     * @return the organization ID retrieved from the request context
     * @throws ZeusServiceException if the rate limit for the given API and organization is exceeded
     */
    public Long checkRateLimit(String apiName) {
        Long orgId = RequestContext.getOrganizationId();
        String key = String.format("%s-%d", apiName, orgId);
        if (!rateLimiter.isAllowed(key, rateLimit, rateLimitWindow)) {
            throw ZeusServiceException.tooManyRequests(rateLimit);
        }
        return orgId;
    }
}
