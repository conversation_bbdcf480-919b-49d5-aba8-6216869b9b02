package co.sandai.zeus.openapi.v1.asset.view;

import co.sandai.zeus.config.SpringContext;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.service.AssetService;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Builder
@Getter
@Setter
@Slf4j
public class PlatformAssetView {
    public long id;
    public String url;

    public static PlatformAssetView fromAsset(Asset asset) {
        if (asset == null) {
            return null;
        }
        AssetService assetService = SpringContext.getBean(AssetService.class);
        return PlatformAssetView.builder()
                .id(asset.id)
                .url(assetService.getAssetPublicUrl(asset, 60))
                .build();
    }
}
