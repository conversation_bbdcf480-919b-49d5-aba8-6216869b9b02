package co.sandai.zeus.openapi.v1.asset;

import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.helper.FilePathHelper;
import co.sandai.zeus.common.vo.IDOnlyResponse;
import co.sandai.zeus.domain.asset.dao.Asset;
import co.sandai.zeus.domain.asset.dao.AssetSource;
import co.sandai.zeus.domain.asset.service.AssetService;
import co.sandai.zeus.openapi.annotation.CodeSample;
import co.sandai.zeus.openapi.annotation.CodeSamples;
import co.sandai.zeus.openapi.common.RateLimitHelper;
import co.sandai.zeus.openapi.v1.asset.service.PlatformAssetApiService;
import co.sandai.zeus.openapi.v1.asset.view.PlatformAssetView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.io.InputStream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "Assets")
@RestController
@RequestMapping("/v1/assets")
public class PlatformAssetController {
    @Autowired
    private AssetService assetService;

    @Autowired
    private PlatformAssetApiService platformAssetApiService;

    @Autowired
    private RateLimitHelper rateLimitHelper;

    @Operation(
            summary = "Upload asset",
            description = "Upload an asset that can be used for video generation or video extension.")
    @CodeSamples({
        @CodeSample(
                lang = "curl",
                label = "cURL",
                source =
                        """
                curl -X POST "https://api.sand.ai/v1/assets" \\
                  -H "Authorization: Bearer YOUR_API_KEY" \\
                  -H "Content-Type: multipart/form-data" \\
                  -F "file=@/path/to/your/video.mp4"
                """),
        @CodeSample(
                lang = "javascript",
                label = "JavaScript",
                source =
                        """
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);

                fetch('https://api.sand.ai/v1/assets', {
                  method: 'POST',
                  headers: {
                    'Authorization': 'Bearer YOUR_API_KEY'
                  },
                  body: formData
                })
                .then(response => response.json())
                .then(data => console.log(data));
                """),
        @CodeSample(
                lang = "python",
                label = "Python",
                source =
                        """
                import requests

                url = "https://api.sand.ai/v1/assets"
                headers = {
                    "Authorization": "Bearer YOUR_API_KEY"
                }
                files = {
                    "file": open("/path/to/your/video.mp4", "rb")
                }

                response = requests.post(url, headers=headers, files=files)
                print(response.json())
                """)
    })
    @PostMapping("")
    public IDOnlyResponse uploadAsset(@RequestParam("file") MultipartFile file) throws IOException {
        Long orgId = rateLimitHelper.checkRateLimit("upload-asset");
        String originalFilename = file.getOriginalFilename();
        String ext = null;
        if (!StringUtils.isBlank(originalFilename)) {
            ext = FilePathHelper.getFileExtName(originalFilename);
        }

        if (ext == null) {
            throw ZeusServiceException.badRequest("invalid filename");
        }

        InputStream inputStream = file.getInputStream();
        byte[] data = inputStream.readAllBytes();
        Asset asset = assetService.addUploadAsset(data, 0L, orgId, ext, file.getOriginalFilename(), AssetSource.Upload);
        return IDOnlyResponse.create(asset.getId());
    }

    @Operation(summary = "Delete asset")
    @CodeSamples({
        @CodeSample(
                lang = "curl",
                label = "cURL",
                source =
                        """
                curl -X DELETE "https://api.sand.ai/v1/assets/123" \\
                  -H "Authorization: Bearer YOUR_API_KEY"
                """),
        @CodeSample(
                lang = "javascript",
                label = "JavaScript",
                source =
                        """
                fetch('https://api.sand.ai/v1/assets/123', {
                  method: 'DELETE',
                  headers: {
                    'Authorization': 'Bearer YOUR_API_KEY'
                  }
                })
                .then(response => response.json())
                .then(data => console.log(data));
                """),
        @CodeSample(
                lang = "python",
                label = "Python",
                source =
                        """
                import requests

                url = "https://api.sand.ai/v1/assets/123"
                headers = {
                    "Authorization": "Bearer YOUR_API_KEY"
                }

                response = requests.delete(url, headers=headers)
                print(response.json())
                """)
    })
    @DeleteMapping("/{id}")
    public IDOnlyResponse deleteAssetById(@PathVariable("id") Long id) {
        Asset asset = platformAssetApiService.getAssetById(id);
        assetService.deleteAssetById(asset.getId());
        return IDOnlyResponse.create(id);
    }

    @Operation(summary = "Get asset info")
    @CodeSamples({
        @CodeSample(
                lang = "curl",
                label = "cURL",
                source =
                        """
                curl -X GET "https://api.sand.ai/v1/assets/123" \\
                  -H "Authorization: Bearer YOUR_API_KEY"
                """),
        @CodeSample(
                lang = "javascript",
                label = "JavaScript",
                source =
                        """
                fetch('https://api.sand.ai/v1/assets/123', {
                  method: 'GET',
                  headers: {
                    'Authorization': 'Bearer YOUR_API_KEY'
                  }
                })
                .then(response => response.json())
                .then(data => console.log(data));
                """),
        @CodeSample(
                lang = "python",
                label = "Python",
                source =
                        """
                import requests

                url = "https://api.sand.ai/v1/assets/123"
                headers = {
                    "Authorization": "Bearer YOUR_API_KEY"
                }

                response = requests.get(url, headers=headers)
                print(response.json())
                """)
    })
    @GetMapping("/{id}")
    public PlatformAssetView getAssetById(@PathVariable("id") Long id) {
        Asset asset = platformAssetApiService.getAssetById(id);
        return PlatformAssetView.fromAsset(asset);
    }
}
